using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace StockCrawler
{
    /// <summary>
    /// ProfitScreenshotWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ProfitScreenshotWindow : Window
    {
        private readonly string _profitValue;
        private readonly SolidColorBrush _profitBackground;
        private readonly SolidColorBrush _profitBorder;

        private readonly bool _isProfit;

        public ProfitScreenshotWindow(string profitValue, bool isProfit)
        {
            InitializeComponent();

            _profitValue = profitValue;
            _isProfit = isProfit;

            // 根据盈亏设置颜色
            if (isProfit)
            {
                // 盈利为红色字体和粉红背景边框
                _profitBackground = new SolidColorBrush(Color.FromRgb(255, 241, 240)); // #FFF1F0 - 粉红背景
                _profitBorder = new SolidColorBrush(Color.FromRgb(255, 163, 158)); // #FFA39E - 粉红边框
            }
            else
            {
                // 亏损为绿色背景和白色字体
                _profitBackground = new SolidColorBrush(Color.FromRgb(82, 196, 26)); // #52C41A - 深绿色背景
                _profitBorder = new SolidColorBrush(Color.FromRgb(56, 158, 13)); // #389E0D - 深绿色边框
            }
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 设置盈亏值和颜色
            ProfitValueTextBlock.Text = _profitValue;
            ProfitInfoBorder.Background = _profitBackground;
            ProfitInfoBorder.BorderBrush = _profitBorder;

            // 设置文本颜色
            if (_isProfit)
            {
                // 盈利为红色字体
                ProfitValueTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(245, 34, 45)); // #F5222D - 红色字体
            }
            else
            {
                // 亏损为白色字体
                ProfitValueTextBlock.Foreground = Brushes.White;
                // 亏损时，"当日总盈亏:"文本也设为白色
                foreach (var child in ((StackPanel)ProfitInfoBorder.Child).Children)
                {
                    if (child is TextBlock textBlock && textBlock != ProfitValueTextBlock)
                    {
                        textBlock.Foreground = Brushes.White;
                    }
                }
            }

            // 等待UI渲染完成
            await Task.Delay(500);

            // 截取窗口并复制到剪贴板
            CaptureWindowToClipboard();

            // 延迟关闭窗口
            DispatcherTimer timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(3)
            };
            timer.Tick += (s, args) =>
            {
                timer.Stop();
                this.Close();
            };
            timer.Start();
        }

        /// <summary>
        /// 截取当前窗口内容部分并复制到剪贴板
        /// </summary>
        private void CaptureWindowToClipboard()
        {
            try
            {
                // 获取窗口内容区域（Border元素）
                Border contentBorder = (Border)this.Content;

                // 确保布局已完成
                contentBorder.UpdateLayout();

                // 获取内容区域尺寸
                double width = contentBorder.ActualWidth;
                double height = contentBorder.ActualHeight;

                if (width <= 0 || height <= 0)
                {
                    throw new InvalidOperationException("内容区域尺寸无效");
                }

                // 创建RenderTargetBitmap，使用稍高的DPI以获得更清晰的图像
                RenderTargetBitmap renderTargetBitmap = new RenderTargetBitmap(
                    (int)(width * 1.5),
                    (int)(height * 1.5),
                    144, // DPI X (96 * 1.5)
                    144, // DPI Y (96 * 1.5)
                    PixelFormats.Pbgra32);

                // 创建DrawingVisual
                DrawingVisual drawingVisual = new DrawingVisual();
                using (DrawingContext drawingContext = drawingVisual.RenderOpen())
                {
                    // 创建VisualBrush，使用内容区域作为源
                    VisualBrush visualBrush = new VisualBrush(contentBorder);

                    // 绘制内容区域
                    drawingContext.DrawRectangle(
                        visualBrush,
                        null,
                        new Rect(0, 0, width, height));
                }

                // 渲染DrawingVisual
                renderTargetBitmap.Render(drawingVisual);

                // 复制到剪贴板
                Clipboard.SetImage(renderTargetBitmap);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"截图失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
