<Window x:Class="StockCrawler.MonthlyProfitSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="月度盈亏设置" Height="600" Width="800" WindowStartupLocation="CenterOwner"
        Loaded="Window_Loaded" Background="White" WindowStyle="None" AllowsTransparency="True"
        ResizeMode="CanResize" ShowInTaskbar="False">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>

    <!-- 添加窗口按钮样式 -->
    <Window.Resources>
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#595959"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FFF5F5F5"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FFE5E5E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="10,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="MinHeight" Value="40"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#40A9FF"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#096DD9"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 盈利编辑框样式 -->
        <Style x:Key="ProfitTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#FFEBEB"/>
            <Setter Property="Foreground" Value="#CC0000"/>
            <Setter Property="BorderBrush" Value="#FFCCCC"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Padding" Value="5,0"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- 亏损编辑框样式 -->
        <Style x:Key="LossTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#E6FFE6"/>
            <Setter Property="Foreground" Value="#006600"/>
            <Setter Property="BorderBrush" Value="#CCFFCC"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Padding" Value="5,0"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <!-- 添加窗口边框和阴影效果 -->
    <Border BorderThickness="1" BorderBrush="#E5E5E5" CornerRadius="8" Margin="5">
        <Border.Effect>
            <DropShadowEffect BlurRadius="15" ShadowDepth="0" Opacity="0.2" Color="#000000"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏区域 -->
            <Grid Grid.Row="0" Margin="20,15,20,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 标题信息 -->
                <TextBlock Grid.Column="0" Text="本年度月度盈亏" FontSize="18" FontWeight="Bold"
                           VerticalAlignment="Center" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown"/>

                <!-- 窗口控制按钮 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Width="30" Height="30" Click="CloseButton_Click"
                            Background="Transparent" BorderThickness="0" Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FFF5F5F5"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#FFE5E5E5"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                        <Path Data="M0,0 L10,10 M0,10 L10,0" Stroke="#595959" StrokeThickness="1"
                              Margin="10" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- 用户名输入和计算按钮 -->
            <Grid Grid.Row="1" Margin="20,0,20,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="用户名:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="1" x:Name="cmbUserName" Height="36" Padding="5,0" VerticalContentAlignment="Center"
                         SelectionChanged="cmbUserName_SelectionChanged"/>
                <Button Grid.Column="2" x:Name="btnLoadUser" Content="加载" Click="btnLoadUser_Click"
                        Width="80" Height="36" Margin="10,0,0,0" Style="{StaticResource ModernButton}"/>
                <Button Grid.Column="3" x:Name="btnCalculate" Content="每日盈亏计算" Click="btnCalculate_Click"
                        Width="120" Height="36" Margin="20,0,0,0" Style="{StaticResource ModernButton}"/>
                <Button Grid.Column="4" x:Name="btnYearlyProfit" Content="年度盈亏设置" Click="btnYearlyProfit_Click"
                        Width="120" Height="36" Margin="20,0,0,0" Style="{StaticResource ModernButton}"/>
                <Button Grid.Column="5" x:Name="btnStatistics" Content="统计" Click="btnStatistics_Click"
                        Width="80" Height="36" Margin="20,0,0,0" Style="{StaticResource ModernButton}"/>
            </Grid>

        <!-- 月度数据输入表格 -->
        <Grid Grid.Row="2" Margin="20,10,20,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 第一季度 -->
            <GroupBox Grid.Row="0" Grid.Column="0" Header="第一季度" Margin="5" BorderBrush="#E0E0E0" BorderThickness="1">
                <GroupBox.HeaderTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding}" FontWeight="Bold" Foreground="#1890FF"/>
                    </DataTemplate>
                </GroupBox.HeaderTemplate>
                <Grid Margin="10,15,10,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="一月:" Margin="0,0,10,10" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtJanuary" Margin="0,0,0,10" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="二月:" Margin="0,0,10,10" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtFebruary" Margin="0,0,0,10" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="三月:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtMarch" Margin="0" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>
                </Grid>
            </GroupBox>

            <!-- 第二季度 -->
            <GroupBox Grid.Row="0" Grid.Column="1" Header="第二季度" Margin="5" BorderBrush="#E0E0E0" BorderThickness="1">
                <GroupBox.HeaderTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding}" FontWeight="Bold" Foreground="#1890FF"/>
                    </DataTemplate>
                </GroupBox.HeaderTemplate>
                <Grid Margin="10,15,10,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="四月:" Margin="0,0,10,10" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtApril" Margin="0,0,0,10" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="五月:" Margin="0,0,10,10" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtMay" Margin="0,0,0,10" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="六月:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtJune" Margin="0" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>
                </Grid>
            </GroupBox>

            <!-- 第三季度 -->
            <GroupBox Grid.Row="0" Grid.Column="2" Header="第三季度" Margin="5" BorderBrush="#E0E0E0" BorderThickness="1">
                <GroupBox.HeaderTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding}" FontWeight="Bold" Foreground="#1890FF"/>
                    </DataTemplate>
                </GroupBox.HeaderTemplate>
                <Grid Margin="10,15,10,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="七月:" Margin="0,0,10,10" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtJuly" Margin="0,0,0,10" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="八月:" Margin="0,0,10,10" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtAugust" Margin="0,0,0,10" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="九月:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtSeptember" Margin="0" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>
                </Grid>
            </GroupBox>

            <!-- 第四季度 -->
            <GroupBox Grid.Row="0" Grid.Column="3" Header="第四季度" Margin="5" BorderBrush="#E0E0E0" BorderThickness="1">
                <GroupBox.HeaderTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding}" FontWeight="Bold" Foreground="#1890FF"/>
                    </DataTemplate>
                </GroupBox.HeaderTemplate>
                <Grid Margin="10,15,10,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="十月:" Margin="0,0,10,10" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtOctober" Margin="0,0,0,10" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="十一月:" Margin="0,0,10,10" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtNovember" Margin="0,0,0,10" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="十二月:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtDecember" Margin="0" Height="30"
                             Padding="5,0" VerticalContentAlignment="Center" TextChanged="MonthValue_TextChanged"/>
                </Grid>
            </GroupBox>
        </Grid>

        <!-- 年度总计 -->
        <Border Grid.Row="3" BorderBrush="#E0E0E0" BorderThickness="1" Margin="20,15,20,15" CornerRadius="6" Background="#F8F9FA">
            <Grid Margin="15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="年度总计:" FontSize="16" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock x:Name="txtYearlyTotal" Text="0" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="截止当前月份总计:" FontSize="16" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock x:Name="txtYearToDateTotal" Text="0" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="20,10,20,20">
            <Button x:Name="btnSave" Content="保存" Width="120" Height="40" Margin="0,0,20,0"
                    Click="btnSave_Click" Style="{StaticResource ModernButton}"/>
            <Button x:Name="btnCancel" Content="取消" Width="120" Height="40"
                    Click="btnCancel_Click" Style="{StaticResource ModernButton}" Background="#F5F5F5" Foreground="#595959"/>
        </StackPanel>
    </Grid>
</Border>
</Window>
