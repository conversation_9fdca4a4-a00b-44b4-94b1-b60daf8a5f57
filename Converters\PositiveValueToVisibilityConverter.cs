using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace StockCrawler.Converters
{
    /// <summary>
    /// 将正值转换为Visible，非正值转换为Collapsed的转换器
    /// </summary>
    public class PositiveValueToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return Visibility.Collapsed;

            // 尝试将值转换为decimal
            if (value is decimal decimalValue)
            {
                return decimalValue > 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            
            // 尝试将值转换为double
            if (value is double doubleValue)
            {
                return doubleValue > 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            
            // 尝试将值转换为int
            if (value is int intValue)
            {
                return intValue > 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            
            // 尝试将值转换为string并解析为decimal
            if (value is string stringValue && decimal.TryParse(stringValue, out decimal parsedValue))
            {
                return parsedValue > 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
