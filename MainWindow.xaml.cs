﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using System.Diagnostics;
using System.IO;
using System.ComponentModel;
using StockCrawler.Models;
using StockCrawler.Services;
using StockCrawler.Converters;
using StockCrawler.ViewModels;
using StockCrawler.Utils;

namespace StockCrawler;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly StockService _stockService;
    private readonly MairuiService _mairuiService;
    private readonly UserStockService _userStockService;
    private ObservableCollection<Stock> _stocks;
    private ObservableCollection<UserStock> _userStocks;
    private DispatcherTimer _timer;
    private MainViewModel _viewModel;

    public MainWindow()
    {
        InitializeComponent();

        // 初始化服务和数据
        _stockService = new StockService();
        _mairuiService = new MairuiService("72430658081e51fc98"); // 使用提供的API Key
        _userStockService = new UserStockService();
        _stocks = new ObservableCollection<Stock>();
        _userStocks = _userStockService.LoadUserStocks();

        // 初始化ViewModel
        _viewModel = new MainViewModel();
        DataContext = _viewModel;

        StockItemsControl.ItemsSource = _stocks;

        // 转换器已在XAML中注册

        // 设置定时器，每60秒自动刷新一次数据
        _timer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(60)
        };
        _timer.Tick += async (s, e) => await RefreshStocksDataAsync();

        // 加载初始数据
        Loaded += async (s, e) =>
        {
            this.Top = 0; // 让窗口紧贴屏幕顶部

            await RefreshStocksDataAsync();
            _timer.Start();

            // 调整窗口大小以适应内容
            AdjustWindowSizeToContent();
        };
    }

    // 调整窗口大小以适应内容
    private void AdjustWindowSizeToContent()
    {
        // 计算所有股票项的总高度
        double totalStocksHeight = _stocks.Count * 150; // 每个股票卡片高度约为150

        // 计算标题栏和总盈亏区域的高度
        double headerHeight = 50 + 50; // 标题栏和总盈亏区域的高度

        // 计算所需的窗口高度，加上边距
        double requiredHeight = totalStocksHeight + headerHeight + 80;

        // 获取屏幕高度
        double screenHeight = SystemParameters.PrimaryScreenHeight;

        // 如果所需高度小于屏幕高度的90%，则调整窗口高度
        if (requiredHeight < screenHeight * 0.9)
        {
            Height = requiredHeight;
        }
        else
        {
            // 否则设置为屏幕高度的90%
            Height = screenHeight * 0.9;
        }

        // 获取当前窗口位置
        double currentLeft = Left;
        double currentTop = Top;

        // 如果窗口已经显示（非初始状态），则保持窗口水平位置不变，但调整垂直位置使其居中
        if (currentLeft != 0 || currentTop != 0)
        {
            // 计算新的垂直位置，使窗口垂直居中
            Top = (screenHeight - Height) / 2;
        }
        else
        {
            // 初始状态，设置窗口在屏幕中央
            Left = (SystemParameters.PrimaryScreenWidth - Width) / 2;
            Top = (screenHeight - Height) / 2;
        }

        // 保证窗口贴紧屏幕顶部
        Top = 0;
    }

    private async Task RefreshStocksDataAsync()
    {
        try
        {
            // 显示加载状态
            RefreshButton.IsEnabled = false;
            RefreshButton.Content = "正在刷新...";

            // 获取股票数据
            var stocks = await _stockService.GetStocksDataAsync();

            // 检查是否有数据
            if (stocks == null || stocks.Count == 0)
            {
                MessageBox.Show("未能获取到任何股票数据，请检查网络连接或稍后再试。", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 检查数据是否有效
            bool hasValidData = false;
            foreach (var stock in stocks)
            {
                if (stock.CurrentPrice > 0)
                {
                    hasValidData = true;
                    break;
                }
            }

            if (!hasValidData)
            {
                MessageBox.Show("获取到的股票数据无效，可能是由于网络问题或未开盘。", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
            }

            // 更新UI并设置持股数量
            _stocks.Clear();
            decimal totalDailyProfit = 0;

            // 重新加载用户自选股数据（以防在设置窗口中有更改）
            _userStocks = _userStockService.LoadUserStocks();

            foreach (var stock in stocks)
            {
                // 查找对应的用户自选股
                var userStock = _userStocks.FirstOrDefault(us => us.Code == stock.Code);
                if (userStock != null)
                {
                    // 设置持股数量和持仓成本
                    stock.SharesHeld = userStock.Shares;
                    stock.CostPrice = userStock.CostPrice;
                }
                else
                {
                    // 如果不是自选股，持股数量和持仓成本为0
                    stock.SharesHeld = 0;
                    stock.CostPrice = 0;
                }

                // 计算当日盈亏并累加到总盈亏
                totalDailyProfit += stock.DailyProfit;

                _stocks.Add(stock);
            }

            // 更新总盈亏显示
            TotalProfitTextBlock.Text = $"{totalDailyProfit:F2}";

            // 设置总盈亏标签样式
            if (totalDailyProfit > 0)
            {
                TotalProfitTextBlock.Foreground = Brushes.Red;
                TotalProfitBorder.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFF1F0"));
                TotalProfitBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFCCC7"));
                TotalProfitBorder.BorderThickness = new Thickness(1);
            }
            else if (totalDailyProfit < 0)
            {
                TotalProfitTextBlock.Foreground = Brushes.Green;
                TotalProfitBorder.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F6FFED"));
                TotalProfitBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#B7EB8F"));
                TotalProfitBorder.BorderThickness = new Thickness(1);
            }
            else
            {
                TotalProfitTextBlock.Foreground = Brushes.Gray;
                TotalProfitBorder.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5"));
                TotalProfitBorder.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D9D9D9"));
                TotalProfitBorder.BorderThickness = new Thickness(1);
            }

            // 显示最后更新时间
            UpdateTimeTextBlock.Text = $"最后更新: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

            // 调整窗口大小以适应内容
            AdjustWindowSizeToContent();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"刷新数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            System.Diagnostics.Debug.WriteLine($"刷新数据异常: {ex}");
        }
        finally
        {
            // 恢复按钮状态
            RefreshButton.IsEnabled = true;
            RefreshButton.Content = "刷新数据";
        }
    }

    private async void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        await RefreshStocksDataAsync();
    }

    /// <summary>
    /// 自选股设置按钮点击事件
    /// </summary>
    private async void UserStockSettingsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 禁用按钮，防止重复点击
            UserStockSettingsButton.IsEnabled = false;
            UserStockSettingsButton.Content = "加载中...";

            // 创建并显示自选股设置窗口
            var settingsWindow = new UserStockSettingsWindow();
            settingsWindow.Owner = this;
            settingsWindow.ShowDialog();

            // 重新加载用户自选股数据
            _userStocks = _userStockService.LoadUserStocks();

            // 窗口关闭后强制刷新数据
            await RefreshStocksDataAsync();

            // 调整窗口大小以适应内容
            AdjustWindowSizeToContent();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开自选股设置窗口时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Debug.WriteLine($"打开自选股设置窗口异常: {ex}");
        }
        finally
        {
            // 恢复按钮状态
            UserStockSettingsButton.IsEnabled = true;
            UserStockSettingsButton.Content = "自选股设置";
        }
    }

    /// <summary>
    /// 新股信息按钮点击事件
    /// </summary>
    private async void NewStockInfoButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 禁用按钮，防止重复点击
            NewStockInfoButton.IsEnabled = false;
            NewStockInfoButton.Content = "加载中...";

            // 显示加载提示
            var loadingWindow = new LoadingWindow("正在获取新股发行信息...");
            loadingWindow.Owner = this;
            loadingWindow.Show();

            try
            {
                // 获取新股发行信息
                var newStockInfoList = await _mairuiService.GetNewStockInfoAsync();

                // 关闭加载窗口
                loadingWindow.Close();

                // 创建并显示新股信息窗口
                var newStockWindow = new NewStockInfoWindow(newStockInfoList);
                newStockWindow.Owner = this;
                newStockWindow.ShowDialog();
            }
            finally
            {
                // 确保加载窗口关闭
                if (loadingWindow.IsVisible)
                {
                    loadingWindow.Close();
                }
            }
        }
        catch (Exception ex)
        {
            // 获取内部异常信息
            string errorMessage = ex.Message;
            Exception innerEx = ex.InnerException;
            while (innerEx != null)
            {
                errorMessage += $"\n内部错误: {innerEx.Message}";
                innerEx = innerEx.InnerException;
            }

            MessageBox.Show($"获取新股发行信息时出错: {errorMessage}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Debug.WriteLine($"获取新股发行信息异常: {ex}");

            // 记录详细的堆栈跟踪
            Debug.WriteLine($"异常堆栈跟踪: {ex.StackTrace}");
        }
        finally
        {
            // 恢复按钮状态
            NewStockInfoButton.IsEnabled = true;
            NewStockInfoButton.Content = "新股信息";
        }
    }

    #region 窗口控制

    // 拖动窗口
    private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ClickCount == 2)
        {
            MaximizeButton_Click(sender, e);
        }
        else
        {
            DragMove();
        }
    }

    // 最小化窗口
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    // 最大化/还原窗口
    private void MaximizeButton_Click(object sender, RoutedEventArgs e)
    {
        if (WindowState == WindowState.Maximized)
        {
            WindowState = WindowState.Normal;
            MaximizeIcon.Data = Geometry.Parse("M0,0 L10,0 L10,10 L0,10 Z");
        }
        else
        {
            WindowState = WindowState.Maximized;
            MaximizeIcon.Data = Geometry.Parse("M0,2 L8,2 L8,10 L0,10 Z M2,0 L10,0 L10,8 L8,8 L8,2 L2,2 Z");
        }
    }

    // 关闭窗口
    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    #endregion

    /// <summary>
    /// 显示股票解禁限售信息
    /// </summary>
    private async void RestrictionInfoButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 获取按钮和股票代码
            var button = (Button)sender;
            string stockCode = button.Tag.ToString();

            // 禁用按钮，显示加载状态
            button.IsEnabled = false;
            button.Content = "加载中...";

            // 查找对应的股票对象
            var stock = _stocks.FirstOrDefault(s => s.Code == stockCode);
            if (stock == null)
            {
                MessageBox.Show($"未找到股票代码为 {stockCode} 的股票信息", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 获取解禁限售信息
            var restrictionInfoList = await _mairuiService.GetStockRestrictionInfoAsync(stockCode);

            // 创建并显示解禁限售信息窗口
            var restrictionWindow = new StockRestrictionWindow(stockCode, stock.Name, restrictionInfoList);
            restrictionWindow.Owner = this;
            restrictionWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取解禁限售信息时出错: {ex.Message}");
            MessageBox.Show($"获取解禁限售信息时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 恢复按钮状态
            if (sender is Button button)
            {
                button.IsEnabled = true;
                button.Content = "解禁限售";
            }
        }
    }

    /// <summary>
    /// 盈亏面板点击事件，截图并复制到剪贴板
    /// </summary>
    private void ProfitPanel_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        try
        {
            if (sender is FrameworkElement element)
            {
                // 捕获截图并复制到剪贴板
                bool success = ScreenshotHelper.CaptureElementToClipboard(element);

                // 获取股票信息
                var stock = element.Tag as Stock;
                string stockInfo = stock != null ? $"{stock.Name} ({stock.Code})" : "当前股票";

                // 显示提示
                if (success)
                {
                    MessageBox.Show($"{stockInfo}的盈亏信息已复制到剪贴板，可以粘贴使用。", "截图成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("截图失败，请稍后重试。", "截图失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"截图时出错: {ex.Message}");
            MessageBox.Show($"截图时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 截屏按钮点击事件，弹出总盈亏信息窗口并自动截屏
    /// </summary>
    private void ScreenshotButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 禁用按钮，防止重复点击
            ScreenshotButton.IsEnabled = false;

            // 获取当前总盈亏值
            string profitValue = TotalProfitTextBlock.Text;

            // 判断是盈利还是亏损
            bool isProfit = true;
            if (profitValue.StartsWith("-"))
            {
                isProfit = false;
            }

            // 创建并显示总盈亏信息窗口
            ProfitScreenshotWindow screenshotWindow = new ProfitScreenshotWindow(profitValue, isProfit);
            screenshotWindow.Owner = this;
            screenshotWindow.Show();
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"创建截图窗口时出错: {ex.Message}");
            MessageBox.Show($"截图时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 恢复按钮状态
            ScreenshotButton.IsEnabled = true;
        }
    }

    /// <summary>
    /// 各季度利润按钮点击事件
    /// </summary>
    private async void QuarterlyProfitButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 获取按钮和股票代码
            var button = (Button)sender;
            string stockCode = button.Tag.ToString();

            // 禁用按钮，显示加载状态
            button.IsEnabled = false;
            button.Content = "加载中...";

            // 查找对应的股票对象
            var stock = _stocks.FirstOrDefault(s => s.Code == stockCode);
            if (stock == null)
            {
                MessageBox.Show($"未找到股票代码为 {stockCode} 的股票信息", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 创建并显示各季度利润窗口
            var profitWindow = new QuarterlyProfitWindow(stockCode, stock.Name);
            profitWindow.Owner = this;
            profitWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"显示各季度利润窗口时出错: {ex.Message}");
            MessageBox.Show($"显示各季度利润窗口时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 恢复按钮状态
            if (sender is Button button)
            {
                button.IsEnabled = true;
                button.Content = "各季度利润";
            }
        }
    }

    /// <summary>
    /// 月度盈亏设置按钮点击事件
    /// </summary>
    private void MonthlyProfitSettingsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MonthlyProfitSettingsWindow window = new MonthlyProfitSettingsWindow();
            window.Owner = this;
            window.ShowDialog();
            LogHelper.LogInfo("月度盈亏设置已更新");
        }
        catch (Exception ex)
        {
            LogHelper.LogError($"打开月度盈亏设置窗口失败: {ex.Message}");
            MessageBox.Show($"打开月度盈亏设置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }


}