﻿using System.Configuration;
using System.Data;
using System.Windows;
using System.Windows.Media.Imaging;

namespace StockCrawler;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // 设置应用程序图标
        try
        {
            var iconUri = new Uri("pack://application:,,,/icons100.png", UriKind.Absolute);
            this.Resources["AppIcon"] = new BitmapImage(iconUri);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"加载图标时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
