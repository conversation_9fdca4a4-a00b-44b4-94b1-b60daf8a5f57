using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace StockCrawler.Models
{
    /// <summary>
    /// 每日盈亏数据模型
    /// </summary>
    public class DailyProfitData
    {
        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; } = DateTime.Now.Year;

        /// <summary>
        /// 月份
        /// </summary>
        public int Month { get; set; } = DateTime.Now.Month;

        /// <summary>
        /// 每日盈亏数据，键为日期（1-31），值为盈亏金额
        /// </summary>
        public Dictionary<int, decimal> DailyValues { get; set; } = new Dictionary<int, decimal>();

        /// <summary>
        /// 计算月度总盈亏
        /// </summary>
        public decimal MonthlyTotal
        {
            get
            {
                return DailyValues.Values.Sum();
            }
        }

        /// <summary>
        /// 计算截止当前日期的月度总盈亏
        /// </summary>
        public decimal MonthToDateTotal
        {
            get
            {
                int currentDay = DateTime.Now.Day;
                return DailyValues
                    .Where(kv => kv.Key <= currentDay)
                    .Sum(kv => kv.Value);
            }
        }

        /// <summary>
        /// 获取指定日期的盈亏值
        /// </summary>
        public decimal GetDayValue(int day)
        {
            if (DailyValues.TryGetValue(day, out decimal value))
            {
                return value;
            }
            return 0;
        }

        /// <summary>
        /// 设置指定日期的盈亏值
        /// </summary>
        public void SetDayValue(int day, decimal value)
        {
            if (day >= 1 && day <= 31)
            {
                DailyValues[day] = value;
            }
        }

        /// <summary>
        /// 获取当月的天数
        /// </summary>
        public int GetDaysInMonth()
        {
            return DateTime.DaysInMonth(Year, Month);
        }
    }
}
