using StockCrawler.Models;
using StockCrawler.Services;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace StockCrawler
{
    /// <summary>
    /// YearlyProfitSettingsWindow.xaml 的交互逻辑
    /// </summary>
    public partial class YearlyProfitSettingsWindow : Window
    {
        private YearlyProfitData _currentData;
        private bool _isInitializing = false;

        public YearlyProfitSettingsWindow()
        {
            InitializeComponent();
            _currentData = new YearlyProfitData();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            _isInitializing = true;

            // 加载用户列表
            LoadUserList();

            // 默认选择当前用户
            string defaultUserName = Environment.UserName;
            if (cmbUserName.Items.Contains(defaultUserName))
            {
                cmbUserName.SelectedItem = defaultUserName;
            }
            else if (cmbUserName.Items.Count > 0)
            {
                cmbUserName.SelectedIndex = 0;
            }
            else
            {
                // 如果没有用户，添加当前用户
                cmbUserName.Items.Add(defaultUserName);
                cmbUserName.SelectedItem = defaultUserName;
            }

            // 初始化年份下拉列表
            List<int> years = new List<int>();
            int currentYear = DateTime.Now.Year;
            for (int i = 2017; i <= currentYear; i++)
            {
                years.Add(i);
            }
            cmbYear.ItemsSource = years;
            cmbYear.SelectedItem = currentYear;

            _isInitializing = false;

            // 加载数据
            LoadUserData();
        }

        /// <summary>
        /// 加载用户列表
        /// </summary>
        private void LoadUserList()
        {
            try
            {
                // 清空列表
                cmbUserName.Items.Clear();

                // 加载所有用户数据
                var allUserData = YearlyProfitService.LoadYearlyProfitData();

                // 获取不重复的用户名列表
                var userNames = allUserData.Select(d => d.UserName).Distinct().ToList();

                // 添加到下拉列表
                foreach (var userName in userNames)
                {
                    cmbUserName.Items.Add(userName);
                }

                LogHelper.LogInfo($"已加载 {cmbUserName.Items.Count} 个用户");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载用户列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 标题栏拖动事件
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // 双击最大化/还原窗口
                if (WindowState == WindowState.Maximized)
                    WindowState = WindowState.Normal;
                else
                    WindowState = WindowState.Maximized;
            }
            else
            {
                // 单击拖动窗口
                DragMove();
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 加载用户数据
        /// </summary>
        private void LoadUserData()
        {
            try
            {
                if (_isInitializing)
                    return;

                string userName = cmbUserName.SelectedItem?.ToString();
                if (string.IsNullOrEmpty(userName))
                {
                    MessageBox.Show("请选择用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                int year = (int)(cmbYear.SelectedItem ?? DateTime.Now.Year);

                _currentData = YearlyProfitService.GetOrCreateUserYearData(userName, year);

                // 更新UI
                UpdateUI();

                LogHelper.LogInfo($"已加载用户 {userName} 的年度盈亏数据");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载用户数据失败: {ex.Message}");
                MessageBox.Show($"加载用户数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 用户选择改变事件
        /// </summary>
        private void cmbUserName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            LoadUserData();
        }

        /// <summary>
        /// 更新UI
        /// </summary>
        private void UpdateUI()
        {
            _isInitializing = true;

            // 更新数据输入框
            UpdateValueTextBox(txtInitialAsset, _currentData.InitialAsset);
            UpdateValueTextBox(txtTransferIn, _currentData.TransferIn);
            UpdateValueTextBox(txtTransferOut, _currentData.TransferOut);
            UpdateValueTextBox(txtPeriodProfit, _currentData.PeriodProfit);

            // 更新计算值
            UpdateCalculatedValues();

            _isInitializing = false;
        }

        /// <summary>
        /// 更新编辑框的值和样式
        /// </summary>
        private void UpdateValueTextBox(TextBox textBox, decimal value)
        {
            // 设置文本值
            textBox.Text = value.ToString("F2");

            // 根据盈亏值设置样式
            if (value >= 0)
            {
                // 盈利，使用红色样式
                textBox.Style = (Style)FindResource("ProfitTextBoxStyle");
            }
            else
            {
                // 亏损，使用绿色样式
                textBox.Style = (Style)FindResource("LossTextBoxStyle");
            }
        }

        /// <summary>
        /// 更新计算值
        /// </summary>
        private void UpdateCalculatedValues()
        {
            // 更新净转入
            decimal netTransfer = _currentData.NetTransfer;
            txtNetTransfer.Text = netTransfer.ToString("N2");
            txtNetTransfer.Foreground = netTransfer >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);

            // 更新期末资产
            decimal finalAsset = _currentData.FinalAsset;
            txtFinalAsset.Text = finalAsset.ToString("N2");
            txtFinalAsset.Foreground = finalAsset >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);

            // 更新年度收益率
            txtYearlyReturnRate.Text = _currentData.YearlyReturnRatePercent;
            txtYearlyReturnRate.Foreground = _currentData.YearlyReturnRate >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);
        }

        /// <summary>
        /// 从UI更新数据
        /// </summary>
        private void UpdateDataFromUI()
        {
            try
            {
                // 从UI更新数据模型
                if (decimal.TryParse(txtInitialAsset.Text, out decimal initialAsset)) _currentData.InitialAsset = initialAsset;
                if (decimal.TryParse(txtTransferIn.Text, out decimal transferIn)) _currentData.TransferIn = transferIn;
                if (decimal.TryParse(txtTransferOut.Text, out decimal transferOut)) _currentData.TransferOut = transferOut;
                if (decimal.TryParse(txtPeriodProfit.Text, out decimal periodProfit)) _currentData.PeriodProfit = periodProfit;

                // 更新计算值
                UpdateCalculatedValues();
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"更新数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 文本框值变更事件
        /// </summary>
        private void ValueTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            // 更新数据
            UpdateDataFromUI();

            // 更新当前编辑框的样式
            if (sender is TextBox textBox)
            {
                if (decimal.TryParse(textBox.Text, out decimal value))
                {
                    // 根据盈亏值设置样式
                    if (value >= 0)
                    {
                        // 盈利，使用红色样式
                        textBox.Style = (Style)FindResource("ProfitTextBoxStyle");
                    }
                    else
                    {
                        // 亏损，使用绿色样式
                        textBox.Style = (Style)FindResource("LossTextBoxStyle");
                    }
                }
            }
        }

        /// <summary>
        /// 年份选择变更事件
        /// </summary>
        private void cmbYear_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            LoadUserData();
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 禁用按钮，防止重复点击
                btnSave.IsEnabled = false;
                btnSave.Content = "保存中...";

                string userName = cmbUserName.SelectedItem?.ToString();
                if (string.IsNullOrEmpty(userName))
                {
                    MessageBox.Show("请选择用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 更新用户名
                _currentData.UserName = userName;

                // 保存数据
                YearlyProfitService.UpdateUserYearData(_currentData);

                MessageBox.Show("保存成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);

                // 关闭窗口
                DialogResult = true;
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"保存数据失败: {ex.Message}");
                MessageBox.Show($"保存数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnSave.IsEnabled = true;
                btnSave.Content = "保存";
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
