using StockCrawler.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace StockCrawler.Services
{
    /// <summary>
    /// 每日盈亏数据服务
    /// </summary>
    public class DailyProfitService
    {
        private const string CSV_FILE_NAME = "daily_profits.csv";
        private static readonly string CSV_FILE_PATH = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, CSV_FILE_NAME);

        /// <summary>
        /// 保存每日盈亏数据到CSV文件
        /// </summary>
        public static void SaveDailyProfitData(List<DailyProfitData> dataList)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(CSV_FILE_PATH, false, Encoding.UTF8))
                {
                    // 写入CSV头
                    writer.WriteLine("UserName,Year,Month,Day,Value");

                    // 写入数据
                    foreach (var data in dataList)
                    {
                        foreach (var dayValue in data.DailyValues)
                        {
                            writer.WriteLine($"{data.UserName},{data.Year},{data.Month},{dayValue.Key},{dayValue.Value}");
                        }
                    }
                }

                LogHelper.LogInfo($"成功保存每日盈亏数据到 {CSV_FILE_PATH}");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"保存每日盈亏数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从CSV文件加载每日盈亏数据
        /// </summary>
        public static List<DailyProfitData> LoadDailyProfitData()
        {
            Dictionary<string, DailyProfitData> dataDict = new Dictionary<string, DailyProfitData>();

            try
            {
                if (!File.Exists(CSV_FILE_PATH))
                {
                    LogHelper.LogInfo($"每日盈亏数据文件不存在: {CSV_FILE_PATH}");
                    return new List<DailyProfitData>();
                }

                using (StreamReader reader = new StreamReader(CSV_FILE_PATH, Encoding.UTF8))
                {
                    // 跳过CSV头
                    string header = reader.ReadLine();

                    // 读取数据行
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        if (string.IsNullOrWhiteSpace(line))
                            continue;

                        string[] values = line.Split(',');
                        if (values.Length < 5)
                            continue;

                        string userName = values[0];
                        if (int.TryParse(values[1], out int year) &&
                            int.TryParse(values[2], out int month) &&
                            int.TryParse(values[3], out int day) &&
                            decimal.TryParse(values[4], out decimal value))
                        {
                            // 创建唯一键
                            string key = $"{userName}_{year}_{month}";

                            // 获取或创建数据对象
                            if (!dataDict.TryGetValue(key, out DailyProfitData data))
                            {
                                data = new DailyProfitData
                                {
                                    UserName = userName,
                                    Year = year,
                                    Month = month
                                };
                                dataDict[key] = data;
                            }

                            // 设置日期值
                            data.SetDayValue(day, value);
                        }
                    }
                }

                LogHelper.LogInfo($"成功加载每日盈亏数据，共 {dataDict.Count} 条记录");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载每日盈亏数据失败: {ex.Message}");
            }

            return dataDict.Values.ToList();
        }

        /// <summary>
        /// 获取或创建用户的每日盈亏数据
        /// </summary>
        public static DailyProfitData GetOrCreateUserData(string userName, int year, int month)
        {
            List<DailyProfitData> dataList = LoadDailyProfitData();
            DailyProfitData userData = dataList.FirstOrDefault(d => 
                d.UserName == userName && d.Year == year && d.Month == month);

            if (userData == null)
            {
                userData = new DailyProfitData 
                { 
                    UserName = userName,
                    Year = year,
                    Month = month
                };
                dataList.Add(userData);
                SaveDailyProfitData(dataList);
            }

            return userData;
        }

        /// <summary>
        /// 更新用户的每日盈亏数据
        /// </summary>
        public static void UpdateUserData(DailyProfitData userData)
        {
            List<DailyProfitData> dataList = LoadDailyProfitData();
            DailyProfitData existingData = dataList.FirstOrDefault(d => 
                d.UserName == userData.UserName && 
                d.Year == userData.Year && 
                d.Month == userData.Month);

            if (existingData != null)
            {
                // 移除现有数据
                dataList.Remove(existingData);
            }

            // 添加新数据
            dataList.Add(userData);

            SaveDailyProfitData(dataList);
        }
    }
}
