<Window x:Class="StockCrawler.YearlyProfitSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="年度盈亏设置" Height="550" Width="800" WindowStartupLocation="CenterOwner"
        Loaded="Window_Loaded" Background="White" WindowStyle="None" AllowsTransparency="True"
        ResizeMode="CanResize" ShowInTaskbar="False">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>

    <!-- 添加窗口按钮样式 -->
    <Window.Resources>
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#595959"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FFF5F5F5"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FFE5E5E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="MinHeight" Value="36"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#40A9FF"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#096DD9"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 盈利编辑框样式 -->
        <Style x:Key="ProfitTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#FFEBEB"/>
            <Setter Property="Foreground" Value="#CC0000"/>
            <Setter Property="BorderBrush" Value="#FFCCCC"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Padding" Value="5,0"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- 亏损编辑框样式 -->
        <Style x:Key="LossTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#E6FFE6"/>
            <Setter Property="Foreground" Value="#006600"/>
            <Setter Property="BorderBrush" Value="#CCFFCC"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Padding" Value="5,0"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <!-- 添加窗口边框和阴影效果 -->
    <Border BorderThickness="1" BorderBrush="#E5E5E5" CornerRadius="8" Margin="5">
        <Border.Effect>
            <DropShadowEffect BlurRadius="15" ShadowDepth="0" Opacity="0.2" Color="#000000"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏区域 -->
            <Grid Grid.Row="0" Margin="20,15,20,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 标题信息 -->
                <TextBlock Grid.Column="0" Text="年度盈亏设置" FontSize="18" FontWeight="Bold"
                           VerticalAlignment="Center" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown"/>

                <!-- 窗口控制按钮 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Width="30" Height="30" Click="CloseButton_Click"
                            Background="Transparent" BorderThickness="0" Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FFF5F5F5"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#FFE5E5E5"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                        <Path Data="M0,0 L10,10 M0,10 L10,0" Stroke="#595959" StrokeThickness="1"
                              Margin="10" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- 用户名和年份选择 -->
            <Grid Grid.Row="1" Margin="20,0,20,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="用户名:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="1" x:Name="cmbUserName" Height="36" Padding="5,0" VerticalContentAlignment="Center"
                         SelectionChanged="cmbUserName_SelectionChanged"/>
                <TextBlock Grid.Column="2" Text="年份:" VerticalAlignment="Center" Margin="20,0,10,0"/>
                <ComboBox Grid.Column="3" x:Name="cmbYear" Height="36" Padding="5,0"
                          VerticalContentAlignment="Center" SelectionChanged="cmbYear_SelectionChanged"/>
            </Grid>

            <!-- 年度数据输入表格 -->
            <Grid Grid.Row="2" Margin="20,10,20,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 起初资产 -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="起初资产:" Margin="0,0,10,15" VerticalAlignment="Center"/>
                <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtInitialAsset" Margin="0,0,0,15" Height="30"
                         Padding="5,0" VerticalContentAlignment="Center" TextChanged="ValueTextBox_TextChanged"/>

                <!-- 转入 -->
                <TextBlock Grid.Row="1" Grid.Column="0" Text="转入:" Margin="0,0,10,15" VerticalAlignment="Center"/>
                <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtTransferIn" Margin="0,0,0,15" Height="30"
                         Padding="5,0" VerticalContentAlignment="Center" TextChanged="ValueTextBox_TextChanged"/>

                <!-- 转出 -->
                <TextBlock Grid.Row="2" Grid.Column="0" Text="转出:" Margin="0,0,10,15" VerticalAlignment="Center"/>
                <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtTransferOut" Margin="0,0,0,15" Height="30"
                         Padding="5,0" VerticalContentAlignment="Center" TextChanged="ValueTextBox_TextChanged"/>

                <!-- 净转入 -->
                <TextBlock Grid.Row="3" Grid.Column="0" Text="净转入:" Margin="0,0,10,15" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="txtNetTransfer" Text="0" Margin="5,0,0,15"
                           VerticalAlignment="Center" FontWeight="Bold"/>

                <!-- 期间盈亏 -->
                <TextBlock Grid.Row="4" Grid.Column="0" Text="期间盈亏:" Margin="0,0,10,15" VerticalAlignment="Center"/>
                <TextBox Grid.Row="4" Grid.Column="1" x:Name="txtPeriodProfit" Margin="0,0,0,15" Height="30"
                         Padding="5,0" VerticalContentAlignment="Center" TextChanged="ValueTextBox_TextChanged"/>

                <!-- 期末资产 -->
                <TextBlock Grid.Row="5" Grid.Column="0" Text="期末资产:" Margin="0,0,10,15" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="5" Grid.Column="1" x:Name="txtFinalAsset" Text="0" Margin="5,0,0,15"
                           VerticalAlignment="Center" FontWeight="Bold"/>

                <!-- 年度收益率 -->
                <TextBlock Grid.Row="6" Grid.Column="0" Text="年度收益率:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="6" Grid.Column="1" x:Name="txtYearlyReturnRate" Text="0%" Margin="5,0,0,0"
                           VerticalAlignment="Center" FontWeight="Bold"/>
            </Grid>

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="20,10,20,20">
                <Button x:Name="btnSave" Content="保存" Width="120" Height="40" Margin="0,0,20,0"
                        Click="btnSave_Click" Style="{StaticResource ModernButton}"/>
                <Button x:Name="btnCancel" Content="取消" Width="120" Height="40"
                        Click="btnCancel_Click" Style="{StaticResource ModernButton}" Background="#F5F5F5" Foreground="#595959"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
