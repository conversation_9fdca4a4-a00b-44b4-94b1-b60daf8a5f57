﻿#pragma checksum "..\..\..\ProfitStatisticsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "709F242708B659E7379EB55E5CD2AC22622D8D6B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using StockCrawler;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace StockCrawler {
    
    
    /// <summary>
    /// ProfitStatisticsWindow
    /// </summary>
    public partial class ProfitStatisticsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 137 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbUserName;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbYear;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas PersonalMonthlyCanvas;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas ThreePersonsCanvas;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalPeriodProfit;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalTransferIn;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalTransferOut;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtNetTransfer;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalInitialAsset;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalFinalAsset;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas YearlyProfitCanvas;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\ProfitStatisticsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/StockCrawler;component/profitstatisticswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ProfitStatisticsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\ProfitStatisticsWindow.xaml"
            ((StockCrawler.ProfitStatisticsWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.Window_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 103 "..\..\..\ProfitStatisticsWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 107 "..\..\..\ProfitStatisticsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.cmbUserName = ((System.Windows.Controls.ComboBox)(target));
            
            #line 138 "..\..\..\ProfitStatisticsWindow.xaml"
            this.cmbUserName.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbUserName_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.cmbYear = ((System.Windows.Controls.ComboBox)(target));
            
            #line 142 "..\..\..\ProfitStatisticsWindow.xaml"
            this.cmbYear.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbYear_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PersonalMonthlyCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 7:
            this.ThreePersonsCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 8:
            this.txtTotalPeriodProfit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.txtTotalTransferIn = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.txtTotalTransferOut = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.txtNetTransfer = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.txtTotalInitialAsset = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.txtTotalFinalAsset = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.YearlyProfitCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 15:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 256 "..\..\..\ProfitStatisticsWindow.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

