<Window x:Class="StockCrawler.NewStockInfoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="新股发行信息" Height="650" Width="1300"
        MinWidth="1300" MinHeight="500"
        WindowStartupLocation="CenterOwner"
        Background="#F8F9FA" FontFamily="Microsoft YaHei"
        WindowStyle="None" AllowsTransparency="True"
        BorderThickness="1" BorderBrush="#E0E0E0"
        ResizeMode="CanResizeWithGrip"
        Loaded="NewStockInfoWindow_Loaded"
        Closing="NewStockInfoWindow_Closing">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>

    <Window.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#8C8C8C"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F5F5F5"/>
                    <Setter Property="Foreground" Value="#FF4D4F"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#E8E8E8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- DataGrid样式 -->
        <Style TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E8E8E8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#F5F9FF"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#E0E0E0"/>
            <Setter Property="VerticalGridLinesBrush" Value="#E0E0E0"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="CanUserReorderColumns" Value="True"/>
            <Setter Property="CanUserResizeRows" Value="False"/>
            <Setter Property="CanUserResizeColumns" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="GridLinesVisibility" Value="All"/>
            <Setter Property="RowHeaderWidth" Value="0"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        </Style>

        <!-- 简化的DataGrid样式 - 使用x:Key避免冲突 -->
        <Style x:Key="NewStockDataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#F5F9FF"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#E0E0E0"/>
            <Setter Property="VerticalGridLinesBrush" Value="#E0E0E0"/>
            <Setter Property="GridLinesVisibility" Value="All"/>
            <Setter Property="RowHeaderWidth" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="ColumnWidth" Value="SizeToHeader"/>
            <Setter Property="MinColumnWidth" Value="60"/>
            <Setter Property="RowHeight" Value="30"/>
            <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
            <Setter Property="ScrollViewer.PanningMode" Value="Both"/>
            <Setter Property="Focusable" Value="True"/>
        </Style>

        <!-- 简化的DataGrid列头样式 - 使用x:Key避免冲突 -->
        <Style x:Key="NewStockColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="Foreground" Value="#1565C0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="8,10"/>
            <Setter Property="BorderBrush" Value="#BBDEFB"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- 简化的DataGrid单元格样式 - 使用x:Key避免冲突 -->
        <Style x:Key="NewStockCellStyle" TargetType="DataGridCell">
            <Setter Property="Padding" Value="4,6"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DataGridCell">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}">
                                <ContentPresenter.Resources>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextTrimming" Value="None"/>
                                        <Setter Property="TextWrapping" Value="NoWrap"/>
                                    </Style>
                                </ContentPresenter.Resources>
                            </ContentPresenter>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#90CAF9"/>
                    <Setter Property="Foreground" Value="#0D47A1"/>
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#EFF8FF"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 按钮样式 - 调整宽度和边距 -->
        <Style x:Key="DetailButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#1E88E5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,4"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="MinWidth" Value="60"/>
            <Setter Property="MinHeight" Value="24"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1E88E5"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Background="#2196F3" Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="新股发行信息" FontSize="16" FontWeight="Bold" Foreground="White" Margin="16,12"/>

                <Button Grid.Column="1" Style="{StaticResource CloseButtonStyle}" Click="CloseButton_Click" Margin="8,8,16,8">
                    <Path x:Name="CloseIcon" Data="M0,0 L10,10 M0,10 L10,0" Stroke="White" StrokeThickness="1.5" Width="10" Height="10"/>
                </Button>
            </Grid>
        </Border>

        <!-- 内容区域 -->
        <Grid Grid.Row="1" Margin="24,16">
            <!-- 新股发行数据表格 - 使用命名样式 -->
            <DataGrid x:Name="NewStockDataGrid"
                      Style="{StaticResource NewStockDataGridStyle}"
                      ColumnHeaderStyle="{StaticResource NewStockColumnHeaderStyle}"
                      CellStyle="{StaticResource NewStockCellStyle}"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      CanUserReorderColumns="True"
                      CanUserResizeRows="False"
                      CanUserResizeColumns="True"
                      SelectionMode="Single"
                      AutoGenerateColumns="False"
                      HeadersVisibility="Column"
                      IsReadOnly="True"
                      HorizontalScrollBarVisibility="Auto"
                      VerticalScrollBarVisibility="Auto">

                <!-- 简单直接的列定义 - 调整列宽 -->
                <DataGrid.Columns>
                    <DataGridTextColumn Header="股票代码" Binding="{Binding Code}" Width="70"/>
                    <DataGridTextColumn Header="股票名称" Binding="{Binding Name}" Width="80"/>
                    <DataGridTextColumn Header="申购代码" Binding="{Binding PurchaseCode}" Width="70"/>
                    <DataGridTextColumn Header="申购日期" Binding="{Binding FormattedPurchaseDate}" Width="100"/>
                    <DataGridTextColumn Header="发行价格" Binding="{Binding FormattedIssuePrice}" Width="75"/>
                    <DataGridTextColumn Header="申购上限" Binding="{Binding FormattedPurchaseLimit}" Width="100"/>
                    <DataGridTextColumn Header="顶格市值" Binding="{Binding FormattedTopPurchaseMarketValue}" Width="85"/>
                    <DataGridTextColumn Header="发行市盈率" Binding="{Binding FormattedIssuePERatio}" Width="75"/>
                    <DataGridTextColumn Header="行业市盈率" Binding="{Binding FormattedIndustryPERatio}" Width="75"/>
                    <DataGridTextColumn Header="中签号公布日" Binding="{Binding FormattedWinningNumberDate}" Width="110"/>
                    <DataGridTextColumn Header="中签缴款日" Binding="{Binding FormattedPaymentDate}" Width="110"/>
                    <DataGridTextColumn Header="上市日期" Binding="{Binding FormattedListingDate}" Width="100"/>
                    <DataGridTextColumn Header="中签率" Binding="{Binding FormattedWinningRate}" Width="70"/>

                    <DataGridTemplateColumn Header="详细" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button Content="详细" Click="DetailButton_Click" Margin="2" Padding="12,4"
                                        Style="{StaticResource DetailButtonStyle}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- 底部信息区域 -->
        <Border Grid.Row="2" Background="#F5F9FF" BorderBrush="#BBDEFB" BorderThickness="0,1,0,0" Padding="24,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="InfoTextBlock" Grid.Column="0" Text="注：仅显示当前日期之后的新股发行信息" Foreground="#1565C0" FontSize="12"/>

                <TextBlock Grid.Column="1" Text="提示：可拖动列标题调整列顺序，可拖动列边缘调整列宽度" Foreground="#1976D2" Margin="0,0,20,0" FontSize="12"/>

                <TextBlock x:Name="CountTextBlock" Grid.Column="2" Text="共 0 条记录" Foreground="#0D47A1" FontWeight="SemiBold" FontSize="12"/>
            </Grid>
        </Border>
    </Grid>
</Window>
