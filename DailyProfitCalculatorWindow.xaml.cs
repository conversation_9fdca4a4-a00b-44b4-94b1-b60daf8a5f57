using StockCrawler.Models;
using StockCrawler.Services;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace StockCrawler
{
    /// <summary>
    /// DailyProfitCalculatorWindow.xaml 的交互逻辑
    /// </summary>
    public partial class DailyProfitCalculatorWindow : Window
    {
        private DailyProfitData _currentData;
        private bool _isInitializing = false;
        private readonly List<string> _userNames = new List<string> { "谢羚羚", "景丝丝", "钟鹮鹮", "屈猫猫" };
        private readonly Dictionary<int, TextBox> _dayTextBoxes = new Dictionary<int, TextBox>();
        
        // 中国法定节假日列表（这里只添加了2025年的部分节假日，实际使用时应该更加完整）
        private static readonly Dictionary<int, List<(int month, int day)>> HolidaysByYear = new Dictionary<int, List<(int month, int day)>>
        {
            // 2025年法定节假日（示例）
            {2025, new List<(int month, int day)>
                {
                    (1, 1),   // 元旦
                    (1, 2),   // 元旦假期
                    (1, 3),   // 元旦假期
                    (1, 28),  // 春节前一天
                    (1, 29),  // 除夕
                    (1, 30),  // 春节
                    (1, 31),  // 春节假期
                    (2, 1),   // 春节假期
                    (2, 2),   // 春节假期
                    (2, 3),   // 春节假期
                    (4, 4),   // 清明节
                    (4, 5),   // 清明节假期
                    (4, 6),   // 清明节假期
                    (5, 1),   // 劳动节
                    (5, 2),   // 劳动节假期
                    (5, 3),   // 劳动节假期
                    (5, 4),   // 劳动节假期
                    (5, 5),   // 劳动节假期
                    (6, 9),   // 端午节
                    (6, 10),  // 端午节假期
                    (6, 11),  // 端午节假期
                    (9, 15),  // 中秋节
                    (9, 16),  // 中秋节假期
                    (9, 17),  // 中秋节假期
                    (10, 1),  // 国庆节
                    (10, 2),  // 国庆节假期
                    (10, 3),  // 国庆节假期
                    (10, 4),  // 国庆节假期
                    (10, 5),  // 国庆节假期
                    (10, 6),  // 国庆节假期
                    (10, 7),  // 国庆节假期
                }
            }
        };

        public DailyProfitCalculatorWindow()
        {
            InitializeComponent();
            _currentData = new DailyProfitData();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            _isInitializing = true;

            // 初始化用户下拉列表
            cmbUserName.ItemsSource = _userNames;
            cmbUserName.SelectedIndex = 0;

            // 初始化年份下拉列表
            List<int> years = new List<int>();
            int currentYear = DateTime.Now.Year;
            for (int i = currentYear - 2; i <= currentYear + 2; i++)
            {
                years.Add(i);
            }
            cmbYear.ItemsSource = years;
            cmbYear.SelectedItem = currentYear;

            // 初始化月份下拉列表
            List<int> months = new List<int>();
            for (int i = 1; i <= 12; i++)
            {
                months.Add(i);
            }
            cmbMonth.ItemsSource = months;
            cmbMonth.SelectedItem = DateTime.Now.Month;

            _isInitializing = false;

            // 加载数据
            LoadUserData();
        }

        /// <summary>
        /// 标题栏拖动事件
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // 双击最大化/还原窗口
                if (WindowState == WindowState.Maximized)
                    WindowState = WindowState.Normal;
                else
                    WindowState = WindowState.Maximized;
            }
            else
            {
                // 单击拖动窗口
                DragMove();
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 加载用户数据
        /// </summary>
        private void LoadUserData()
        {
            try
            {
                if (_isInitializing)
                    return;

                string userName = cmbUserName.SelectedItem?.ToString() ?? string.Empty;
                if (string.IsNullOrEmpty(userName))
                    return;

                int year = (int)(cmbYear.SelectedItem ?? DateTime.Now.Year);
                int month = (int)(cmbMonth.SelectedItem ?? DateTime.Now.Month);

                _currentData = DailyProfitService.GetOrCreateUserData(userName, year, month);

                // 创建每日盈亏输入框
                CreateDailyInputs();

                // 更新总计
                UpdateTotals();

                LogHelper.LogInfo($"已加载用户 {userName} 的每日盈亏数据");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载用户数据失败: {ex.Message}");
                MessageBox.Show($"加载用户数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 创建每日盈亏输入框
        /// </summary>
        private void CreateDailyInputs()
        {
            _isInitializing = true;
            _dayTextBoxes.Clear();
            DaysPanel.Children.Clear();

            int daysInMonth = _currentData.GetDaysInMonth();
            int year = _currentData.Year;
            int month = _currentData.Month;

            for (int day = 1; day <= daysInMonth; day++)
            {
                // 检查是否为周末或节假日
                bool isWeekendOrHoliday = IsWeekendOrHoliday(year, month, day);
                
                // 创建每日盈亏输入面板
                Border border = new Border
                {
                    BorderThickness = new Thickness(1),
                    CornerRadius = new CornerRadius(4),
                    Margin = new Thickness(5),
                    Padding = new Thickness(10),
                    Width = 150
                };
                
                // 如果是周末或节假日，应用特殊样式
                if (isWeekendOrHoliday)
                {
                    border.Style = (Style)FindResource("WeekendHolidayBorderStyle");
                }
                else
                {
                    border.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0"));
                }

                Grid grid = new Grid();
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                // 日期标签
                string dayOfWeek = GetDayOfWeekString(year, month, day);
                TextBlock dayLabel = new TextBlock
                {
                    Text = $"{day}日 {dayOfWeek}",
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                
                // 如果是周末或节假日，设置特殊颜色
                if (isWeekendOrHoliday)
                {
                    dayLabel.Foreground = new SolidColorBrush(Colors.Gray);
                }
                
                Grid.SetRow(dayLabel, 0);
                grid.Children.Add(dayLabel);

                // 获取当日盈亏值
                decimal dayValue = _currentData.GetDayValue(day);

                // 盈亏输入框
                TextBox dayInput = new TextBox
                {
                    Height = 30,
                    Padding = new Thickness(5, 0, 5, 0),
                    VerticalContentAlignment = VerticalAlignment.Center
                };
                
                if (isWeekendOrHoliday)
                {
                    // 周末或节假日，使用灰色样式并禁用编辑
                    dayInput.Style = (Style)FindResource("WeekendHolidayTextBoxStyle");
                }
                else
                {
                    // 正常交易日
                    dayInput.Text = dayValue.ToString("F2");
                    
                    // 根据盈亏值设置样式
                    if (dayValue >= 0)
                    {
                        // 盈利，使用红色样式
                        dayInput.Style = (Style)FindResource("ProfitTextBoxStyle");
                    }
                    else
                    {
                        // 亏损，使用绿色样式
                        dayInput.Style = (Style)FindResource("LossTextBoxStyle");
                    }

                    dayInput.Tag = day;
                    dayInput.TextChanged += ProfitValue_TextChanged;
                    
                    // 保存输入框引用
                    _dayTextBoxes[day] = dayInput;
                }
                
                Grid.SetRow(dayInput, 1);
                grid.Children.Add(dayInput);

                border.Child = grid;
                DaysPanel.Children.Add(border);
            }

            _isInitializing = false;
        }

        /// <summary>
        /// 检查指定日期是否为周末或节假日
        /// </summary>
        private bool IsWeekendOrHoliday(int year, int month, int day)
        {
            try
            {
                // 检查日期是否有效
                if (month < 1 || month > 12 || day < 1 || day > DateTime.DaysInMonth(year, month))
                    return false;
                
                // 创建指定日期的DateTime对象
                DateTime date = new DateTime(year, month, day);
                
                // 检查是否为周末（周六或周日）
                if (date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday)
                    return true;
                
                // 检查是否为法定节假日
                if (HolidaysByYear.TryGetValue(year, out var holidays))
                {
                    if (holidays.Contains((month, day)))
                        return true;
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 获取指定日期的星期几字符串
        /// </summary>
        private string GetDayOfWeekString(int year, int month, int day)
        {
            try
            {
                DateTime date = new DateTime(year, month, day);
                
                // 获取星期几的中文表示
                string[] weekDays = { "周日", "周一", "周二", "周三", "周四", "周五", "周六" };
                return weekDays[(int)date.DayOfWeek];
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 更新总计
        /// </summary>
        private void UpdateTotals()
        {
            // 更新月度总计
            decimal monthlyTotal = _currentData.MonthlyTotal;
            txtMonthlyTotal.Text = monthlyTotal.ToString("N2");
            txtMonthlyTotal.Foreground = monthlyTotal >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);

            // 更新截止当前日期总计
            decimal monthToDateTotal = _currentData.MonthToDateTotal;
            txtMonthToDateTotal.Text = monthToDateTotal.ToString("N2");
            txtMonthToDateTotal.Foreground = monthToDateTotal >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);
        }

        /// <summary>
        /// 从UI更新数据
        /// </summary>
        private void UpdateDataFromUI()
        {
            try
            {
                if (_isInitializing)
                    return;

                foreach (var pair in _dayTextBoxes)
                {
                    int day = pair.Key;
                    TextBox textBox = pair.Value;

                    if (decimal.TryParse(textBox.Text, out decimal value))
                    {
                        _currentData.SetDayValue(day, value);
                    }
                    else
                    {
                        _currentData.SetDayValue(day, 0);
                    }
                }

                // 更新总计
                UpdateTotals();
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"从UI更新数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 盈亏值文本框变更事件
        /// </summary>
        private void ProfitValue_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            // 更新数据
            UpdateDataFromUI();

            // 更新当前编辑框的样式
            if (sender is TextBox textBox)
            {
                if (decimal.TryParse(textBox.Text, out decimal value))
                {
                    // 根据盈亏值设置样式
                    if (value >= 0)
                    {
                        // 盈利，使用红色样式
                        textBox.Style = (Style)FindResource("ProfitTextBoxStyle");
                    }
                    else
                    {
                        // 亏损，使用绿色样式
                        textBox.Style = (Style)FindResource("LossTextBoxStyle");
                    }
                }
            }
        }

        /// <summary>
        /// 用户选择变更事件
        /// </summary>
        private void cmbUserName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            LoadUserData();
        }

        /// <summary>
        /// 年份选择变更事件
        /// </summary>
        private void cmbYear_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            LoadUserData();
        }

        /// <summary>
        /// 月份选择变更事件
        /// </summary>
        private void cmbMonth_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            LoadUserData();
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 禁用按钮，防止重复点击
                btnSave.IsEnabled = false;
                btnSave.Content = "保存中...";

                // 更新数据
                UpdateDataFromUI();

                // 保存数据
                DailyProfitService.UpdateUserData(_currentData);

                MessageBox.Show("保存成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);

                // 关闭窗口
                DialogResult = true;
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"保存数据失败: {ex.Message}");
                MessageBox.Show($"保存数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnSave.IsEnabled = true;
                btnSave.Content = "保存";
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
