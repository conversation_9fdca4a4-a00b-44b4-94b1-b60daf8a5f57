using StockCrawler.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace StockCrawler.Services
{
    /// <summary>
    /// 月度盈亏数据服务
    /// </summary>
    public class MonthlyProfitService
    {
        private const string CSV_FILE_NAME = "monthly_profits.csv";
        private static readonly string CSV_FILE_PATH = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, CSV_FILE_NAME);

        /// <summary>
        /// 保存月度盈亏数据到CSV文件
        /// </summary>
        public static void SaveMonthlyProfitData(List<MonthlyProfitData> dataList)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(CSV_FILE_PATH, false, Encoding.UTF8))
                {
                    // 写入CSV头
                    writer.WriteLine("UserName,January,February,March,April,May,June,July,August,September,October,November,December");

                    // 写入数据
                    foreach (var data in dataList)
                    {
                        writer.WriteLine($"{data.UserName},{data.January},{data.February},{data.March},{data.April},{data.May},{data.June}," +
                                        $"{data.July},{data.August},{data.September},{data.October},{data.November},{data.December}");
                    }
                }

                LogHelper.LogInfo($"成功保存月度盈亏数据到 {CSV_FILE_PATH}");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"保存月度盈亏数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从CSV文件加载月度盈亏数据
        /// </summary>
        public static List<MonthlyProfitData> LoadMonthlyProfitData()
        {
            List<MonthlyProfitData> dataList = new List<MonthlyProfitData>();

            try
            {
                if (!File.Exists(CSV_FILE_PATH))
                {
                    LogHelper.LogInfo($"月度盈亏数据文件不存在: {CSV_FILE_PATH}");
                    return dataList;
                }

                using (StreamReader reader = new StreamReader(CSV_FILE_PATH, Encoding.UTF8))
                {
                    // 跳过CSV头
                    string header = reader.ReadLine();

                    // 读取数据行
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        if (string.IsNullOrWhiteSpace(line))
                            continue;

                        string[] values = line.Split(',');
                        if (values.Length < 13)
                            continue;

                        MonthlyProfitData data = new MonthlyProfitData
                        {
                            UserName = values[0]
                        };

                        // 解析月度数据
                        for (int i = 1; i <= 12; i++)
                        {
                            if (decimal.TryParse(values[i], out decimal value))
                            {
                                data.SetMonthValue(i, value);
                            }
                        }

                        dataList.Add(data);
                    }
                }

                LogHelper.LogInfo($"成功加载月度盈亏数据，共 {dataList.Count} 条记录");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载月度盈亏数据失败: {ex.Message}");
            }

            return dataList;
        }

        /// <summary>
        /// 获取或创建用户的月度盈亏数据
        /// </summary>
        public static MonthlyProfitData GetOrCreateUserData(string userName)
        {
            List<MonthlyProfitData> dataList = LoadMonthlyProfitData();
            MonthlyProfitData userData = dataList.FirstOrDefault(d => d.UserName == userName);

            if (userData == null)
            {
                userData = new MonthlyProfitData { UserName = userName };
                dataList.Add(userData);
                SaveMonthlyProfitData(dataList);
            }

            return userData;
        }

        /// <summary>
        /// 更新用户的月度盈亏数据
        /// </summary>
        public static void UpdateUserData(MonthlyProfitData userData)
        {
            List<MonthlyProfitData> dataList = LoadMonthlyProfitData();
            MonthlyProfitData existingData = dataList.FirstOrDefault(d => d.UserName == userData.UserName);

            if (existingData != null)
            {
                // 更新现有数据
                for (int i = 1; i <= 12; i++)
                {
                    existingData.SetMonthValue(i, userData.GetMonthValue(i));
                }
            }
            else
            {
                // 添加新数据
                dataList.Add(userData);
            }

            SaveMonthlyProfitData(dataList);
        }
    }
}
