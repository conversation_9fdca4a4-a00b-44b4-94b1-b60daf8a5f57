using System;

namespace StockCrawler.Models
{
    public class StockRestrictionInfo
    {
        // 解禁日期
        public DateTime ReleaseDate { get; set; }
        
        // 解禁数量(万股)
        public decimal ReleaseAmount { get; set; }
        
        // 解禁股流通市值(亿元)
        public decimal ReleaseMarketValue { get; set; }
        
        // 上市批次
        public int Batch { get; set; }
        
        // 公告日期
        public DateTime? AnnouncementDate { get; set; }
        
        // 格式化后的解禁日期
        public string FormattedReleaseDate => ReleaseDate.ToString("yyyy-MM-dd");
        
        // 格式化后的公告日期
        public string FormattedAnnouncementDate => AnnouncementDate?.ToString("yyyy-MM-dd") ?? "未知";
        
        // 格式化后的解禁数量
        public string FormattedReleaseAmount => $"{ReleaseAmount:N0}万股";
        
        // 格式化后的解禁市值
        public string FormattedReleaseMarketValue => $"{ReleaseMarketValue:N2}亿元";
    }
}
