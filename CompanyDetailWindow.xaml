<Window x:Class="StockCrawler.CompanyDetailWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="公司详情" Height="400" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="#F8F9FA" FontFamily="Microsoft YaHei"
        WindowStyle="None" AllowsTransparency="True"
        BorderThickness="1" BorderBrush="#E0E0E0"
        ResizeMode="CanResizeWithGrip">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>

    <Window.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#8C8C8C"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F5F5F5"/>
                    <Setter Property="Foreground" Value="#FF4D4F"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#E8E8E8"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Background="#2196F3" Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="TitleTextBlock" Grid.Column="0" Text="公司详情" FontSize="16" FontWeight="Bold" Foreground="White" Margin="16,12"/>

                <Button Grid.Column="1" Style="{StaticResource CloseButtonStyle}" Click="CloseButton_Click" Margin="8,8,16,8">
                    <Path x:Name="CloseIcon" Data="M0,0 L10,10 M0,10 L10,0" Stroke="White" StrokeThickness="1.5" Width="10" Height="10"/>
                </Button>
            </Grid>
        </Border>

        <!-- 内容区域 -->
        <Grid Grid.Row="1" Margin="24,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 基本信息 -->
            <Grid Grid.Row="0" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="股票代码：" FontWeight="SemiBold" Foreground="#1565C0" Margin="0,0,8,8"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="CodeTextBlock" Text="" Foreground="#333333" Margin="0,0,16,8"/>

                <TextBlock Grid.Row="0" Grid.Column="2" Text="股票名称：" FontWeight="SemiBold" Foreground="#1565C0" Margin="0,0,8,8"/>
                <TextBlock Grid.Row="0" Grid.Column="3" x:Name="NameTextBlock" Text="" Foreground="#333333" Margin="0,0,0,8"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="申购日期：" FontWeight="SemiBold" Foreground="#1565C0" Margin="0,0,8,0"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="PurchaseDateTextBlock" Text="" Foreground="#333333" Margin="0,0,16,0"/>

                <TextBlock Grid.Row="1" Grid.Column="2" Text="发行价格：" FontWeight="SemiBold" Foreground="#1565C0" Margin="0,0,8,0"/>
                <TextBlock Grid.Row="1" Grid.Column="3" x:Name="IssuePriceTextBlock" Text="" Foreground="#333333" Margin="0,0,0,0"/>
            </Grid>

            <!-- 主营业务 -->
            <Grid Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="主营业务" FontSize="14" FontWeight="Bold" Foreground="#1565C0" Margin="0,0,0,8"/>

                <Border Grid.Row="1" BorderBrush="#E0E0E0" BorderThickness="1" Background="White">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                        <TextBlock x:Name="MainBusinessTextBlock" Text="" TextWrapping="Wrap" Padding="12" Foreground="#333333" LineHeight="24"/>
                    </ScrollViewer>
                </Border>
            </Grid>
        </Grid>

        <!-- 底部按钮区域 -->
        <Border Grid.Row="2" Background="#F5F9FF" BorderBrush="#BBDEFB" BorderThickness="0,1,0,0" Padding="24,12">
            <Button Content="关闭" Width="80" Height="30" HorizontalAlignment="Right" Click="CloseButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Background" Value="#2196F3"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="BorderBrush" Value="#1E88E5"/>
                        <Setter Property="BorderThickness" Value="1"/>
                        <Setter Property="Cursor" Value="Hand"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1E88E5"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </Border>
    </Grid>
</Window>
