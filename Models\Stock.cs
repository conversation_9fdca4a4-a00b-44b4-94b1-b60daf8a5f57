using System;
using System.Windows.Media;

namespace StockCrawler.Models
{
    public class Stock
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;

        // 持股数量
        public int SharesHeld { get; set; }

        // 持仓成本价
        public decimal CostPrice { get; set; }

        // 原始数据
        private decimal _currentPrice;
        private decimal _priceChange;
        private string _percentChange = "0.00%";

        // 当前价格
        public decimal CurrentPrice
        {
            get => _currentPrice;
            set => _currentPrice = value;
        }

        // 涨跌额
        public decimal PriceChange
        {
            get => _priceChange;
            set => _priceChange = value;
        }

        // 涨跌幅 - 原始字符串
        public string PercentChange
        {
            get => _percentChange;
            set => _percentChange = value;
        }

        // 实际价格（可能需要除以100）
        public decimal ActualPrice => CurrentPrice / 100;

        // 实际涨跌额（可能需要除以100）
        public decimal ActualPriceChange => PriceChange / 100;

        // 格式化后的价格显示（用于UI显示）
        public string FormattedPrice => $"{ActualPrice:F2}";

        // 格式化后的涨跌额显示（用于UI显示）
        public string FormattedPriceChange => $"{ActualPriceChange:F2}";

        // 格式化后的涨跌幅显示（用于UI显示）
        public string FormattedPercentChange
        {
            get
            {
                // 尝试从百分比字符串中提取数值
                if (decimal.TryParse(_percentChange.Replace("%", ""), out decimal percent))
                {
                    return $"{percent:F2}%";
                }
                return _percentChange;
            }
        }

        // 当日盈亏（持股数量 * 实际涨跌额）
        public decimal DailyProfit => SharesHeld * ActualPriceChange;

        // 格式化后的当日盈亏显示
        public string FormattedDailyProfit => $"{DailyProfit:F2}";

        // 当日盈亏百分比（相对于持仓市值）
        public decimal DailyProfitPercentage => TotalValue > 0 ? (DailyProfit / TotalValue) * 100 : 0;

        // 格式化后的当日盈亏百分比显示
        public string FormattedDailyProfitPercentage => $"{DailyProfitPercentage:F2}%";

        // 持仓市值（持股数量 * 实际价格）
        public decimal TotalValue => SharesHeld * ActualPrice;

        // 格式化后的持仓市值显示
        public string FormattedTotalValue => $"{TotalValue:F2}";

        // 持仓成本总额（持股数量 * 成本价）
        public decimal TotalCost => SharesHeld * CostPrice;

        // 格式化后的持仓成本总额显示
        public string FormattedTotalCost => $"{TotalCost:F2}";

        // 券商手续费率（3%）
        public const decimal BrokerageFeeRate = 0.03m;

        // 券商手续费（总盈亏的3%，无论盈利还是亏损都收取）
        public decimal BrokerageFee => Math.Abs(TotalRawProfit) * BrokerageFeeRate;

        // 格式化后的券商手续费显示
        public string FormattedBrokerageFee => $"{BrokerageFee:F2}";

        // 原始总盈亏（持仓市值 - 持仓成本总额，未扣除手续费）
        public decimal TotalRawProfit => TotalValue - TotalCost;

        // 格式化后的原始总盈亏显示
        public string FormattedTotalRawProfit => $"{TotalRawProfit:F2}";

        // 总盈亏（持仓市值 - 持仓成本总额 - 券商手续费）
        public decimal TotalProfit => TotalRawProfit - BrokerageFee;

        // 格式化后的总盈亏显示
        public string FormattedTotalProfit => $"{TotalProfit:F2}";

        // 总盈亏百分比（相对于持仓成本）
        public decimal TotalProfitPercentage => TotalCost > 0 ? (TotalProfit / TotalCost) * 100 : 0;

        // 格式化后的总盈亏百分比显示
        public string FormattedTotalProfitPercentage => $"{TotalProfitPercentage:F2}%";

        // 用于UI显示的颜色属性
        public Brush PercentChangeColor
        {
            get
            {
                if (ActualPriceChange > 0)
                    return Brushes.Red;
                else if (ActualPriceChange < 0)
                    return Brushes.Green;
                else
                    return Brushes.Gray;
            }
        }

        // 用于UI显示的盈亏颜色属性
        public Brush ProfitColor
        {
            get
            {
                if (DailyProfit > 0)
                    return Brushes.Red;
                else if (DailyProfit < 0)
                    return Brushes.Green;
                else
                    return Brushes.Gray;
            }
        }

        // 用于UI显示的总盈亏颜色属性
        public Brush TotalProfitColor
        {
            get
            {
                if (TotalProfit > 0)
                    return Brushes.Red;
                else if (TotalProfit < 0)
                    return Brushes.Green;
                else
                    return Brushes.Gray;
            }
        }
    }
}
