<Window x:Class="StockCrawler.LoadingWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="加载中" Height="150" Width="300"
        WindowStartupLocation="CenterOwner"
        Background="#F8F9FA" FontFamily="Microsoft YaHei"
        WindowStyle="None" AllowsTransparency="True"
        BorderThickness="1" BorderBrush="#E0E0E0"
        ResizeMode="NoResize">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>

    <Window.Resources>
        <!-- 加载动画样式 -->
        <Style x:Key="LoadingSpinner" TargetType="Canvas">
            <Style.Resources>
                <Storyboard x:Key="SpinnerStoryboard" RepeatBehavior="Forever">
                    <DoubleAnimation Storyboard.TargetProperty="(Canvas.RenderTransform).(RotateTransform.Angle)"
                                     From="0" To="360" Duration="0:0:1"/>
                </Storyboard>
            </Style.Resources>
            <Style.Triggers>
                <Trigger Property="Visibility" Value="Visible">
                    <Trigger.EnterActions>
                        <BeginStoryboard Storyboard="{StaticResource SpinnerStoryboard}"/>
                    </Trigger.EnterActions>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Border CornerRadius="8" Background="White" BorderBrush="#E0E0E0" BorderThickness="1">
        <Border.Effect>
            <DropShadowEffect BlurRadius="10" ShadowDepth="0" Opacity="0.2" Color="#000000"/>
        </Border.Effect>

        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Canvas Grid.Row="0" Width="30" Height="30" Style="{StaticResource LoadingSpinner}" HorizontalAlignment="Center" Margin="0,0,0,15">
                <Canvas.RenderTransform>
                    <RotateTransform CenterX="15" CenterY="15"/>
                </Canvas.RenderTransform>
                <Path Stroke="#1890FF" StrokeThickness="3" StrokeStartLineCap="Round" StrokeEndLineCap="Round">
                    <Path.Data>
                        <PathGeometry>
                            <PathFigure StartPoint="15,0">
                                <ArcSegment Size="15,15" IsLargeArc="True" SweepDirection="Clockwise" Point="15,30"/>
                            </PathFigure>
                        </PathGeometry>
                    </Path.Data>
                </Path>
            </Canvas>

            <TextBlock Grid.Row="1" x:Name="LoadingTextBlock" Text="加载中..." HorizontalAlignment="Center" VerticalAlignment="Center"
                       FontSize="14" Foreground="#595959"/>
        </Grid>
    </Border>
</Window>
