using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
using StockCrawler.Models;

namespace StockCrawler.Services
{
    /// <summary>
    /// 用户自选股服务，负责读取和保存用户自选股数据
    /// </summary>
    public class UserStockService
    {
        private readonly string _csvFilePath;

        /// <summary>
        /// 初始化用户自选股服务
        /// </summary>
        public UserStockService()
        {
            // 设置CSV文件路径
            _csvFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userstocks_rows.csv");
            Debug.WriteLine($"用户自选股CSV文件路径: {_csvFilePath}");

            // 确保文件存在
            EnsureCsvFileExists();
        }

        /// <summary>
        /// 确保CSV文件存在，如果不存在则创建
        /// </summary>
        private void EnsureCsvFileExists()
        {
            try
            {
                if (!File.Exists(_csvFilePath))
                {
                    // 创建文件并写入标题行
                    using (var writer = new StreamWriter(_csvFilePath, false, Encoding.UTF8))
                    {
                        writer.WriteLine("code,name,shares,costPrice");

                        // 添加一些示例数据
                        writer.WriteLine("600298,安琪酵母,2000,25.80");
                        writer.WriteLine("000516,国际医学,5000,3.50");
                        writer.WriteLine("000021,深科技,1000,18.20");
                        writer.WriteLine("000525,ST红太阳,700,2.10");
                        writer.WriteLine("000564,供销大集,100,1.50");
                    }

                    Debug.WriteLine("创建了新的用户自选股CSV文件");
                }
                else
                {
                    // 检查是否需要更新CSV文件结构（添加costPrice列）
                    UpdateCsvFileStructureIfNeeded();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"确保CSV文件存在时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 检查并更新CSV文件结构，添加缺失的列
        /// </summary>
        private void UpdateCsvFileStructureIfNeeded()
        {
            try
            {
                if (File.Exists(_csvFilePath))
                {
                    var lines = File.ReadAllLines(_csvFilePath, Encoding.UTF8);
                    if (lines.Length > 0)
                    {
                        var header = lines[0].Trim();
                        if (!header.Contains("costPrice"))
                        {
                            Debug.WriteLine("检测到旧版CSV文件结构，正在更新...");

                            // 创建新的内容
                            var newLines = new List<string>();
                            newLines.Add("code,name,shares,costPrice");

                            // 处理数据行，添加默认成本价0
                            for (int i = 1; i < lines.Length; i++)
                            {
                                var line = lines[i].Trim();
                                if (!string.IsNullOrEmpty(line))
                                {
                                    newLines.Add($"{line},0");
                                }
                            }

                            // 写回文件
                            File.WriteAllLines(_csvFilePath, newLines, Encoding.UTF8);
                            Debug.WriteLine("已更新CSV文件结构，添加了costPrice列");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"更新CSV文件结构时出错: {ex.Message}");
                // 不抛出异常，允许程序继续运行
            }
        }

        /// <summary>
        /// 从CSV文件加载用户自选股数据
        /// </summary>
        public ObservableCollection<UserStock> LoadUserStocks()
        {
            var userStocks = new ObservableCollection<UserStock>();

            try
            {
                if (File.Exists(_csvFilePath))
                {
                    // 读取CSV文件
                    var lines = File.ReadAllLines(_csvFilePath, Encoding.UTF8);

                    // 跳过标题行
                    for (int i = 1; i < lines.Length; i++)
                    {
                        var line = lines[i].Trim();
                        if (string.IsNullOrEmpty(line))
                            continue;

                        var parts = line.Split(',');
                        if (parts.Length >= 3)
                        {
                            var code = parts[0].Trim();
                            var name = parts[1].Trim();

                            if (int.TryParse(parts[2].Trim(), out int shares))
                            {
                                // 尝试解析成本价
                                decimal costPrice = 0;
                                if (parts.Length >= 4 && decimal.TryParse(parts[3].Trim(), out decimal parsedCostPrice))
                                {
                                    costPrice = parsedCostPrice;
                                }

                                userStocks.Add(new UserStock(code, name, shares, costPrice));
                            }
                            else
                            {
                                Debug.WriteLine($"无法解析持股数量: {parts[2]}");
                            }
                        }
                        else
                        {
                            Debug.WriteLine($"CSV行格式不正确: {line}");
                        }
                    }

                    Debug.WriteLine($"从CSV文件加载了 {userStocks.Count} 只自选股");
                }
                else
                {
                    Debug.WriteLine("CSV文件不存在，无法加载用户自选股");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载用户自选股时出错: {ex.Message}");
                throw;
            }

            return userStocks;
        }

        /// <summary>
        /// 保存用户自选股数据到CSV文件
        /// </summary>
        public void SaveUserStocks(IEnumerable<UserStock> userStocks)
        {
            try
            {
                // 创建CSV内容
                var sb = new StringBuilder();
                sb.AppendLine("code,name,shares,costPrice");

                foreach (var stock in userStocks)
                {
                    sb.AppendLine($"{stock.Code},{stock.Name},{stock.Shares},{stock.CostPrice:F2}");
                }

                // 写入文件
                File.WriteAllText(_csvFilePath, sb.ToString(), Encoding.UTF8);

                Debug.WriteLine($"已保存 {userStocks.Count()} 只自选股到CSV文件");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存用户自选股时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 添加新的用户自选股
        /// </summary>
        public void AddUserStock(ObservableCollection<UserStock> userStocks, UserStock newStock)
        {
            // 检查是否已存在相同代码的股票
            var existingStock = userStocks.FirstOrDefault(s => s.Code == newStock.Code);
            if (existingStock != null)
            {
                // 更新现有股票的持股数量
                existingStock.Shares = newStock.Shares;
                existingStock.Name = newStock.Name;
            }
            else
            {
                // 添加新股票
                userStocks.Add(newStock);
            }

            // 保存到文件
            SaveUserStocks(userStocks);
        }

        /// <summary>
        /// 更新用户自选股
        /// </summary>
        public void UpdateUserStock(ObservableCollection<UserStock> userStocks, UserStock updatedStock)
        {
            // 查找要更新的股票
            var existingStock = userStocks.FirstOrDefault(s => s.Code == updatedStock.Code);
            if (existingStock != null)
            {
                // 更新股票信息
                existingStock.Name = updatedStock.Name;
                existingStock.Shares = updatedStock.Shares;

                // 保存到文件
                SaveUserStocks(userStocks);
            }
        }

        /// <summary>
        /// 删除用户自选股
        /// </summary>
        public void DeleteUserStock(ObservableCollection<UserStock> userStocks, UserStock stockToDelete)
        {
            // 查找要删除的股票
            var existingStock = userStocks.FirstOrDefault(s => s.Code == stockToDelete.Code);
            if (existingStock != null)
            {
                // 从集合中删除
                userStocks.Remove(existingStock);

                // 保存到文件
                SaveUserStocks(userStocks);
            }
        }
    }
}
