<Window x:Class="StockCrawler.UserStockSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="自选股设置" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="#F8F9FA" FontFamily="Microsoft YaHei"
        WindowStyle="None" AllowsTransparency="True"
        BorderThickness="1" BorderBrush="#E0E0E0"
        ResizeMode="CanResizeWithGrip">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>

    <Window.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#8C8C8C"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F5F5F5"/>
                    <Setter Property="Foreground" Value="#FF4D4F"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#E8E8E8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 操作按钮样式 -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#40A9FF"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#096DD9"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BFBFBF"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 删除按钮样式 -->
        <Style x:Key="DeleteButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF4D4F"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF7875"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#CF1322"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BFBFBF"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 文本框样式 -->
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" Margin="{TemplateBinding Padding}" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="#40A9FF"/>
                </Trigger>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#1890FF"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- DataGrid样式 -->
        <Style TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E8E8E8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#F9F9F9"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#F0F0F0"/>
            <Setter Property="VerticalGridLinesBrush" Value="#F0F0F0"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="CanUserReorderColumns" Value="False"/>
            <Setter Property="CanUserResizeRows" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="GridLinesVisibility" Value="All"/>
            <Setter Property="RowHeaderWidth" Value="0"/>
        </Style>

        <!-- DataGrid列头样式 -->
        <Style TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#595959"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,12"/>
            <Setter Property="BorderBrush" Value="#E8E8E8"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
        </Style>

        <!-- DataGrid单元格样式 -->
        <Style TargetType="DataGridCell">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#F0F0F0"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DataGridCell">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="#E6F7FF"/>
                    <Setter Property="BorderBrush" Value="#91D5FF"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Background="#1890FF" Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="自选股设置" FontSize="16" FontWeight="Bold" Foreground="White" Margin="16,12"/>

                <Button Grid.Column="1" Style="{StaticResource CloseButtonStyle}" Click="CloseButton_Click" Margin="8,8,16,8">
                    <Path x:Name="CloseIcon" Data="M0,0 L10,10 M0,10 L10,0" Stroke="White" StrokeThickness="1.5" Width="10" Height="10"/>
                </Button>
            </Grid>
        </Border>

        <!-- 内容区域 -->
        <Grid Grid.Row="1" Margin="24,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 自选股列表 -->
            <DataGrid x:Name="UserStocksDataGrid" Grid.Row="0" Margin="0,0,0,16"
                      SelectionChanged="UserStocksDataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="股票代码" Binding="{Binding Code}" Width="120"/>
                    <DataGridTextColumn Header="股票名称" Binding="{Binding Name}" Width="150"/>
                    <DataGridTextColumn Header="持股数量" Binding="{Binding Shares}" Width="120"/>
                    <DataGridTextColumn Header="持仓成本" Binding="{Binding FormattedCostPrice}" Width="120"/>
                    <DataGridTemplateColumn Header="操作" Width="*">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                                    <Button Content="编辑" Style="{StaticResource ActionButtonStyle}" Margin="0,0,8,0" Padding="12,6"
                                            Click="EditButton_Click" Tag="{Binding}"/>
                                    <Button Content="删除" Style="{StaticResource DeleteButtonStyle}" Padding="12,6"
                                            Click="DeleteButton_Click" Tag="{Binding}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>

            <!-- 添加/编辑表单 -->
            <Border Grid.Row="1" Background="White" BorderBrush="#E8E8E8" BorderThickness="1" CornerRadius="4" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 表单标题 -->
                    <TextBlock Grid.Row="0" Grid.ColumnSpan="2" Text="添加/编辑自选股" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,16"/>

                    <!-- 股票代码 -->
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="股票代码:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="1" Grid.Column="1" x:Name="CodeTextBox" Style="{StaticResource TextBoxStyle}" Margin="0,0,0,12"/>

                    <!-- 股票名称 -->
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="股票名称:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="1" x:Name="NameTextBox" Style="{StaticResource TextBoxStyle}" Margin="0,0,0,12"/>

                    <!-- 持股数量 -->
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="持股数量:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="3" Grid.Column="1" x:Name="SharesTextBox" Style="{StaticResource TextBoxStyle}" Margin="0,0,0,12"/>

                    <!-- 持仓成本 -->
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="持仓成本:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="4" Grid.Column="1" x:Name="CostPriceTextBox" Style="{StaticResource TextBoxStyle}"/>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部按钮区域 -->
        <Grid Grid.Row="2" Margin="24,0,24,24">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="1" Content="保存" Style="{StaticResource ActionButtonStyle}" Margin="0,0,12,0"
                    Click="SaveButton_Click"/>

            <Button Grid.Column="2" Content="取消" Style="{StaticResource CloseButtonStyle}" Background="#F5F5F5"
                    Foreground="#595959" BorderBrush="#D9D9D9" BorderThickness="1" Padding="16,8"
                    Click="CancelButton_Click"/>
        </Grid>
    </Grid>
</Window>
