using System.Windows;
using System.Windows.Input;
using StockCrawler.Models;

namespace StockCrawler
{
    /// <summary>
    /// CompanyDetailWindow.xaml 的交互逻辑
    /// </summary>
    public partial class CompanyDetailWindow : Window
    {
        public CompanyDetailWindow(NewStockInfo stockInfo)
        {
            InitializeComponent();
            
            // 设置窗口标题
            TitleTextBlock.Text = $"{stockInfo.Name} - 公司详情";
            
            // 填充基本信息
            CodeTextBlock.Text = stockInfo.Code;
            NameTextBlock.Text = stockInfo.Name;
            PurchaseDateTextBlock.Text = stockInfo.FormattedPurchaseDate;
            IssuePriceTextBlock.Text = stockInfo.FormattedIssuePrice;
            
            // 填充主营业务信息
            MainBusinessTextBlock.Text = stockInfo.MainBusiness;
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
