using StockCrawler.Models;
using StockCrawler.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace StockCrawler
{
    /// <summary>
    /// ProfitStatisticsWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ProfitStatisticsWindow : Window
    {
        private bool _isInitializing = false;
        private readonly List<string> _userNames = new List<string> { "谢羚羚", "景丝丝", "钟鹮鹮", "屈猫猫" };
        private readonly SolidColorBrush _profitBrush = new SolidColorBrush(Colors.Red);
        private readonly SolidColorBrush _lossBrush = new SolidColorBrush(Colors.Green);
        private readonly SolidColorBrush _textBrush = new SolidColorBrush(Colors.Black);
        private readonly SolidColorBrush _gridBrush = new SolidColorBrush(Color.FromRgb(220, 220, 220));

        public ProfitStatisticsWindow()
        {
            InitializeComponent();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            _isInitializing = true;

            // 初始化用户下拉列表
            List<string> allUsers = new List<string>(_userNames);
            allUsers.Insert(0, Environment.UserName); // 添加当前用户
            cmbUserName.ItemsSource = allUsers;
            cmbUserName.SelectedIndex = 0;

            // 初始化年份下拉列表
            List<int> years = new List<int>();
            int currentYear = DateTime.Now.Year;
            for (int i = currentYear - 2; i <= currentYear + 2; i++)
            {
                years.Add(i);
            }
            cmbYear.ItemsSource = years;
            cmbYear.SelectedItem = currentYear;

            _isInitializing = false;

            // 加载数据并绘制图表
            LoadDataAndDrawCharts();
        }

        /// <summary>
        /// 标题栏拖动事件
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // 双击最大化/还原窗口
                if (WindowState == WindowState.Maximized)
                    WindowState = WindowState.Normal;
                else
                    WindowState = WindowState.Maximized;
            }
            else
            {
                // 单击拖动窗口
                DragMove();
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// 用户选择变更事件
        /// </summary>
        private void cmbUserName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            LoadDataAndDrawCharts();
        }

        /// <summary>
        /// 年份选择变更事件
        /// </summary>
        private void cmbYear_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            LoadDataAndDrawCharts();
        }

        /// <summary>
        /// 加载数据并绘制图表
        /// </summary>
        private void LoadDataAndDrawCharts()
        {
            try
            {
                string selectedUser = cmbUserName.SelectedItem?.ToString() ?? Environment.UserName;
                int selectedYear = (int)(cmbYear.SelectedItem ?? DateTime.Now.Year);

                // 获取当前月份，如果是未来年份则使用12月，如果是过去年份则使用当前月份
                int currentMonth = DateTime.Now.Month;
                int selectedMonth = selectedYear > DateTime.Now.Year ? 12 :
                                   (selectedYear < DateTime.Now.Year ? currentMonth : currentMonth);

                LogHelper.LogInfo($"加载统计数据: 用户={selectedUser}, 年份={selectedYear}, 月份={selectedMonth}");

                // 加载个人年内每月盈亏数据
                var personalMonthlyData = LoadPersonalMonthlyData(selectedUser, selectedYear);
                DrawPersonalMonthlyChart(personalMonthlyData);

                // 加载四人当月总盈亏数据
                var fourPersonsData = LoadThreePersonsData(selectedYear, selectedMonth);
                DrawThreePersonsChart(fourPersonsData);

                // 加载并显示多年汇总数据
                LoadAndDisplaySummaryData(selectedUser);

                // 加载并绘制年度盈亏柱状图
                var yearlyProfitData = LoadYearlyProfitData(selectedUser);
                DrawYearlyProfitChart(yearlyProfitData);

                LogHelper.LogInfo($"四人当月总盈亏数据: {string.Join(", ", fourPersonsData.Select(kv => $"{kv.Key}={kv.Value}"))}");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载统计数据失败: {ex.Message}");
                MessageBox.Show($"加载统计数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载并显示多年汇总数据
        /// </summary>
        private void LoadAndDisplaySummaryData(string userName)
        {
            try
            {
                LogHelper.LogInfo($"加载用户 {userName} 的多年汇总数据");

                // 从YearlyProfitService获取汇总数据
                var summaryData = YearlyProfitService.GetUserSummaryData(userName);

                // 显示汇总数据
                DisplaySummaryData(summaryData);

                LogHelper.LogInfo($"成功显示用户 {userName} 的多年汇总数据");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载多年汇总数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示汇总数据
        /// </summary>
        private void DisplaySummaryData(Dictionary<string, decimal> summaryData)
        {
            // 设置文本值
            txtTotalPeriodProfit.Text = summaryData["TotalPeriodProfit"].ToString("N2");
            txtTotalTransferIn.Text = summaryData["TotalTransferIn"].ToString("N2");
            txtTotalTransferOut.Text = summaryData["TotalTransferOut"].ToString("N2");
            txtNetTransfer.Text = summaryData["NetTransfer"].ToString("N2");
            txtTotalInitialAsset.Text = summaryData["TotalInitialAsset"].ToString("N2");
            txtTotalFinalAsset.Text = summaryData["TotalFinalAsset"].ToString("N2");

            // 设置颜色
            txtTotalPeriodProfit.Foreground = summaryData["TotalPeriodProfit"] >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);
            txtTotalTransferIn.Foreground = new SolidColorBrush(Colors.Red);  // 转入总是红色
            txtTotalTransferOut.Foreground = new SolidColorBrush(Colors.Green);  // 转出总是绿色
            txtNetTransfer.Foreground = summaryData["NetTransfer"] >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);
            txtTotalInitialAsset.Foreground = summaryData["TotalInitialAsset"] >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);
            txtTotalFinalAsset.Foreground = summaryData["TotalFinalAsset"] >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);

            // 设置字体大小和粗细，使数据更醒目
            txtTotalPeriodProfit.FontSize = 16;
            txtTotalPeriodProfit.FontWeight = FontWeights.Bold;

            // 如果总盈利值特别大，添加特殊效果
            if (Math.Abs(summaryData["TotalPeriodProfit"]) > 100000)
            {
                // 使用更大的字体和更粗的字体
                txtTotalPeriodProfit.FontSize = 18;
                txtTotalPeriodProfit.FontWeight = FontWeights.ExtraBold;
            }
        }

        /// <summary>
        /// 加载个人年内每月盈亏数据
        /// </summary>
        private MonthlyProfitData LoadPersonalMonthlyData(string userName, int year)
        {
            // 从服务加载数据
            var userData = MonthlyProfitService.GetOrCreateUserData(userName);
            return userData;
        }

        /// <summary>
        /// 加载四人当月总盈亏数据
        /// </summary>
        private Dictionary<string, decimal> LoadThreePersonsData(int year, int month)
        {
            Dictionary<string, decimal> result = new Dictionary<string, decimal>();

            LogHelper.LogInfo($"从每日盈亏数据加载四人当月总盈亏数据: 年份={year}, 月份={month}");

            // 从DailyProfitService加载每日盈亏数据
            List<DailyProfitData> allDailyData = DailyProfitService.LoadDailyProfitData();
            LogHelper.LogInfo($"加载到 {allDailyData.Count} 条每日盈亏数据");

            foreach (var userName in _userNames)
            {
                // 查找用户当月数据
                var userMonthData = allDailyData.FirstOrDefault(d =>
                    d.UserName == userName &&
                    d.Year == year &&
                    d.Month == month);

                decimal monthValue = 0;

                if (userMonthData != null)
                {
                    // 使用每日盈亏数据计算月度总盈亏
                    monthValue = userMonthData.MonthlyTotal;
                    LogHelper.LogInfo($"从每日盈亏数据计算: 用户 {userName} 年份 {year} 月份 {month} 盈亏值: {monthValue}");
                }
                else
                {
                    LogHelper.LogInfo($"未找到用户 {userName} 年份 {year} 月份 {month} 的每日盈亏数据");

                    // 尝试从月度盈亏数据获取
                    var monthlyData = MonthlyProfitService.GetOrCreateUserData(userName);
                    monthValue = monthlyData.GetMonthValue(month);
                    LogHelper.LogInfo($"从月度盈亏数据获取: 用户 {userName} 月份 {month} 盈亏值: {monthValue}");
                }

                result[userName] = monthValue;
            }

            // 如果没有数据，确保至少显示用户名
            if (result.Count == 0)
            {
                foreach (var userName in _userNames)
                {
                    result[userName] = 0;
                }
                LogHelper.LogInfo("没有找到任何数据，使用默认值0");
            }

            LogHelper.LogInfo($"四人当月总盈亏数据: {string.Join(", ", result.Select(kv => $"{kv.Key}={kv.Value}"))}");

            return result;
        }

        /// <summary>
        /// 创建测试数据
        /// </summary>
        private void CreateTestData()
        {
            LogHelper.LogInfo("开始创建三人盈亏测试数据");

            try
            {
                // 从CSV文件加载所有用户数据
                List<MonthlyProfitData> allUserData = MonthlyProfitService.LoadMonthlyProfitData();
                LogHelper.LogInfo($"当前CSV文件中有 {allUserData.Count} 条用户数据");

                // 记录现有用户
                foreach (var data in allUserData)
                {
                    LogHelper.LogInfo($"现有用户: {data.UserName}");
                }

                // 为三个用户创建测试数据
                Random random = new Random();
                bool dataChanged = false;

                foreach (var userName in _userNames)
                {
                    // 检查用户是否已存在
                    var existingUser = allUserData.FirstOrDefault(d => d.UserName == userName);
                    if (existingUser == null)
                    {
                        LogHelper.LogInfo($"创建新用户数据: {userName}");

                        // 创建新的用户数据
                        MonthlyProfitData userData = new MonthlyProfitData { UserName = userName };

                        // 设置随机的月度盈亏数据
                        for (int month = 1; month <= 12; month++)
                        {
                            // 生成-10000到10000之间的随机数
                            decimal value = (decimal)(random.NextDouble() * 20000 - 10000);
                            // 四舍五入到整数
                            value = Math.Round(value);

                            userData.SetMonthValue(month, value);
                            LogHelper.LogInfo($"用户 {userName} 月份 {month} 设置盈亏值: {value}");
                        }

                        // 添加到列表
                        allUserData.Add(userData);
                        dataChanged = true;
                    }
                    else
                    {
                        LogHelper.LogInfo($"用户 {userName} 已存在，检查月度数据");

                        // 检查是否所有月份都有数据
                        bool hasAllZeros = true;
                        for (int month = 1; month <= 12; month++)
                        {
                            decimal value = existingUser.GetMonthValue(month);
                            LogHelper.LogInfo($"用户 {userName} 月份 {month} 现有盈亏值: {value}");
                            if (value != 0)
                            {
                                hasAllZeros = false;
                            }
                        }

                        // 如果所有月份都是0，则设置随机值
                        if (hasAllZeros)
                        {
                            LogHelper.LogInfo($"用户 {userName} 所有月份盈亏值都为0，设置随机值");

                            for (int month = 1; month <= 12; month++)
                            {
                                // 生成-10000到10000之间的随机数
                                decimal value = (decimal)(random.NextDouble() * 20000 - 10000);
                                // 四舍五入到整数
                                value = Math.Round(value);

                                existingUser.SetMonthValue(month, value);
                                LogHelper.LogInfo($"用户 {userName} 月份 {month} 更新盈亏值: {value}");
                            }

                            dataChanged = true;
                        }
                    }
                }

                // 只有在数据发生变化时才保存
                if (dataChanged)
                {
                    LogHelper.LogInfo("数据已变更，保存到CSV文件");
                    MonthlyProfitService.SaveMonthlyProfitData(allUserData);
                }
                else
                {
                    LogHelper.LogInfo("数据未变更，无需保存");
                }

                // 验证数据是否正确保存
                List<MonthlyProfitData> verifyData = MonthlyProfitService.LoadMonthlyProfitData();
                foreach (var userName in _userNames)
                {
                    var userData = verifyData.FirstOrDefault(d => d.UserName == userName);
                    if (userData != null)
                    {
                        LogHelper.LogInfo($"验证用户 {userName} 数据:");
                        for (int month = 1; month <= 12; month++)
                        {
                            decimal value = userData.GetMonthValue(month);
                            LogHelper.LogInfo($"  月份 {month}: {value}");
                        }
                    }
                    else
                    {
                        LogHelper.LogError($"验证失败: 用户 {userName} 数据未找到");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"创建测试数据失败: {ex.Message}");
                LogHelper.LogError($"异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 绘制个人年内每月盈亏图表
        /// </summary>
        private void DrawPersonalMonthlyChart(MonthlyProfitData data)
        {
            // 清空画布
            PersonalMonthlyCanvas.Children.Clear();

            // 获取画布尺寸
            double canvasWidth = PersonalMonthlyCanvas.ActualWidth;
            double canvasHeight = PersonalMonthlyCanvas.ActualHeight;

            // 如果画布尺寸为0，设置默认值
            if (canvasWidth <= 0) canvasWidth = 800;
            if (canvasHeight <= 0) canvasHeight = 300;

            // 设置图表边距
            double marginLeft = 60;
            double marginRight = 20;
            double marginTop = 20;
            double marginBottom = 40;

            // 计算图表区域
            double chartWidth = canvasWidth - marginLeft - marginRight;
            double chartHeight = canvasHeight - marginTop - marginBottom;

            // 绘制坐标轴
            DrawAxes(PersonalMonthlyCanvas, marginLeft, marginTop, chartWidth, chartHeight);

            // 获取月度数据
            List<decimal> monthlyValues = new List<decimal>
            {
                data.January, data.February, data.March, data.April, data.May, data.June,
                data.July, data.August, data.September, data.October, data.November, data.December
            };

            // 找出最大值和最小值
            decimal maxValue = monthlyValues.Max();
            decimal minValue = monthlyValues.Min();
            decimal absMaxValue = Math.Max(Math.Abs(maxValue), Math.Abs(minValue));

            // 确保有一个合理的范围
            if (absMaxValue == 0) absMaxValue = 1000;

            // 计算比例
            double yScale = chartHeight / (double)(absMaxValue * 2);
            double barWidth = chartWidth / 12 * 0.6; // 柱宽为间距的60%
            double barSpacing = chartWidth / 12;

            // 绘制月份标签和网格线
            for (int i = 0; i < 12; i++)
            {
                double x = marginLeft + i * barSpacing + barSpacing / 2;

                // 绘制月份标签
                TextBlock monthLabel = new TextBlock
                {
                    Text = (i + 1) + "月",
                    Foreground = _textBrush,
                    TextAlignment = TextAlignment.Center,
                    Width = barWidth
                };
                Canvas.SetLeft(monthLabel, x - barWidth / 2);
                Canvas.SetTop(monthLabel, marginTop + chartHeight + 10);
                PersonalMonthlyCanvas.Children.Add(monthLabel);

                // 绘制垂直网格线
                Line gridLine = new Line
                {
                    X1 = x,
                    Y1 = marginTop,
                    X2 = x,
                    Y2 = marginTop + chartHeight,
                    Stroke = _gridBrush,
                    StrokeThickness = 1,
                    StrokeDashArray = new DoubleCollection { 4, 2 }
                };
                PersonalMonthlyCanvas.Children.Add(gridLine);
            }

            // 绘制水平网格线和数值标签
            int numHorizontalLines = 4; // 上下各2条线
            for (int i = 0; i <= numHorizontalLines; i++)
            {
                double y = marginTop + chartHeight / 2 - (i - numHorizontalLines / 2) * (chartHeight / numHorizontalLines);
                decimal value = (decimal)(i - numHorizontalLines / 2) * (absMaxValue * 2 / numHorizontalLines);

                // 绘制水平网格线
                Line gridLine = new Line
                {
                    X1 = marginLeft,
                    Y1 = y,
                    X2 = marginLeft + chartWidth,
                    Y2 = y,
                    Stroke = _gridBrush,
                    StrokeThickness = 1,
                    StrokeDashArray = new DoubleCollection { 4, 2 }
                };
                PersonalMonthlyCanvas.Children.Add(gridLine);

                // 绘制数值标签
                TextBlock valueLabel = new TextBlock
                {
                    Text = value.ToString("N0"),
                    Foreground = _textBrush,
                    TextAlignment = TextAlignment.Right,
                    Width = marginLeft - 5
                };
                Canvas.SetLeft(valueLabel, 0);
                Canvas.SetTop(valueLabel, y - 10);
                PersonalMonthlyCanvas.Children.Add(valueLabel);
            }

            // 绘制柱状图
            for (int i = 0; i < 12; i++)
            {
                decimal value = monthlyValues[i];
                double barHeight = Math.Abs((double)value * yScale);
                double x = marginLeft + i * barSpacing + (barSpacing - barWidth) / 2;
                double y = marginTop + chartHeight / 2; // 从中间开始

                if (value >= 0)
                {
                    // 盈利，向上绘制红色柱子
                    Rectangle bar = new Rectangle
                    {
                        Width = barWidth,
                        Height = barHeight,
                        Fill = _profitBrush
                    };
                    Canvas.SetLeft(bar, x);
                    Canvas.SetTop(bar, y - barHeight);
                    PersonalMonthlyCanvas.Children.Add(bar);
                }
                else
                {
                    // 亏损，向下绘制绿色柱子
                    Rectangle bar = new Rectangle
                    {
                        Width = barWidth,
                        Height = barHeight,
                        Fill = _lossBrush
                    };
                    Canvas.SetLeft(bar, x);
                    Canvas.SetTop(bar, y);
                    PersonalMonthlyCanvas.Children.Add(bar);
                }

                // 绘制数值标签
                if (value != 0)
                {
                    TextBlock valueLabel = new TextBlock
                    {
                        Text = value.ToString("N0"),
                        Foreground = value >= 0 ? _profitBrush : _lossBrush,
                        TextAlignment = TextAlignment.Center,
                        Width = barWidth
                    };
                    Canvas.SetLeft(valueLabel, x);
                    Canvas.SetTop(valueLabel, value >= 0 ? y - barHeight - 20 : y + barHeight + 5);
                    PersonalMonthlyCanvas.Children.Add(valueLabel);
                }
            }
        }

        /// <summary>
        /// 绘制四人当月总盈亏图表
        /// </summary>
        private void DrawThreePersonsChart(Dictionary<string, decimal> data)
        {
            // 清空画布
            ThreePersonsCanvas.Children.Clear();

            // 获取画布尺寸
            double canvasWidth = ThreePersonsCanvas.ActualWidth;
            double canvasHeight = ThreePersonsCanvas.ActualHeight;

            // 如果画布尺寸为0，设置默认值
            if (canvasWidth <= 0) canvasWidth = 800;
            if (canvasHeight <= 0) canvasHeight = 300;

            // 设置图表边距
            double marginLeft = 60;
            double marginRight = 20;
            double marginTop = 20;
            double marginBottom = 40;

            // 计算图表区域
            double chartWidth = canvasWidth - marginLeft - marginRight;
            double chartHeight = canvasHeight - marginTop - marginBottom;

            // 绘制坐标轴
            DrawAxes(ThreePersonsCanvas, marginLeft, marginTop, chartWidth, chartHeight);

            // 获取数据
            List<string> names = data.Keys.ToList();
            List<decimal> values = data.Values.ToList();

            LogHelper.LogInfo($"绘制四人当月总盈亏图表: 用户数={names.Count}, 数据={string.Join(", ", data.Select(kv => $"{kv.Key}={kv.Value}"))}");

            // 确保有数据可显示
            if (names.Count == 0)
            {
                LogHelper.LogInfo("没有四人当月总盈亏数据，使用默认数据");
                // 使用默认数据
                foreach (var userName in _userNames)
                {
                    names.Add(userName);
                    values.Add(0); // 默认值为0
                }
            }

            // 找出最大值和最小值
            decimal maxValue = values.Count > 0 ? values.Max() : 0;
            decimal minValue = values.Count > 0 ? values.Min() : 0;
            decimal absMaxValue = Math.Max(Math.Abs(maxValue), Math.Abs(minValue));

            // 确保有一个合理的范围
            if (absMaxValue == 0) absMaxValue = 1000;

            // 计算比例
            double yScale = chartHeight / (double)(absMaxValue * 2);
            double barWidth = chartWidth / names.Count * 0.6; // 柱宽为间距的60%
            double barSpacing = chartWidth / names.Count;

            // 绘制姓名标签和网格线
            for (int i = 0; i < names.Count; i++)
            {
                double x = marginLeft + i * barSpacing + barSpacing / 2;

                // 绘制姓名标签
                TextBlock nameLabel = new TextBlock
                {
                    Text = names[i],
                    Foreground = _textBrush,
                    TextAlignment = TextAlignment.Center,
                    Width = barWidth
                };
                Canvas.SetLeft(nameLabel, x - barWidth / 2);
                Canvas.SetTop(nameLabel, marginTop + chartHeight + 10);
                ThreePersonsCanvas.Children.Add(nameLabel);

                // 绘制垂直网格线
                Line gridLine = new Line
                {
                    X1 = x,
                    Y1 = marginTop,
                    X2 = x,
                    Y2 = marginTop + chartHeight,
                    Stroke = _gridBrush,
                    StrokeThickness = 1,
                    StrokeDashArray = new DoubleCollection { 4, 2 }
                };
                ThreePersonsCanvas.Children.Add(gridLine);
            }

            // 绘制水平网格线和数值标签
            int numHorizontalLines = 4; // 上下各2条线
            for (int i = 0; i <= numHorizontalLines; i++)
            {
                double y = marginTop + chartHeight / 2 - (i - numHorizontalLines / 2) * (chartHeight / numHorizontalLines);
                decimal value = (decimal)(i - numHorizontalLines / 2) * (absMaxValue * 2 / numHorizontalLines);

                // 绘制水平网格线
                Line gridLine = new Line
                {
                    X1 = marginLeft,
                    Y1 = y,
                    X2 = marginLeft + chartWidth,
                    Y2 = y,
                    Stroke = _gridBrush,
                    StrokeThickness = 1,
                    StrokeDashArray = new DoubleCollection { 4, 2 }
                };
                ThreePersonsCanvas.Children.Add(gridLine);

                // 绘制数值标签
                TextBlock valueLabel = new TextBlock
                {
                    Text = value.ToString("N0"),
                    Foreground = _textBrush,
                    TextAlignment = TextAlignment.Right,
                    Width = marginLeft - 5
                };
                Canvas.SetLeft(valueLabel, 0);
                Canvas.SetTop(valueLabel, y - 10);
                ThreePersonsCanvas.Children.Add(valueLabel);
            }

            // 绘制柱状图
            for (int i = 0; i < names.Count; i++)
            {
                decimal value = values[i];
                double barHeight = Math.Abs((double)value * yScale);
                double x = marginLeft + i * barSpacing + (barSpacing - barWidth) / 2;
                double y = marginTop + chartHeight / 2; // 从中间开始

                if (value >= 0)
                {
                    // 盈利，向上绘制红色柱子
                    Rectangle bar = new Rectangle
                    {
                        Width = barWidth,
                        Height = barHeight,
                        Fill = _profitBrush
                    };
                    Canvas.SetLeft(bar, x);
                    Canvas.SetTop(bar, y - barHeight);
                    ThreePersonsCanvas.Children.Add(bar);
                }
                else
                {
                    // 亏损，向下绘制绿色柱子
                    Rectangle bar = new Rectangle
                    {
                        Width = barWidth,
                        Height = barHeight,
                        Fill = _lossBrush
                    };
                    Canvas.SetLeft(bar, x);
                    Canvas.SetTop(bar, y);
                    ThreePersonsCanvas.Children.Add(bar);
                }

                // 绘制数值标签
                if (value != 0)
                {
                    TextBlock valueLabel = new TextBlock
                    {
                        Text = value.ToString("N0"),
                        Foreground = value >= 0 ? _profitBrush : _lossBrush,
                        TextAlignment = TextAlignment.Center,
                        Width = barWidth
                    };
                    Canvas.SetLeft(valueLabel, x);
                    Canvas.SetTop(valueLabel, value >= 0 ? y - barHeight - 20 : y + barHeight + 5);
                    ThreePersonsCanvas.Children.Add(valueLabel);
                }
            }
        }

        /// <summary>
        /// 绘制坐标轴
        /// </summary>
        private void DrawAxes(Canvas canvas, double marginLeft, double marginTop, double chartWidth, double chartHeight)
        {
            // 绘制X轴
            Line xAxis = new Line
            {
                X1 = marginLeft,
                Y1 = marginTop + chartHeight / 2,
                X2 = marginLeft + chartWidth,
                Y2 = marginTop + chartHeight / 2,
                Stroke = _textBrush,
                StrokeThickness = 1
            };
            canvas.Children.Add(xAxis);

            // 绘制Y轴
            Line yAxis = new Line
            {
                X1 = marginLeft,
                Y1 = marginTop,
                X2 = marginLeft,
                Y2 = marginTop + chartHeight,
                Stroke = _textBrush,
                StrokeThickness = 1
            };
            canvas.Children.Add(yAxis);
        }

        /// <summary>
        /// 加载年度盈亏数据
        /// </summary>
        private Dictionary<int, YearlyProfitInfo> LoadYearlyProfitData(string userName)
        {
            Dictionary<int, YearlyProfitInfo> result = new Dictionary<int, YearlyProfitInfo>();

            try
            {
                // 获取所有年度数据
                var allYearlyData = YearlyProfitService.LoadYearlyProfitData();

                // 筛选当前用户的数据
                var userYearlyData = allYearlyData.Where(d => d.UserName == userName).ToList();

                // 计算每年的盈亏数据
                foreach (var yearData in userYearlyData)
                {
                    // 计算盈亏百分比
                    decimal profitPercentage = 0;
                    if (yearData.InitialAsset != 0)
                    {
                        profitPercentage = yearData.PeriodProfit / yearData.InitialAsset * 100;
                    }

                    result[yearData.Year] = new YearlyProfitInfo
                    {
                        Year = yearData.Year,
                        PeriodProfit = yearData.PeriodProfit,
                        ProfitPercentage = profitPercentage
                    };
                }

                // 确保从2017年到当前年份都有数据
                int currentYear = DateTime.Now.Year;
                for (int year = 2017; year <= currentYear; year++)
                {
                    if (!result.ContainsKey(year))
                    {
                        result[year] = new YearlyProfitInfo
                        {
                            Year = year,
                            PeriodProfit = 0,
                            ProfitPercentage = 0
                        };
                    }
                }

                LogHelper.LogInfo($"加载年度盈亏数据: 用户={userName}, 年份数量={result.Count}");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载年度盈亏数据失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 绘制年度盈亏柱状图
        /// </summary>
        private void DrawYearlyProfitChart(Dictionary<int, YearlyProfitInfo> data)
        {
            // 清空画布
            YearlyProfitCanvas.Children.Clear();

            // 获取画布尺寸
            double canvasWidth = YearlyProfitCanvas.ActualWidth;
            double canvasHeight = YearlyProfitCanvas.ActualHeight;

            // 如果画布尺寸为0，设置默认值
            if (canvasWidth <= 0) canvasWidth = 800;
            if (canvasHeight <= 0) canvasHeight = 300;

            // 设置图表边距
            double marginLeft = 60;
            double marginRight = 20;
            double marginTop = 20;
            double marginBottom = 40;

            // 计算图表区域
            double chartWidth = canvasWidth - marginLeft - marginRight;
            double chartHeight = canvasHeight - marginTop - marginBottom;

            // 绘制坐标轴
            DrawAxes(YearlyProfitCanvas, marginLeft, marginTop, chartWidth, chartHeight);

            // 获取年度数据，按年份排序
            var yearlyData = data.Values.OrderBy(d => d.Year).ToList();

            // 找出最大值和最小值
            decimal maxProfit = yearlyData.Max(d => d.PeriodProfit);
            decimal minProfit = yearlyData.Min(d => d.PeriodProfit);
            decimal absMaxProfit = Math.Max(Math.Abs(maxProfit), Math.Abs(minProfit));

            // 确保有一个合理的范围
            if (absMaxProfit == 0) absMaxProfit = 1000;

            // 计算比例
            double yScale = chartHeight / (double)(absMaxProfit * 2);
            double barWidth = chartWidth / yearlyData.Count * 0.6; // 柱宽为间距的60%
            double barSpacing = chartWidth / yearlyData.Count;

            // 绘制年份标签和网格线
            for (int i = 0; i < yearlyData.Count; i++)
            {
                double x = marginLeft + i * barSpacing + barSpacing / 2;

                // 绘制年份标签
                TextBlock yearLabel = new TextBlock
                {
                    Text = yearlyData[i].Year.ToString(),
                    Foreground = _textBrush,
                    TextAlignment = TextAlignment.Center,
                    Width = barWidth
                };
                Canvas.SetLeft(yearLabel, x - barWidth / 2);
                Canvas.SetTop(yearLabel, marginTop + chartHeight + 10);
                YearlyProfitCanvas.Children.Add(yearLabel);

                // 绘制垂直网格线
                Line gridLine = new Line
                {
                    X1 = x,
                    Y1 = marginTop,
                    X2 = x,
                    Y2 = marginTop + chartHeight,
                    Stroke = _gridBrush,
                    StrokeThickness = 1,
                    StrokeDashArray = new DoubleCollection { 4, 2 }
                };
                YearlyProfitCanvas.Children.Add(gridLine);
            }

            // 绘制水平网格线和数值标签
            int numHorizontalLines = 4; // 上下各2条线
            for (int i = 0; i <= numHorizontalLines; i++)
            {
                double y = marginTop + chartHeight / 2 - (i - numHorizontalLines / 2) * (chartHeight / numHorizontalLines);
                decimal value = (decimal)(i - numHorizontalLines / 2) * (absMaxProfit * 2 / numHorizontalLines);

                // 绘制水平网格线
                Line gridLine = new Line
                {
                    X1 = marginLeft,
                    Y1 = y,
                    X2 = marginLeft + chartWidth,
                    Y2 = y,
                    Stroke = _gridBrush,
                    StrokeThickness = 1,
                    StrokeDashArray = new DoubleCollection { 4, 2 }
                };
                YearlyProfitCanvas.Children.Add(gridLine);

                // 绘制数值标签
                TextBlock valueLabel = new TextBlock
                {
                    Text = value.ToString("N0"),
                    Foreground = _textBrush,
                    TextAlignment = TextAlignment.Right,
                    Width = marginLeft - 5
                };
                Canvas.SetLeft(valueLabel, 0);
                Canvas.SetTop(valueLabel, y - 10);
                YearlyProfitCanvas.Children.Add(valueLabel);
            }

            // 绘制柱状图
            for (int i = 0; i < yearlyData.Count; i++)
            {
                var yearData = yearlyData[i];
                decimal value = yearData.PeriodProfit;
                double barHeight = Math.Abs((double)value * yScale);
                double x = marginLeft + i * barSpacing + (barSpacing - barWidth) / 2;
                double y = marginTop + chartHeight / 2; // 从中间开始

                if (value >= 0)
                {
                    // 盈利，向上绘制红色柱子
                    Rectangle bar = new Rectangle
                    {
                        Width = barWidth,
                        Height = barHeight,
                        Fill = _profitBrush
                    };
                    Canvas.SetLeft(bar, x);
                    Canvas.SetTop(bar, y - barHeight);
                    YearlyProfitCanvas.Children.Add(bar);
                }
                else
                {
                    // 亏损，向下绘制绿色柱子
                    Rectangle bar = new Rectangle
                    {
                        Width = barWidth,
                        Height = barHeight,
                        Fill = _lossBrush
                    };
                    Canvas.SetLeft(bar, x);
                    Canvas.SetTop(bar, y);
                    YearlyProfitCanvas.Children.Add(bar);
                }

                // 绘制盈亏值标签
                if (value != 0)
                {
                    TextBlock valueLabel = new TextBlock
                    {
                        Text = value.ToString("N0"),
                        Foreground = value >= 0 ? _profitBrush : _lossBrush,
                        TextAlignment = TextAlignment.Center,
                        Width = barWidth
                    };
                    Canvas.SetLeft(valueLabel, x);
                    Canvas.SetTop(valueLabel, value >= 0 ? y - barHeight - 20 : y + barHeight + 5);
                    YearlyProfitCanvas.Children.Add(valueLabel);
                }

                // 绘制盈亏百分比标签
                if (yearData.ProfitPercentage != 0)
                {
                    TextBlock percentLabel = new TextBlock
                    {
                        Text = yearData.ProfitPercentage.ToString("F2") + "%",
                        Foreground = value >= 0 ? _profitBrush : _lossBrush,
                        TextAlignment = TextAlignment.Center,
                        Width = barWidth
                    };
                    Canvas.SetLeft(percentLabel, x);
                    Canvas.SetTop(percentLabel, value >= 0 ? y - barHeight - 40 : y + barHeight + 25);
                    YearlyProfitCanvas.Children.Add(percentLabel);
                }
            }
        }
    }

    /// <summary>
    /// 年度盈亏信息类
    /// </summary>
    public class YearlyProfitInfo
    {
        public int Year { get; set; }
        public decimal PeriodProfit { get; set; }
        public decimal ProfitPercentage { get; set; }
    }
}
