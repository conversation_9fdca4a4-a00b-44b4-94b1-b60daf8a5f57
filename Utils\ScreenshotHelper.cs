using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace StockCrawler.Utils
{
    /// <summary>
    /// 提供截屏功能的辅助类
    /// </summary>
    public static class ScreenshotHelper
    {
        /// <summary>
        /// 捕获指定UI元素的截图并复制到剪贴板
        /// </summary>
        /// <param name="element">要截图的UI元素</param>
        /// <returns>是否成功截图</returns>
        public static bool CaptureElementToClipboard(FrameworkElement element)
        {
            try
            {
                if (element == null)
                    return false;

                // 获取元素的实际大小
                double actualWidth = element.ActualWidth;
                double actualHeight = element.ActualHeight;

                if (actualWidth <= 0 || actualHeight <= 0)
                    return false;

                // 创建RenderTargetBitmap
                var renderTargetBitmap = new RenderTargetBitmap(
                    (int)actualWidth,
                    (int)actualHeight,
                    96, // DPI X
                    96, // DPI Y
                    PixelFormats.Pbgra32);

                // 准备视觉对象
                var visual = new DrawingVisual();
                using (var context = visual.RenderOpen())
                {
                    // 创建VisualBrush
                    var visualBrush = new VisualBrush(element);
                    context.DrawRectangle(
                        visualBrush,
                        null,
                        new Rect(0, 0, actualWidth, actualHeight));
                }

                // 渲染到位图
                renderTargetBitmap.Render(visual);

                // 将位图复制到剪贴板
                Clipboard.SetImage(renderTargetBitmap);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"截图失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 捕获指定UI元素的截图并保存到文件
        /// </summary>
        /// <param name="element">要截图的UI元素</param>
        /// <param name="filePath">保存路径</param>
        /// <returns>是否成功截图</returns>
        public static bool CaptureElementToFile(FrameworkElement element, string filePath)
        {
            try
            {
                if (element == null || string.IsNullOrEmpty(filePath))
                    return false;

                // 获取元素的实际大小
                double actualWidth = element.ActualWidth;
                double actualHeight = element.ActualHeight;

                if (actualWidth <= 0 || actualHeight <= 0)
                    return false;

                // 创建RenderTargetBitmap
                var renderTargetBitmap = new RenderTargetBitmap(
                    (int)actualWidth,
                    (int)actualHeight,
                    96, // DPI X
                    96, // DPI Y
                    PixelFormats.Pbgra32);

                // 准备视觉对象
                var visual = new DrawingVisual();
                using (var context = visual.RenderOpen())
                {
                    // 创建VisualBrush
                    var visualBrush = new VisualBrush(element);
                    context.DrawRectangle(
                        visualBrush,
                        null,
                        new Rect(0, 0, actualWidth, actualHeight));
                }

                // 渲染到位图
                renderTargetBitmap.Render(visual);

                // 创建PNG编码器
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderTargetBitmap));

                // 保存到文件
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    encoder.Save(stream);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"截图失败: {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 捕获指定UI元素的部分区域截图并复制到剪贴板
        /// </summary>
        /// <param name="element">要截图的UI元素</param>
        /// <param name="captureRightHalf">是否只截取右半部分</param>
        /// <returns>是否成功截图</returns>
        public static bool CaptureElementPartialToClipboard(FrameworkElement element, bool captureRightHalf = true)
        {
            try
            {
                if (element == null)
                    return false;

                // 获取元素的实际大小
                double actualWidth = element.ActualWidth;
                double actualHeight = element.ActualHeight;

                if (actualWidth <= 0 || actualHeight <= 0)
                    return false;

                // 计算截图区域
                double captureWidth = captureRightHalf ? actualWidth / 2 : actualWidth;
                double captureX = captureRightHalf ? actualWidth / 2 : 0;

                // 创建RenderTargetBitmap
                var renderTargetBitmap = new RenderTargetBitmap(
                    (int)captureWidth,
                    (int)actualHeight,
                    96, // DPI X
                    96, // DPI Y
                    PixelFormats.Pbgra32);

                // 准备视觉对象
                var visual = new DrawingVisual();
                using (var context = visual.RenderOpen())
                {
                    // 创建VisualBrush，并设置ViewBox和ViewPort来只显示指定部分
                    var visualBrush = new VisualBrush(element)
                    {
                        Viewbox = new Rect(captureX / actualWidth, 0, captureWidth / actualWidth, 1),
                        ViewboxUnits = BrushMappingMode.RelativeToBoundingBox,
                        Viewport = new Rect(0, 0, 1, 1),
                        ViewportUnits = BrushMappingMode.RelativeToBoundingBox
                    };

                    context.DrawRectangle(
                        visualBrush,
                        null,
                        new Rect(0, 0, captureWidth, actualHeight));
                }

                // 渲染到位图
                renderTargetBitmap.Render(visual);

                // 将位图复制到剪贴板
                Clipboard.SetImage(renderTargetBitmap);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"部分截图失败: {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 捕获指定UI元素的右侧一半区域截图并复制到剪贴板
        /// </summary>
        /// <param name="element">要截图的UI元素</param>
        /// <returns>是否成功截图</returns>
        public static bool CaptureRightHalfToClipboard(FrameworkElement element)
        {
            try
            {
                if (element == null)
                    return false;

                // 获取元素的实际大小
                double actualWidth = element.ActualWidth;
                double actualHeight = element.ActualHeight;

                if (actualWidth <= 0 || actualHeight <= 0)
                    return false;

                // 计算截图区域 - 只截取右侧一半
                double captureWidth = actualWidth / 2;
                double captureX = actualWidth / 2;

                // 创建RenderTargetBitmap，只截取右半部分的宽度
                var renderTargetBitmap = new RenderTargetBitmap(
                    (int)captureWidth,
                    (int)actualHeight,
                    96, // DPI X
                    96, // DPI Y
                    PixelFormats.Pbgra32);

                // 准备视觉对象
                var visual = new DrawingVisual();
                using (var context = visual.RenderOpen())
                {
                    // 创建VisualBrush，并设置ViewBox和ViewPort来只显示右半部分
                    var visualBrush = new VisualBrush(element)
                    {
                        Viewbox = new Rect(0.5, 0, 0.5, 1), // 固定取右半部分
                        ViewboxUnits = BrushMappingMode.RelativeToBoundingBox,
                        Viewport = new Rect(0, 0, 1, 1),
                        ViewportUnits = BrushMappingMode.RelativeToBoundingBox
                    };

                    context.DrawRectangle(
                        visualBrush,
                        null,
                        new Rect(0, 0, captureWidth, actualHeight));
                }

                // 渲染到位图
                renderTargetBitmap.Render(visual);

                // 将位图复制到剪贴板
                Clipboard.SetImage(renderTargetBitmap);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"右侧截图失败: {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 直接截取指定元素的高清截图并复制到剪贴板
        /// </summary>
        /// <param name="element">要截图的UI元素</param>
        /// <returns>是否成功截图</returns>
        public static bool CaptureElementDirectlyToClipboard(FrameworkElement element)
        {
            try
            {
                if (element == null)
                    return false;

                // 确保元素已经完成布局
                element.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                element.Arrange(new Rect(element.DesiredSize));
                element.UpdateLayout();

                // 获取元素的实际大小
                double actualWidth = element.ActualWidth;
                double actualHeight = element.ActualHeight;

                if (actualWidth <= 0 || actualHeight <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"元素尺寸无效: 宽度={actualWidth}, 高度={actualHeight}");
                    return false;
                }

                // 使用更高的DPI值以获得更清晰的图像
                double scaleFactor = 3.0; // 更高的缩放因子，提高分辨率
                double dpi = 288; // 更高的DPI值 (96 * 3)

                // 直接使用元素，不创建副本

                // 创建RenderTargetBitmap，使用更高的分辨率
                var renderTargetBitmap = new RenderTargetBitmap(
                    (int)(actualWidth * scaleFactor),
                    (int)(actualHeight * scaleFactor),
                    dpi, // DPI X
                    dpi, // DPI Y
                    PixelFormats.Pbgra32);

                // 直接渲染元素
                renderTargetBitmap.Render(element);

                // 将位图编码为PNG格式，保持高质量
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderTargetBitmap));

                // 将PNG图像保存到内存流
                using (var memoryStream = new MemoryStream())
                {
                    encoder.Save(memoryStream);
                    memoryStream.Position = 0;

                    // 从内存流创建BitmapImage
                    var bitmapImage = new BitmapImage();
                    bitmapImage.BeginInit();
                    bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                    bitmapImage.StreamSource = memoryStream;
                    bitmapImage.EndInit();
                    bitmapImage.Freeze(); // 重要：使图像可以跨线程使用

                    // 将高质量图像复制到剪贴板
                    Clipboard.SetImage(bitmapImage);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高清截图失败: {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 简单直接地截取指定元素的高清截图并复制到剪贴板
        /// </summary>
        /// <param name="element">要截图的UI元素</param>
        /// <returns>是否成功截图</returns>
        public static bool CaptureSimpleToClipboard(FrameworkElement element)
        {
            try
            {
                if (element == null)
                    return false;

                // 获取元素的实际大小
                double actualWidth = element.ActualWidth;
                double actualHeight = element.ActualHeight;

                if (actualWidth <= 0 || actualHeight <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"元素尺寸无效: 宽度={actualWidth}, 高度={actualHeight}");
                    return false;
                }

                // 使用更高的DPI值以获得更清晰的图像
                double scaleFactor = 3.0; // 更高的缩放因子，提高分辨率
                double dpi = 288; // 更高的DPI值 (96 * 3)

                // 创建RenderTargetBitmap，使用更高的分辨率
                var renderTargetBitmap = new RenderTargetBitmap(
                    (int)(actualWidth * scaleFactor),
                    (int)(actualHeight * scaleFactor),
                    dpi, // DPI X
                    dpi, // DPI Y
                    PixelFormats.Pbgra32);

                // 准备视觉对象
                var visual = new DrawingVisual();
                using (var context = visual.RenderOpen())
                {
                    // 创建VisualBrush
                    var visualBrush = new VisualBrush(element);
                    context.PushTransform(new ScaleTransform(scaleFactor, scaleFactor));
                    context.DrawRectangle(
                        visualBrush,
                        null,
                        new Rect(0, 0, actualWidth, actualHeight));
                }

                // 渲染到位图
                renderTargetBitmap.Render(visual);

                // 将位图直接复制到剪贴板，不进行额外处理
                Clipboard.SetImage(renderTargetBitmap);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"简单截图失败: {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 截取指定元素的高清截图，并适当扩大边距
        /// </summary>
        /// <param name="element">要截图的UI元素</param>
        /// <param name="padding">四周扩大的边距（像素）</param>
        /// <returns>是否成功截图</returns>
        public static bool CaptureElementWithPaddingToClipboard(FrameworkElement element, int padding = 10)
        {
            try
            {
                if (element == null)
                    return false;

                // 获取元素的实际大小
                double actualWidth = element.ActualWidth;
                double actualHeight = element.ActualHeight;

                if (actualWidth <= 0 || actualHeight <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"元素尺寸无效: 宽度={actualWidth}, 高度={actualHeight}");
                    return false;
                }

                // 使用更高的DPI值以获得更清晰的图像
                double scaleFactor = 3.0; // 更高的缩放因子，提高分辨率
                double dpi = 288; // 更高的DPI值 (96 * 3)

                // 计算带边距的尺寸
                double paddedWidth = actualWidth + (padding * 2);
                double paddedHeight = actualHeight + (padding * 2);

                // 创建RenderTargetBitmap，使用更高的分辨率
                var renderTargetBitmap = new RenderTargetBitmap(
                    (int)(paddedWidth * scaleFactor),
                    (int)(paddedHeight * scaleFactor),
                    dpi, // DPI X
                    dpi, // DPI Y
                    PixelFormats.Pbgra32);

                // 准备视觉对象
                var visual = new DrawingVisual();
                using (var context = visual.RenderOpen())
                {
                    // 创建背景
                    context.DrawRectangle(
                        Brushes.White,
                        null,
                        new Rect(0, 0, paddedWidth * scaleFactor, paddedHeight * scaleFactor));

                    // 创建VisualBrush
                    var visualBrush = new VisualBrush(element);

                    // 应用变换：缩放和平移（考虑边距）
                    context.PushTransform(new ScaleTransform(scaleFactor, scaleFactor));
                    context.PushTransform(new TranslateTransform(padding, padding));

                    // 绘制元素
                    context.DrawRectangle(
                        visualBrush,
                        null,
                        new Rect(0, 0, actualWidth, actualHeight));
                }

                // 渲染到位图
                renderTargetBitmap.Render(visual);

                // 将位图编码为PNG格式，保持高质量
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderTargetBitmap));

                // 将PNG图像保存到内存流
                using (var memoryStream = new MemoryStream())
                {
                    encoder.Save(memoryStream);
                    memoryStream.Position = 0;

                    // 从内存流创建BitmapImage
                    var bitmapImage = new BitmapImage();
                    bitmapImage.BeginInit();
                    bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                    bitmapImage.StreamSource = memoryStream;
                    bitmapImage.EndInit();
                    bitmapImage.Freeze(); // 重要：使图像可以跨线程使用

                    // 将高质量图像复制到剪贴板
                    Clipboard.SetImage(bitmapImage);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"带边距截图失败: {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 直接截取指定元素的高清截图，使用更可靠的方法
        /// </summary>
        /// <param name="element">要截图的UI元素</param>
        /// <returns>是否成功截图</returns>
        public static bool CaptureTotalProfitBorderToClipboard(FrameworkElement element)
        {
            try
            {
                if (element == null)
                    return false;

                // 获取元素的实际大小
                double actualWidth = element.ActualWidth;
                double actualHeight = element.ActualHeight;

                if (actualWidth <= 0 || actualHeight <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"元素尺寸无效: 宽度={actualWidth}, 高度={actualHeight}");
                    return false;
                }

                // 添加边距
                int padding = 20;
                double paddedWidth = actualWidth + (padding * 2);
                double paddedHeight = actualHeight + (padding * 2);

                // 创建一个新的位图，使用更高的DPI
                double dpi = 192;
                RenderTargetBitmap renderTarget = new RenderTargetBitmap(
                    (int)paddedWidth,
                    (int)paddedHeight,
                    dpi,
                    dpi,
                    PixelFormats.Pbgra32);

                // 创建一个DrawingVisual来绘制元素
                DrawingVisual drawingVisual = new DrawingVisual();
                using (DrawingContext drawingContext = drawingVisual.RenderOpen())
                {
                    // 绘制白色背景
                    drawingContext.DrawRectangle(
                        Brushes.White,
                        null,
                        new Rect(0, 0, paddedWidth, paddedHeight));

                    // 创建一个VisualBrush，使用元素作为源
                    VisualBrush visualBrush = new VisualBrush(element);

                    // 在DrawingContext中绘制元素，考虑边距
                    drawingContext.DrawRectangle(
                        visualBrush,
                        null,
                        new Rect(padding, padding, actualWidth, actualHeight));
                }

                // 渲染DrawingVisual到位图
                renderTarget.Render(drawingVisual);

                // 将位图复制到剪贴板
                Clipboard.SetImage(renderTarget);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"截图失败: {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 最简单直接的方式截取元素并复制到剪贴板
        /// </summary>
        /// <param name="element">要截图的UI元素</param>
        /// <returns>是否成功截图</returns>
        public static bool CaptureSimpleElementToClipboard(FrameworkElement element)
        {
            try
            {
                if (element == null)
                    return false;

                // 获取元素的实际大小
                double actualWidth = element.ActualWidth;
                double actualHeight = element.ActualHeight;

                if (actualWidth <= 0 || actualHeight <= 0)
                    return false;

                // 创建RenderTargetBitmap
                var renderTargetBitmap = new RenderTargetBitmap(
                    (int)actualWidth,
                    (int)actualHeight,
                    96, // DPI X
                    96, // DPI Y
                    PixelFormats.Pbgra32);

                // 直接渲染元素
                renderTargetBitmap.Render(element);

                // 将位图复制到剪贴板
                Clipboard.SetImage(renderTargetBitmap);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"简单截图失败: {ex.Message}");
                return false;
            }
        }
    }
}
