﻿#pragma checksum "..\..\..\DailyProfitCalculatorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "963DEC34B78C42C2FD4B867629C7CEFB7190337D"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using StockCrawler;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace StockCrawler {
    
    
    /// <summary>
    /// DailyProfitCalculatorWindow
    /// </summary>
    public partial class DailyProfitCalculatorWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 165 "..\..\..\DailyProfitCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbUserName;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\DailyProfitCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbYear;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\DailyProfitCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbMonth;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\DailyProfitCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel DaysPanel;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\DailyProfitCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtMonthlyTotal;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\DailyProfitCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtMonthToDateTotal;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\DailyProfitCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSave;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\DailyProfitCalculatorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/StockCrawler;component/dailyprofitcalculatorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\DailyProfitCalculatorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\DailyProfitCalculatorWindow.xaml"
            ((StockCrawler.DailyProfitCalculatorWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.Window_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 129 "..\..\..\DailyProfitCalculatorWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 133 "..\..\..\DailyProfitCalculatorWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.cmbUserName = ((System.Windows.Controls.ComboBox)(target));
            
            #line 166 "..\..\..\DailyProfitCalculatorWindow.xaml"
            this.cmbUserName.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbUserName_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.cmbYear = ((System.Windows.Controls.ComboBox)(target));
            
            #line 170 "..\..\..\DailyProfitCalculatorWindow.xaml"
            this.cmbYear.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbYear_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.cmbMonth = ((System.Windows.Controls.ComboBox)(target));
            
            #line 174 "..\..\..\DailyProfitCalculatorWindow.xaml"
            this.cmbMonth.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbMonth_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.DaysPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 8:
            this.txtMonthlyTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.txtMonthToDateTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.btnSave = ((System.Windows.Controls.Button)(target));
            
            #line 205 "..\..\..\DailyProfitCalculatorWindow.xaml"
            this.btnSave.Click += new System.Windows.RoutedEventHandler(this.btnSave_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 207 "..\..\..\DailyProfitCalculatorWindow.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.btnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

