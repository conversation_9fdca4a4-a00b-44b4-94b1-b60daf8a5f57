using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using StockCrawler.Models;
using StockCrawler.Services;

namespace StockCrawler
{
    /// <summary>
    /// UserStockSettingsWindow.xaml 的交互逻辑
    /// </summary>
    public partial class UserStockSettingsWindow : Window
    {
        private readonly UserStockService _userStockService;
        private ObservableCollection<UserStock> _userStocks;
        private UserStock? _currentEditingStock;
        private bool _isEditing = false;

        public UserStockSettingsWindow()
        {
            InitializeComponent();

            // 初始化服务
            _userStockService = new UserStockService();

            // 加载用户自选股数据
            _userStocks = _userStockService.LoadUserStocks();
            UserStocksDataGrid.ItemsSource = _userStocks;

            // 清空表单
            ClearForm();
        }

        /// <summary>
        /// 清空表单
        /// </summary>
        private void ClearForm()
        {
            CodeTextBox.Text = string.Empty;
            NameTextBox.Text = string.Empty;
            SharesTextBox.Text = "0";
            CostPriceTextBox.Text = "0.00";
            _currentEditingStock = null;
            _isEditing = false;
            CodeTextBox.IsEnabled = true;
        }

        /// <summary>
        /// 验证表单
        /// </summary>
        private bool ValidateForm()
        {
            // 验证股票代码
            if (string.IsNullOrWhiteSpace(CodeTextBox.Text))
            {
                MessageBox.Show("请输入股票代码", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                CodeTextBox.Focus();
                return false;
            }

            // 验证股票名称
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("请输入股票名称", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            // 验证持股数量
            if (!int.TryParse(SharesTextBox.Text, out int shares) || shares < 0)
            {
                MessageBox.Show("请输入有效的持股数量（非负整数）", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                SharesTextBox.Focus();
                return false;
            }

            // 验证持仓成本
            if (!decimal.TryParse(CostPriceTextBox.Text, out decimal costPrice) || costPrice < 0)
            {
                MessageBox.Show("请输入有效的持仓成本（非负数）", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                CostPriceTextBox.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证表单
                if (!ValidateForm())
                    return;

                // 获取表单数据
                string code = CodeTextBox.Text.Trim();
                string name = NameTextBox.Text.Trim();
                int shares = int.Parse(SharesTextBox.Text.Trim());
                decimal costPrice = decimal.Parse(CostPriceTextBox.Text.Trim());

                if (_isEditing && _currentEditingStock != null)
                {
                    // 更新现有股票
                    _currentEditingStock.Code = code;
                    _currentEditingStock.Name = name;
                    _currentEditingStock.Shares = shares;
                    _currentEditingStock.CostPrice = costPrice;

                    // 保存到文件
                    _userStockService.SaveUserStocks(_userStocks);

                    Debug.WriteLine($"更新自选股: {code}, {name}, {shares}, {costPrice}");
                }
                else
                {
                    // 添加新股票
                    var newStock = new UserStock(code, name, shares, costPrice);
                    _userStockService.AddUserStock(_userStocks, newStock);

                    Debug.WriteLine($"添加自选股: {code}, {name}, {shares}, {costPrice}");
                }

                // 清空表单
                ClearForm();

                // 重新加载数据并刷新数据网格
                _userStocks = _userStockService.LoadUserStocks();
                UserStocksDataGrid.ItemsSource = _userStocks;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存自选股时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Debug.WriteLine($"保存自选股异常: {ex}");
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is UserStock stock)
            {
                // 设置当前编辑的股票
                _currentEditingStock = stock;
                _isEditing = true;

                // 填充表单
                CodeTextBox.Text = stock.Code;
                NameTextBox.Text = stock.Name;
                SharesTextBox.Text = stock.Shares.ToString();
                CostPriceTextBox.Text = stock.CostPrice.ToString("F2");

                // 禁用股票代码输入框（不允许修改股票代码）
                CodeTextBox.IsEnabled = false;
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is UserStock stock)
            {
                // 确认删除
                var result = MessageBox.Show($"确定要删除自选股 {stock.Name}({stock.Code}) 吗？", "确认删除",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // 从集合中删除股票
                        _userStocks.Remove(stock);
                        
                        // 保存到CSV文件
                        _userStockService.SaveUserStocks(_userStocks);

                        // 刷新UI
                        UserStocksDataGrid.Items.Refresh();
                        
                        // 如果正在编辑被删除的股票，清空表单
                        if (_currentEditingStock != null && _currentEditingStock.Code == stock.Code)
                        {
                            ClearForm();
                        }

                        Debug.WriteLine($"删除自选股: {stock.Code}, {stock.Name}");
                        
                        // 显示操作成功的提示
                        MessageBox.Show($"已成功删除自选股 {stock.Name}({stock.Code})", "删除成功", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"删除自选股时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        Debug.WriteLine($"删除自选股异常: {ex}");
                    }
                }
            }
        }

        /// <summary>
        /// 数据网格选择变更事件
        /// </summary>
        private void UserStocksDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (UserStocksDataGrid.SelectedItem is UserStock stock)
            {
                // 设置当前编辑的股票
                _currentEditingStock = stock;
                _isEditing = true;

                // 填充表单
                CodeTextBox.Text = stock.Code;
                NameTextBox.Text = stock.Name;
                SharesTextBox.Text = stock.Shares.ToString();
                CostPriceTextBox.Text = stock.CostPrice.ToString("F2");

                // 禁用股票代码输入框（不允许修改股票代码）
                CodeTextBox.IsEnabled = false;
            }
            else
            {
                // 如果没有选中项，重置表单
                if (!_isEditing)
                {
                    ClearForm();
                    CodeTextBox.IsEnabled = true;
                }
            }
        }

        /// <summary>
        /// 标题栏拖动事件
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
