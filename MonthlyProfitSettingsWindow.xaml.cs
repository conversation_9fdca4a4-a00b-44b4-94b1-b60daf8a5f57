using StockCrawler.Models;
using StockCrawler.Services;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace StockCrawler
{
    /// <summary>
    /// MonthlyProfitSettingsWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MonthlyProfitSettingsWindow : Window
    {
        private MonthlyProfitData _currentData;
        private bool _isInitializing = false;

        public MonthlyProfitSettingsWindow()
        {
            InitializeComponent();
            _currentData = new MonthlyProfitData();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 加载用户列表
            LoadUserList();

            // 默认选择当前用户
            string defaultUserName = Environment.UserName;
            if (cmbUserName.Items.Contains(defaultUserName))
            {
                cmbUserName.SelectedItem = defaultUserName;
            }
            else if (cmbUserName.Items.Count > 0)
            {
                cmbUserName.SelectedIndex = 0;
            }
            else
            {
                // 如果没有用户，添加当前用户
                cmbUserName.Items.Add(defaultUserName);
                cmbUserName.SelectedItem = defaultUserName;
                LoadUserData(defaultUserName);
            }
        }

        /// <summary>
        /// 加载用户列表
        /// </summary>
        private void LoadUserList()
        {
            try
            {
                // 清空列表
                cmbUserName.Items.Clear();

                // 加载所有用户数据
                var allUserData = MonthlyProfitService.LoadMonthlyProfitData();

                // 添加到下拉列表
                foreach (var userData in allUserData)
                {
                    cmbUserName.Items.Add(userData.UserName);
                }

                LogHelper.LogInfo($"已加载 {cmbUserName.Items.Count} 个用户");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载用户列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 标题栏拖动事件
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // 双击最大化/还原窗口
                if (WindowState == WindowState.Maximized)
                    WindowState = WindowState.Normal;
                else
                    WindowState = WindowState.Maximized;
            }
            else
            {
                // 单击拖动窗口
                DragMove();
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void LoadUserData(string userName)
        {
            try
            {
                _isInitializing = true;
                _currentData = MonthlyProfitService.GetOrCreateUserData(userName);

                // 更新UI
                UpdateUI();
                _isInitializing = false;

                LogHelper.LogInfo($"已加载用户 {userName} 的月度盈亏数据");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载用户数据失败: {ex.Message}");
                MessageBox.Show($"加载用户数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateUI()
        {
            // 更新月度数据输入框
            UpdateMonthTextBox(txtJanuary, _currentData.January);
            UpdateMonthTextBox(txtFebruary, _currentData.February);
            UpdateMonthTextBox(txtMarch, _currentData.March);
            UpdateMonthTextBox(txtApril, _currentData.April);
            UpdateMonthTextBox(txtMay, _currentData.May);
            UpdateMonthTextBox(txtJune, _currentData.June);
            UpdateMonthTextBox(txtJuly, _currentData.July);
            UpdateMonthTextBox(txtAugust, _currentData.August);
            UpdateMonthTextBox(txtSeptember, _currentData.September);
            UpdateMonthTextBox(txtOctober, _currentData.October);
            UpdateMonthTextBox(txtNovember, _currentData.November);
            UpdateMonthTextBox(txtDecember, _currentData.December);

            // 更新总计
            UpdateTotals();
        }

        /// <summary>
        /// 更新月度编辑框的值和样式
        /// </summary>
        private void UpdateMonthTextBox(TextBox textBox, decimal value)
        {
            // 设置文本值
            textBox.Text = value.ToString("F2");

            // 根据盈亏值设置样式
            if (value >= 0)
            {
                // 盈利，使用红色样式
                textBox.Style = (Style)FindResource("ProfitTextBoxStyle");
            }
            else
            {
                // 亏损，使用绿色样式
                textBox.Style = (Style)FindResource("LossTextBoxStyle");
            }
        }

        private void UpdateTotals()
        {
            // 更新年度总计
            decimal yearlyTotal = _currentData.YearlyTotal;
            txtYearlyTotal.Text = yearlyTotal.ToString("N2");
            txtYearlyTotal.Foreground = yearlyTotal >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);

            // 更新截止当前月份总计
            decimal yearToDateTotal = _currentData.YearToDateTotal;
            txtYearToDateTotal.Text = yearToDateTotal.ToString("N2");
            txtYearToDateTotal.Foreground = yearToDateTotal >= 0 ? new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Green);
        }

        private void UpdateDataFromUI()
        {
            try
            {
                // 从UI更新数据模型
                if (decimal.TryParse(txtJanuary.Text, out decimal jan)) _currentData.January = jan;
                if (decimal.TryParse(txtFebruary.Text, out decimal feb)) _currentData.February = feb;
                if (decimal.TryParse(txtMarch.Text, out decimal mar)) _currentData.March = mar;
                if (decimal.TryParse(txtApril.Text, out decimal apr)) _currentData.April = apr;
                if (decimal.TryParse(txtMay.Text, out decimal may)) _currentData.May = may;
                if (decimal.TryParse(txtJune.Text, out decimal jun)) _currentData.June = jun;
                if (decimal.TryParse(txtJuly.Text, out decimal jul)) _currentData.July = jul;
                if (decimal.TryParse(txtAugust.Text, out decimal aug)) _currentData.August = aug;
                if (decimal.TryParse(txtSeptember.Text, out decimal sep)) _currentData.September = sep;
                if (decimal.TryParse(txtOctober.Text, out decimal oct)) _currentData.October = oct;
                if (decimal.TryParse(txtNovember.Text, out decimal nov)) _currentData.November = nov;
                if (decimal.TryParse(txtDecember.Text, out decimal dec)) _currentData.December = dec;

                // 更新总计
                UpdateTotals();
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"更新数据失败: {ex.Message}");
            }
        }

        private void MonthValue_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            // 更新数据
            UpdateDataFromUI();

            // 更新当前编辑框的样式
            if (sender is TextBox textBox)
            {
                if (decimal.TryParse(textBox.Text, out decimal value))
                {
                    // 根据盈亏值设置样式
                    if (value >= 0)
                    {
                        // 盈利，使用红色样式
                        textBox.Style = (Style)FindResource("ProfitTextBoxStyle");
                    }
                    else
                    {
                        // 亏损，使用绿色样式
                        textBox.Style = (Style)FindResource("LossTextBoxStyle");
                    }
                }
            }
        }

        private void btnLoadUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 禁用按钮，防止重复点击
                btnLoadUser.IsEnabled = false;
                btnLoadUser.Content = "加载中...";

                string userName = cmbUserName.SelectedItem?.ToString();
                if (string.IsNullOrEmpty(userName))
                {
                    MessageBox.Show("请选择用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                LoadUserData(userName);
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载用户数据失败: {ex.Message}");
                MessageBox.Show($"加载用户数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnLoadUser.IsEnabled = true;
                btnLoadUser.Content = "加载";
            }
        }

        /// <summary>
        /// 用户选择改变事件
        /// </summary>
        private void cmbUserName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            string userName = cmbUserName.SelectedItem?.ToString();
            if (!string.IsNullOrEmpty(userName))
            {
                LoadUserData(userName);
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 禁用按钮，防止重复点击
                btnSave.IsEnabled = false;
                btnSave.Content = "保存中...";

                string userName = cmbUserName.SelectedItem?.ToString();
                if (string.IsNullOrEmpty(userName))
                {
                    MessageBox.Show("请选择用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 更新用户名
                _currentData.UserName = userName;

                // 保存数据
                MonthlyProfitService.UpdateUserData(_currentData);

                MessageBox.Show("保存成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);

                // 关闭窗口
                DialogResult = true;
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"保存数据失败: {ex.Message}");
                MessageBox.Show($"保存数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnSave.IsEnabled = true;
                btnSave.Content = "保存";
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 每日盈亏计算按钮点击事件
        /// </summary>
        private void btnCalculate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 禁用按钮，防止重复点击
                btnCalculate.IsEnabled = false;

                // 创建并显示每日盈亏计算窗口
                DailyProfitCalculatorWindow window = new DailyProfitCalculatorWindow();
                window.Owner = this;
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"打开每日盈亏计算窗口失败: {ex.Message}");
                MessageBox.Show($"打开每日盈亏计算窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnCalculate.IsEnabled = true;
            }
        }

        /// <summary>
        /// 统计按钮点击事件
        /// </summary>
        private void btnStatistics_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 禁用按钮，防止重复点击
                btnStatistics.IsEnabled = false;

                // 创建并显示盈亏统计窗口
                ProfitStatisticsWindow window = new ProfitStatisticsWindow();
                window.Owner = this;
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"打开盈亏统计窗口失败: {ex.Message}");
                MessageBox.Show($"打开盈亏统计窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnStatistics.IsEnabled = true;
            }
        }

        /// <summary>
        /// 年度盈亏设置按钮点击事件
        /// </summary>
        private void btnYearlyProfit_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 禁用按钮，防止重复点击
                btnYearlyProfit.IsEnabled = false;

                // 创建并显示年度盈亏设置窗口
                YearlyProfitSettingsWindow window = new YearlyProfitSettingsWindow();
                window.Owner = this;
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"打开年度盈亏设置窗口失败: {ex.Message}");
                MessageBox.Show($"打开年度盈亏设置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnYearlyProfit.IsEnabled = true;
            }
        }
    }
}
