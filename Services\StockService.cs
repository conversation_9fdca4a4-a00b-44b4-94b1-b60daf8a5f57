using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Net;
using System.Text;
using StockCrawler.Models;

namespace StockCrawler.Services
{
    public class StockService
    {
        private readonly HttpClient _httpClient;
        private readonly Dictionary<string, string> _stockNameMap;

        public StockService()
        {
            // 配置HttpClient，绕过一些反爬虫机制
            var handler = new HttpClientHandler
            {
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                UseCookies = true,
                CookieContainer = new CookieContainer()
            };

            _httpClient = new HttpClient(handler);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
            _httpClient.DefaultRequestHeaders.Add("Cache-Control", "max-age=0");
            _httpClient.DefaultRequestHeaders.Add("Connection", "keep-alive");
            _httpClient.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");

            // 初始化股票代码和名称的映射
            _stockNameMap = new Dictionary<string, string>();

            // 从用户自选股服务加载股票
            LoadUserStocks();

            // 打印调试信息
            Debug.WriteLine("StockService initialized");
        }

        public async Task<List<Stock>> GetStocksDataAsync()
        {
            // 每次获取数据前重新加载用户自选股
            LoadUserStocks();

            List<Stock> stocks = new List<Stock>();
            var tasks = new List<Task<Stock>>();

            // 并行发起所有请求
            foreach (var stockCode in _stockNameMap.Keys)
            {
                tasks.Add(Task.Run(async () => {
                    try
                    {
                        Debug.WriteLine($"开始获取股票 {stockCode} ({_stockNameMap[stockCode]}) 的数据");
                        var stock = await GetStockDataAsync(stockCode, _stockNameMap[stockCode]);
                        
                        if (stock != null)
                        {
                            if (stock.CurrentPrice > 0)
                            {
                                Debug.WriteLine($"成功获取股票 {stockCode} 数据: 价格={stock.CurrentPrice}, 涨跌幅={stock.PercentChange}");
                            }
                            else
                            {
                                Debug.WriteLine($"获取股票 {stockCode} 数据失败: 价格为0");
                            }
                        }
                        return stock;
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"获取股票 {stockCode} 数据时出错: {ex.Message}");
                        // 创建一个带有错误信息的股票对象
                        return new Stock
                        {
                            Code = stockCode,
                            Name = _stockNameMap[stockCode],
                            CurrentPrice = 0,
                            PriceChange = 0,
                            PercentChange = "获取失败"
                        };
                    }
                }));
            }

            // 等待所有任务完成并按添加顺序收集结果
            var results = await Task.WhenAll(tasks);
            stocks.AddRange(results);

            return stocks;
        }

        private async Task<Stock> GetStockDataAsync(string code, string name)
        {
            // 创建股票对象
            Stock stock = new Stock
            {
                Code = code,
                Name = name,
                CurrentPrice = 0,
                PriceChange = 0,
                PercentChange = "0.00%"
            };

            try 
            {
                // 创建多个数据源获取任务
                Task<Stock> tencentTask = TryGetStockDataFromTencentAsync(stock);
                Task<Stock> sinaTask = GetStockDataFromSinaAsync(new Stock { 
                    Code = stock.Code, 
                    Name = stock.Name,
                    CurrentPrice = 0,
                    PriceChange = 0,
                    PercentChange = "0.00%" 
                });
                Task<Stock> eastMoneyTask = TryGetStockDataFromEastMoneyAsync(new Stock { 
                    Code = stock.Code, 
                    Name = stock.Name,
                    CurrentPrice = 0,
                    PriceChange = 0,
                    PercentChange = "0.00%" 
                });
                Task<Stock> xueqiuTask = TryGetStockDataFromXueqiuAsync(new Stock { 
                    Code = stock.Code, 
                    Name = stock.Name,
                    CurrentPrice = 0,
                    PriceChange = 0,
                    PercentChange = "0.00%" 
                });

                // 等待所有任务完成
                await Task.WhenAll(tencentTask, sinaTask, eastMoneyTask, xueqiuTask);

                // 优先使用腾讯数据
                Stock tencentStock = await tencentTask;
                if (tencentStock.CurrentPrice > 0)
                    return tencentStock;
                
                // 检查各数据源的结果，按优先级选择
                Stock sinaStock = await sinaTask;
                if (sinaStock.CurrentPrice > 0)
                    return sinaStock;
                
                Stock eastMoneyStock = await eastMoneyTask;
                if (eastMoneyStock.CurrentPrice > 0)
                    return eastMoneyStock;
                
                Stock xueqiuStock = await xueqiuTask;
                if (xueqiuStock.CurrentPrice > 0)
                    return xueqiuStock;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取股票数据时出错: {ex.Message}");
            }

            // 所有数据源都失败，返回原始对象
            return stock;
        }

        private async Task<Stock> GetStockDataFromSinaAsync(Stock stock)
        {
            try
            {
                // 根据股票代码确定前缀
                string sinaPrefix = GetSinaStockPrefix(stock.Code);
                string sinaCode = $"{sinaPrefix}{stock.Code}";

                // 新浪财经API
                string sinaUrl = $"https://hq.sinajs.cn/list={sinaCode}";

                // 创建新的请求，避免请求头冲突
                using (var request = new HttpRequestMessage(HttpMethod.Get, sinaUrl))
                {
                    // 添加必要的请求头
                    request.Headers.Add("Referer", "https://finance.sina.com.cn/");
                    request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");

                    // 发送请求
                    var response = await _httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();

                    // 获取响应内容
                    string responseContent = await response.Content.ReadAsStringAsync();
                    Debug.WriteLine($"新浪财经响应: {responseContent}");

                    // 解析新浪财经返回的数据
                    // 格式通常为：var hq_str_sh600298="安琪酵母,29.800,29.800,30.000,30.200,29.700,30.000,30.010,8591931,257516375.000,13100,30.000,15200,29.990,8800,29.980,16700,29.970,12800,29.960,35100,30.010,19000,30.020,25700,30.030,17400,30.040,19900,30.050,2023-04-25,15:00:00,00,";
                    Match sinaMatch = Regex.Match(responseContent, @"var hq_str_\w+=""([^""]+)""");
                    if (sinaMatch.Success && sinaMatch.Groups.Count > 1)
                    {
                        string[] values = sinaMatch.Groups[1].Value.Split(',');
                        if (values.Length > 3)
                        {
                            // 当前价格通常是第4个值（索引3）
                            if (decimal.TryParse(values[3], out decimal currentPrice))
                            {
                                stock.CurrentPrice = currentPrice;

                                // 计算涨跌额和涨跌幅
                                if (decimal.TryParse(values[2], out decimal yesterdayClose))
                                {
                                    decimal priceChange = currentPrice - yesterdayClose;
                                    decimal percentChange = (priceChange / yesterdayClose) * 100;

                                    stock.PriceChange = priceChange;
                                    stock.PercentChange = $"{percentChange:F2}%";

                                    Debug.WriteLine($"新浪财经计算结果: 当前价={currentPrice}, 昨收={yesterdayClose}, 涨跌额={priceChange}, 涨跌幅={percentChange}%");
                                }
                                else
                                {
                                    Debug.WriteLine($"新浪财经无法解析昨收价: {values[2]}");
                                }
                            }
                            else
                            {
                                Debug.WriteLine($"新浪财经无法解析当前价格: {values[3]}");
                            }
                        }
                        else
                        {
                            Debug.WriteLine($"新浪财经返回的数据格式不正确，数组长度不足: {values.Length}");
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"新浪财经返回的数据格式不正确，无法匹配: {responseContent}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从新浪财经获取股票 {stock.Code} 数据时出错: {ex.Message}");
            }
            
            return stock;
        }

        private async Task<Stock> TryGetStockDataFromXueqiuAsync(Stock stock)
        {
            try
            {
                // 根据股票代码确定前缀
                string prefix = GetXueqiuStockPrefix(stock.Code);
                string fullCode = $"{prefix}{stock.Code}";

                // 雪球网API URL (使用API而不是HTML页面)
                string url = $"https://stock.xueqiu.com/v5/stock/quote.json?symbol={fullCode}&extend=detail";

                // 创建新的请求，避免请求头冲突
                using (var request = new HttpRequestMessage(HttpMethod.Get, url))
                {
                    // 添加必要的请求头
                    request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                    request.Headers.Add("Accept", "application/json");
                    request.Headers.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                    request.Headers.Add("Referer", "https://xueqiu.com/");

                    // 发送请求
                    var response = await _httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();

                    // 获取响应内容
                    string jsonContent = await response.Content.ReadAsStringAsync();
                    Debug.WriteLine($"雪球网API响应: {jsonContent}");

                    // 提取当前价格
                    Match currentMatch = Regex.Match(jsonContent, @"""current"":([0-9.]+)");
                    // 提取昨收价
                    Match closeMatch = Regex.Match(jsonContent, @"""last_close"":([0-9.]+)");

                    if (currentMatch.Success && currentMatch.Groups.Count > 1 &&
                        closeMatch.Success && closeMatch.Groups.Count > 1)
                    {
                        if (decimal.TryParse(currentMatch.Groups[1].Value, out decimal currentPrice) &&
                            decimal.TryParse(closeMatch.Groups[1].Value, out decimal yesterdayClose))
                        {
                            stock.CurrentPrice = currentPrice;

                            // 手动计算涨跌额和涨跌幅
                            decimal priceChange = currentPrice - yesterdayClose;
                            decimal percentChange = (priceChange / yesterdayClose) * 100;

                            stock.PriceChange = priceChange;
                            stock.PercentChange = $"{percentChange:F2}%";

                            Debug.WriteLine($"雪球网计算结果: 当前价={currentPrice}, 昨收={yesterdayClose}, 涨跌额={priceChange}, 涨跌幅={percentChange}%");
                            return stock;
                        }
                    }

                    // 如果API方法失败，尝试从HTML页面获取数据
                    url = $"https://xueqiu.com/S/{fullCode}";

                    using (var htmlRequest = new HttpRequestMessage(HttpMethod.Get, url))
                    {
                        // 添加必要的请求头
                        htmlRequest.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                        htmlRequest.Headers.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
                        htmlRequest.Headers.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                        htmlRequest.Headers.Add("Referer", "https://xueqiu.com/");

                        // 发送请求
                        var htmlResponse = await _httpClient.SendAsync(htmlRequest);
                        htmlResponse.EnsureSuccessStatusCode();

                        // 获取响应内容
                        string htmlContent = await htmlResponse.Content.ReadAsStringAsync();
                        Debug.WriteLine($"雪球网HTML响应长度: {htmlContent.Length}");

                        // 尝试从HTML中提取股票数据
                        // 提取当前价格和昨收价
                        Match priceMatch = Regex.Match(htmlContent, @"current"":([0-9.]+)");
                        Match yesterdayMatch = Regex.Match(htmlContent, @"last_close"":([0-9.]+)");

                        if (priceMatch.Success && priceMatch.Groups.Count > 1 &&
                            yesterdayMatch.Success && yesterdayMatch.Groups.Count > 1)
                        {
                            if (decimal.TryParse(priceMatch.Groups[1].Value, out decimal currentPrice) &&
                                decimal.TryParse(yesterdayMatch.Groups[1].Value, out decimal yesterdayClose))
                            {
                                stock.CurrentPrice = currentPrice;

                                // 手动计算涨跌额和涨跌幅
                                decimal priceChange = currentPrice - yesterdayClose;
                                decimal percentChange = (priceChange / yesterdayClose) * 100;

                                stock.PriceChange = priceChange;
                                stock.PercentChange = $"{percentChange:F2}%";

                                Debug.WriteLine($"雪球网HTML计算结果: 当前价={currentPrice}, 昨收={yesterdayClose}, 涨跌额={priceChange}, 涨跌幅={percentChange}%");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从雪球网获取股票 {stock.Code} 数据时出错: {ex.Message}");
            }
            
            return stock;
        }

        private async Task<Stock> TryGetStockDataFromEastMoneyAsync(Stock stock)
        {
            try
            {
                // 东方财富网API
                string url = $"https://push2.eastmoney.com/api/qt/stock/get?secid={(stock.Code.StartsWith("6") ? "1" : "0")}.{stock.Code}&fields=f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63,f64,f65,f71,f80";

                // 创建新的请求，避免请求头冲突
                using (var request = new HttpRequestMessage(HttpMethod.Get, url))
                {
                    // 添加必要的请求头
                    request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                    request.Headers.Add("Referer", "https://quote.eastmoney.com/");

                    // 发送请求
                    var response = await _httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();

                    // 获取响应内容
                    string jsonContent = await response.Content.ReadAsStringAsync();
                    Debug.WriteLine($"东方财富响应: {jsonContent}");

                    // 解析JSON数据
                    // 提取当前价格 (f43)
                    Match priceMatch = Regex.Match(jsonContent, @"""f43"":([0-9.]+)");
                    // 提取昨收价 (f60)
                    Match closeMatch = Regex.Match(jsonContent, @"""f60"":([0-9.]+)");

                    if (priceMatch.Success && priceMatch.Groups.Count > 1 &&
                        closeMatch.Success && closeMatch.Groups.Count > 1)
                    {
                        if (decimal.TryParse(priceMatch.Groups[1].Value, out decimal currentPrice) &&
                            decimal.TryParse(closeMatch.Groups[1].Value, out decimal yesterdayClose))
                        {
                            stock.CurrentPrice = currentPrice;

                            // 手动计算涨跌额和涨跌幅
                            decimal priceChange = currentPrice - yesterdayClose;
                            decimal percentChange = (priceChange / yesterdayClose) * 100;

                            stock.PriceChange = priceChange;
                            stock.PercentChange = $"{percentChange:F2}%";

                            Debug.WriteLine($"东方财富计算结果: 当前价={currentPrice}, 昨收={yesterdayClose}, 涨跌额={priceChange}, 涨跌幅={percentChange}%");
                        }
                        else
                        {
                            Debug.WriteLine($"东方财富无法解析价格数据: 当前价={priceMatch.Groups[1].Value}, 昨收={closeMatch.Groups[1].Value}");
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"东方财富返回的数据格式不正确，无法匹配价格或昨收价");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从东方财富获取股票 {stock.Code} 数据时出错: {ex.Message}");
            }
            
            return stock;
        }

        private async Task<Stock> TryGetStockDataFromTencentAsync(Stock stock)
        {
            try
            {
                // 根据股票代码确定前缀
                string prefix = GetTencentStockPrefix(stock.Code);
                string fullCode = $"{prefix}{stock.Code}";

                // 腾讯证券API URL
                string url = $"https://qt.gtimg.cn/q={fullCode}";

                // 创建新的请求，避免请求头冲突
                using (var request = new HttpRequestMessage(HttpMethod.Get, url))
                {
                    // 添加必要的请求头
                    request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                    request.Headers.Add("Referer", "https://finance.sina.com.cn/");

                    // 发送请求
                    var response = await _httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();

                    // 获取响应内容
                    string responseContent = await response.Content.ReadAsStringAsync();
                    Debug.WriteLine($"腾讯证券响应: {responseContent}");

                    // 解析腾讯证券返回的数据
                    // 格式通常为：v_sz000651="51~格力电器~000651~36.77~36.93~36.94~417243~153664~263580~36.77~334~36.76~186~36.75~236~36.74~332~36.73~137~36.78~448~36.79~171~36.80~268~36.81~126~36.82~249~15:00:01/36.77/4695/S/17241815/24564|14:57:00/36.77/52/S/191204/24454|14:56:57/36.78/116/B/427079/24452|14:56:54/36.77/10/S/36770/24449|14:56:48/36.78/60/B/220639/24445~20230425150003~-0.16~-0.43~37.18~36.50~36.77/417243/1536599754~417243~153660~1.82~19.19~~37.18~36.50~1.84~1166.32~1166.94~2.93~40.29~33.28~";
                    Match tencentMatch = Regex.Match(responseContent, @"v_\w+=""([^""]+)""");
                    if (tencentMatch.Success && tencentMatch.Groups.Count > 1)
                    {
                        string[] values = tencentMatch.Groups[1].Value.Split('~');
                        if (values.Length > 4)
                        {
                            // 当前价格通常是第4个值（索引3）
                            if (decimal.TryParse(values[3], out decimal currentPrice))
                            {
                                stock.CurrentPrice = currentPrice;

                                // 涨跌额通常是倒数第3个值
                                if (decimal.TryParse(values[values.Length - 3], out decimal priceChange))
                                {
                                    stock.PriceChange = priceChange;
                                }

                                // 涨跌幅通常是倒数第2个值
                                if (values.Length > 2 && values[values.Length - 2].EndsWith("%"))
                                {
                                    stock.PercentChange = values[values.Length - 2];
                                }
                                else if (decimal.TryParse(values[values.Length - 2], out decimal percentChange))
                                {
                                    stock.PercentChange = $"{percentChange}%";
                                }

                                Debug.WriteLine($"腾讯证券解析结果: 当前价={currentPrice}, 涨跌额={stock.PriceChange}, 涨跌幅={stock.PercentChange}");
                            }
                            else
                            {
                                Debug.WriteLine($"腾讯证券无法解析当前价格: {values[3]}");
                            }
                        }
                        else
                        {
                            Debug.WriteLine($"腾讯证券返回的数据格式不正确，数组长度不足: {values.Length}");
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"腾讯证券返回的数据格式不正确，无法匹配: {responseContent}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从腾讯证券获取股票 {stock.Code} 数据时出错: {ex.Message}");
            }
            
            return stock;
        }

        /// <summary>
        /// 从用户自选股服务加载股票
        /// </summary>
        private void LoadUserStocks()
        {
            try
            {
                // 创建用户自选股服务
                var userStockService = new UserStockService();

                // 加载用户自选股
                var userStocks = userStockService.LoadUserStocks();

                // 清空现有映射
                _stockNameMap.Clear();

                // 添加用户自选股到映射
                foreach (var stock in userStocks)
                {
                    if (!_stockNameMap.ContainsKey(stock.Code))
                    {
                        _stockNameMap.Add(stock.Code, stock.Name);
                    }
                }

                Debug.WriteLine($"从用户自选股服务加载了 {_stockNameMap.Count} 只股票");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载用户自选股时出错: {ex.Message}");

                // 如果加载失败，使用默认股票
                _stockNameMap.Clear();
                _stockNameMap.Add("600298", "安琪酵母");
                _stockNameMap.Add("000516", "国际医学");
                _stockNameMap.Add("000021", "深科技");
                _stockNameMap.Add("000525", "ST红太阳");
                _stockNameMap.Add("000564", "供销大集");
            }
        }

        private string GetSinaStockPrefix(string code)
        {
            // 新浪财经的股票代码前缀
            if (code.StartsWith("6"))
            {
                return "sh"; // 上海证券交易所
            }
            else if (code.StartsWith("0") || code.StartsWith("3"))
            {
                return "sz"; // 深圳证券交易所
            }
            else if (code.StartsWith("4") || code.StartsWith("8"))
            {
                return "bj"; // 北京证券交易所
            }
            else
            {
                return "sh"; // 默认使用上海证券交易所
            }
        }

        private string GetTencentStockPrefix(string code)
        {
            // 腾讯证券的股票代码前缀
            if (code.StartsWith("6"))
            {
                return "sh"; // 上海证券交易所
            }
            else if (code.StartsWith("0") || code.StartsWith("3"))
            {
                return "sz"; // 深圳证券交易所
            }
            else if (code.StartsWith("4") || code.StartsWith("8"))
            {
                return "bj"; // 北京证券交易所
            }
            else
            {
                return "sh"; // 默认使用上海证券交易所
            }
        }

        private string GetXueqiuStockPrefix(string code)
        {
            // 雪球网的股票代码前缀
            if (code.StartsWith("6"))
            {
                return "SH"; // 上海证券交易所
            }
            else if (code.StartsWith("0") || code.StartsWith("3"))
            {
                return "SZ"; // 深圳证券交易所
            }
            else if (code.StartsWith("4") || code.StartsWith("8"))
            {
                return "BJ"; // 北京证券交易所
            }
            else
            {
                return "SH"; // 默认使用上海证券交易所
            }
        }
    }
}
