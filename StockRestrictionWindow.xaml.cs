using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using StockCrawler.Models;

namespace StockCrawler
{
    /// <summary>
    /// StockRestrictionWindow.xaml 的交互逻辑
    /// </summary>
    public partial class StockRestrictionWindow : Window
    {
        public string StockCode { get; private set; }
        public string StockName { get; private set; }
        public string WindowTitle { get; private set; }

        public StockRestrictionWindow(string stockCode, string stockName, List<StockRestrictionInfo> restrictionInfoList)
        {
            InitializeComponent();
            
            StockCode = stockCode;
            StockName = stockName;
            WindowTitle = $"{stockName}({stockCode}) - 解禁限售信息";
            
            // 设置数据上下文
            DataContext = this;
            
            // 过滤出当前日期之后的解禁信息
            var filteredList = restrictionInfoList
                .Where(info => info.ReleaseDate >= DateTime.Today)
                .OrderBy(info => info.ReleaseDate)
                .ToList();
            
            // 设置数据源
            RestrictionDataGrid.ItemsSource = filteredList;
            
            // 更新提示信息
            if (filteredList.Count == 0)
            {
                InfoTextBlock.Text = "暂无未来解禁限售信息";
            }
            else
            {
                InfoTextBlock.Text = $"注：仅显示当前日期之后的解禁限售信息，共 {filteredList.Count} 条";
            }
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
