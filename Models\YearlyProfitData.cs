using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace StockCrawler.Models
{
    /// <summary>
    /// 年度盈亏数据模型
    /// </summary>
    public class YearlyProfitData
    {
        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; } = DateTime.Now.Year;

        /// <summary>
        /// 起初资产
        /// </summary>
        public decimal InitialAsset { get; set; } = 0;

        /// <summary>
        /// 净转入（转入-转出）
        /// </summary>
        public decimal NetTransfer
        {
            get
            {
                return TransferIn - TransferOut;
            }
        }

        /// <summary>
        /// 转入
        /// </summary>
        public decimal TransferIn { get; set; } = 0;

        /// <summary>
        /// 转出
        /// </summary>
        public decimal TransferOut { get; set; } = 0;

        /// <summary>
        /// 期间盈亏
        /// </summary>
        public decimal PeriodProfit { get; set; } = 0;

        /// <summary>
        /// 期末资产
        /// </summary>
        public decimal FinalAsset
        {
            get
            {
                return InitialAsset + NetTransfer + PeriodProfit;
            }
        }

        /// <summary>
        /// 年度收益率
        /// </summary>
        public decimal YearlyReturnRate
        {
            get
            {
                // 计算年度收益率：期间盈亏 / (起初资产 + 净转入/2)
                decimal denominator = InitialAsset + (NetTransfer / 2);
                if (denominator == 0)
                    return 0;

                return PeriodProfit / denominator;
            }
        }

        /// <summary>
        /// 年度收益率（百分比形式）
        /// </summary>
        public string YearlyReturnRatePercent
        {
            get
            {
                return $"{YearlyReturnRate:P2}";
            }
        }
    }
}
