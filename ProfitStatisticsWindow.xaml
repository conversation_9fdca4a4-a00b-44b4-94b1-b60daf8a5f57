<Window x:Class="StockCrawler.ProfitStatisticsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="盈亏统计" Height="950" Width="900" WindowStartupLocation="CenterOwner"
        Loaded="Window_Loaded" Background="White" WindowStyle="None" AllowsTransparency="True"
        ResizeMode="CanResize" ShowInTaskbar="False">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>

    <!-- 添加窗口按钮样式 -->
    <Window.Resources>
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#595959"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FFF5F5F5"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FFE5E5E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="MinHeight" Value="36"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#40A9FF"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#096DD9"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <!-- 添加窗口边框和阴影效果 -->
    <Border BorderThickness="1" BorderBrush="#E5E5E5" CornerRadius="8" Margin="5">
        <Border.Effect>
            <DropShadowEffect BlurRadius="15" ShadowDepth="0" Opacity="0.2" Color="#000000"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="2*"/>
                <RowDefinition Height="2*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="2*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏区域 -->
            <Grid Grid.Row="0" Margin="20,15,20,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 标题信息 -->
                <TextBlock Grid.Column="0" Text="盈亏统计" FontSize="18" FontWeight="Bold"
                           VerticalAlignment="Center" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown"/>

                <!-- 窗口控制按钮 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Width="30" Height="30" Click="CloseButton_Click"
                            Background="Transparent" BorderThickness="0" Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FFF5F5F5"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#FFE5E5E5"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                        <Path Data="M0,0 L10,10 M0,10 L10,0" Stroke="#595959" StrokeThickness="1"
                              Margin="10" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- 用户选择和年份选择 -->
            <Grid Grid.Row="1" Margin="20,0,20,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="用户:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="1" x:Name="cmbUserName" Height="36" Padding="5,0"
                          VerticalContentAlignment="Center" SelectionChanged="cmbUserName_SelectionChanged"/>

                <TextBlock Grid.Column="2" Text="年份:" VerticalAlignment="Center" Margin="15,0,10,0"/>
                <ComboBox Grid.Column="3" x:Name="cmbYear" Height="36" Padding="5,0"
                          VerticalContentAlignment="Center" SelectionChanged="cmbYear_SelectionChanged"/>
            </Grid>

            <!-- 个人年内每月盈亏统计图表 -->
            <Border Grid.Row="2" BorderBrush="#E0E0E0" BorderThickness="1" Margin="20,0,20,10" CornerRadius="6">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="个人年内每月盈亏统计" FontSize="16" FontWeight="Bold"  Margin="0,0,0,10"/>
                    <Canvas Grid.Row="1" x:Name="PersonalMonthlyCanvas" Background="#F8F9FA"/>
                </Grid>
            </Border>

            <!-- 三人当月总盈亏统计图表 -->
            <Border Grid.Row="3" BorderBrush="#E0E0E0" BorderThickness="1" Margin="20,10,20,10" CornerRadius="6">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="秦岭四宝月度竞赛" FontSize="16" FontWeight="Bold"  Margin="0,0,0,10"/>
                    <Canvas Grid.Row="1" x:Name="ThreePersonsCanvas" Background="#F8F9FA"/>
                </Grid>
            </Border>

            <!-- 多年汇总数据区域 -->
            <Border Grid.Row="4" BorderBrush="#E0E0E0" BorderThickness="1" Margin="20,0,20,10" CornerRadius="6" Background="#F0F8FF">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <TextBlock Grid.Row="0" Text="2017年至今汇总数据" FontSize="16" FontWeight="Bold" Margin="0,0,0,10" />

                    <!-- 数据显示区域 -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 标签行 -->
                        <Border Grid.Column="0" Grid.Row="0" Background="#E6E6E6" Margin="2" Padding="5">
                            <TextBlock Text="总盈利值" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="1" Grid.Row="0" Background="#E6E6E6" Margin="2" Padding="5">
                            <TextBlock Text="总转入值" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="2" Grid.Row="0" Background="#E6E6E6" Margin="2" Padding="5">
                            <TextBlock Text="总转出值" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="3" Grid.Row="0" Background="#E6E6E6" Margin="2" Padding="5">
                            <TextBlock Text="净转入转出值" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="4" Grid.Row="0" Background="#E6E6E6" Margin="2" Padding="5">
                            <TextBlock Text="起初资产" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="5" Grid.Row="0" Background="#E6E6E6" Margin="2" Padding="5">
                            <TextBlock Text="最终资产" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                        </Border>

                        <!-- 数据行 -->
                        <Border Grid.Column="0" Grid.Row="1" Margin="2" Padding="5" BorderThickness="1" BorderBrush="#E0E0E0">
                            <TextBlock x:Name="txtTotalPeriodProfit" Text="0" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="1" Grid.Row="1" Margin="2" Padding="5" BorderThickness="1" BorderBrush="#E0E0E0">
                            <TextBlock x:Name="txtTotalTransferIn" Text="0" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="2" Grid.Row="1" Margin="2" Padding="5" BorderThickness="1" BorderBrush="#E0E0E0">
                            <TextBlock x:Name="txtTotalTransferOut" Text="0" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="3" Grid.Row="1" Margin="2" Padding="5" BorderThickness="1" BorderBrush="#E0E0E0">
                            <TextBlock x:Name="txtNetTransfer" Text="0" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="4" Grid.Row="1" Margin="2" Padding="5" BorderThickness="1" BorderBrush="#E0E0E0">
                            <TextBlock x:Name="txtTotalInitialAsset" Text="0" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                        <Border Grid.Column="5" Grid.Row="1" Margin="2" Padding="5" BorderThickness="1" BorderBrush="#E0E0E0">
                            <TextBlock x:Name="txtTotalFinalAsset" Text="0" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Grid>
            </Border>

            <!-- 年度盈亏柱状图区域 -->
            <Border Grid.Row="5" BorderBrush="#E0E0E0" BorderThickness="1" Margin="20,0,20,10" CornerRadius="6">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="2017年至今年度盈亏统计" FontSize="16" FontWeight="Bold"  Margin="0,0,0,10"/>
                    <Canvas Grid.Row="1" x:Name="YearlyProfitCanvas" Background="#F8F9FA"/>
                </Grid>
            </Border>

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="6" Orientation="Horizontal" HorizontalAlignment="Center" Margin="20,0,20,15">
                <Button x:Name="btnClose" Content="关闭" Width="120" Height="40"
                        Click="CloseButton_Click" Style="{StaticResource ModernButton}" Background="#F5F5F5" Foreground="#595959"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
