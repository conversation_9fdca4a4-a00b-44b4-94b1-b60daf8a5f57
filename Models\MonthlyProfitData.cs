using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace StockCrawler.Models
{
    /// <summary>
    /// 月度盈亏数据模型
    /// </summary>
    public class MonthlyProfitData
    {
        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 一月盈亏
        /// </summary>
        public decimal January { get; set; }

        /// <summary>
        /// 二月盈亏
        /// </summary>
        public decimal February { get; set; }

        /// <summary>
        /// 三月盈亏
        /// </summary>
        public decimal March { get; set; }

        /// <summary>
        /// 四月盈亏
        /// </summary>
        public decimal April { get; set; }

        /// <summary>
        /// 五月盈亏
        /// </summary>
        public decimal May { get; set; }

        /// <summary>
        /// 六月盈亏
        /// </summary>
        public decimal June { get; set; }

        /// <summary>
        /// 七月盈亏
        /// </summary>
        public decimal July { get; set; }

        /// <summary>
        /// 八月盈亏
        /// </summary>
        public decimal August { get; set; }

        /// <summary>
        /// 九月盈亏
        /// </summary>
        public decimal September { get; set; }

        /// <summary>
        /// 十月盈亏
        /// </summary>
        public decimal October { get; set; }

        /// <summary>
        /// 十一月盈亏
        /// </summary>
        public decimal November { get; set; }

        /// <summary>
        /// 十二月盈亏
        /// </summary>
        public decimal December { get; set; }

        /// <summary>
        /// 计算年度总盈亏
        /// </summary>
        public decimal YearlyTotal
        {
            get
            {
                return January + February + March + April + May + June +
                       July + August + September + October + November + December;
            }
        }

        /// <summary>
        /// 计算截止当前月份的年度总盈亏
        /// </summary>
        public decimal YearToDateTotal
        {
            get
            {
                int currentMonth = DateTime.Now.Month;
                decimal total = 0;

                if (currentMonth >= 1) total += January;
                if (currentMonth >= 2) total += February;
                if (currentMonth >= 3) total += March;
                if (currentMonth >= 4) total += April;
                if (currentMonth >= 5) total += May;
                if (currentMonth >= 6) total += June;
                if (currentMonth >= 7) total += July;
                if (currentMonth >= 8) total += August;
                if (currentMonth >= 9) total += September;
                if (currentMonth >= 10) total += October;
                if (currentMonth >= 11) total += November;
                if (currentMonth >= 12) total += December;

                return total;
            }
        }

        /// <summary>
        /// 获取指定月份的盈亏值
        /// </summary>
        public decimal GetMonthValue(int month)
        {
            return month switch
            {
                1 => January,
                2 => February,
                3 => March,
                4 => April,
                5 => May,
                6 => June,
                7 => July,
                8 => August,
                9 => September,
                10 => October,
                11 => November,
                12 => December,
                _ => 0
            };
        }

        /// <summary>
        /// 设置指定月份的盈亏值
        /// </summary>
        public void SetMonthValue(int month, decimal value)
        {
            switch (month)
            {
                case 1: January = value; break;
                case 2: February = value; break;
                case 3: March = value; break;
                case 4: April = value; break;
                case 5: May = value; break;
                case 6: June = value; break;
                case 7: July = value; break;
                case 8: August = value; break;
                case 9: September = value; break;
                case 10: October = value; break;
                case 11: November = value; break;
                case 12: December = value; break;
            }
        }
    }
}
