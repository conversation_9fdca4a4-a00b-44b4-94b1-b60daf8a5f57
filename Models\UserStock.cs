using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace StockCrawler.Models
{
    /// <summary>
    /// 用户自选股信息模型
    /// </summary>
    public class UserStock : INotifyPropertyChanged
    {
        private string _code = string.Empty;
        private string _name = string.Empty;
        private int _shares;
        private decimal _costPrice;

        /// <summary>
        /// 股票代码
        /// </summary>
        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 股票名称
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 持股数量
        /// </summary>
        public int Shares
        {
            get => _shares;
            set
            {
                if (_shares != value)
                {
                    _shares = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 持仓成本价（每股）
        /// </summary>
        public decimal CostPrice
        {
            get => _costPrice;
            set
            {
                if (_costPrice != value)
                {
                    _costPrice = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 格式化后的持仓成本价显示
        /// </summary>
        public string FormattedCostPrice => $"{CostPrice:F2}";

        /// <summary>
        /// 创建新的用户自选股
        /// </summary>
        public UserStock()
        {
        }

        /// <summary>
        /// 使用指定的代码、名称和持股数量创建新的用户自选股
        /// </summary>
        public UserStock(string code, string name, int shares)
        {
            Code = code;
            Name = name;
            Shares = shares;
            CostPrice = 0;
        }

        /// <summary>
        /// 使用指定的代码、名称、持股数量和成本价创建新的用户自选股
        /// </summary>
        public UserStock(string code, string name, int shares, decimal costPrice)
        {
            Code = code;
            Name = name;
            Shares = shares;
            CostPrice = costPrice;
        }

        /// <summary>
        /// 属性变更事件
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性变更事件
        /// </summary>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
