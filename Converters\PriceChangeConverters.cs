using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace StockCrawler.Converters
{
    // 将价格变化转换为样式的转换器
    public class PriceChangeToStyleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal priceChange)
            {
                if (priceChange > 0)
                {
                    return Application.Current.Resources["UpTagStyle"];
                }
                else if (priceChange < 0)
                {
                    return Application.Current.Resources["DownTagStyle"];
                }
                else
                {
                    return Application.Current.Resources["NeutralTagStyle"];
                }
            }
            return Application.Current.Resources["NeutralTagStyle"];
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // 将盈亏转换为背景色的转换器
    public class ProfitToBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal profit)
            {
                if (profit > 0)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFF1F0"));
                }
                else if (profit < 0)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F6FFED"));
                }
                else
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5"));
                }
            }
            return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // 将盈亏转换为边框色的转换器
    public class ProfitToBorderConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal profit)
            {
                if (profit > 0)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFCCC7"));
                }
                else if (profit < 0)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#B7EB8F"));
                }
                else
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D9D9D9"));
                }
            }
            return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D9D9D9"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
