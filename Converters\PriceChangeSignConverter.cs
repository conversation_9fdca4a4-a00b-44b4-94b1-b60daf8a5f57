using System;
using System.Globalization;
using System.Windows.Data;

namespace StockCrawler.Converters
{
    /// <summary>
    /// 根据价格变化返回正负号的转换器
    /// </summary>
    public class PriceChangeSignConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return string.Empty;

            // 尝试将值转换为decimal
            if (value is decimal decimalValue)
            {
                return decimalValue > 0 ? "+" : decimalValue < 0 ? "" : "";
            }
            
            // 尝试将值转换为double
            if (value is double doubleValue)
            {
                return doubleValue > 0 ? "+" : doubleValue < 0 ? "" : "";
            }
            
            // 尝试将值转换为string并解析为decimal
            if (value is string stringValue && decimal.TryParse(stringValue, out decimal parsedValue))
            {
                return parsedValue > 0 ? "+" : parsedValue < 0 ? "" : "";
            }
            
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
