﻿<Window x:Class="StockCrawler.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        xmlns:converters="clr-namespace:StockCrawler.Converters"
        mc:Ignorable="d"
        Title="股票实时监控" Height="900" Width="1000"
        MinHeight="700" SizeToContent="Height"
        WindowStartupLocation="CenterScreen"
        Background="#F8F9FA" FontFamily="Microsoft YaHei"
        WindowStyle="None" AllowsTransparency="True"
        BorderThickness="1" BorderBrush="#E0E0E0"
        ResizeMode="CanResizeWithGrip"
        SnapsToDevicePixels="True" UseLayoutRounding="True"
        Opacity="0">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>

    <Window.Resources>
        <!-- 转换器资源 -->
        <converters:ColorToBackgroundConverter x:Key="ColorToBackgroundConverter"/>
        <converters:ColorToBorderConverter x:Key="ColorToBorderConverter"/>
        <converters:ProfitToGradientBackgroundConverter x:Key="ProfitToGradientBackgroundConverter"/>
        <converters:ProfitToGradientBorderConverter x:Key="ProfitToGradientBorderConverter"/>
        <converters:ProfitToTextColorConverter x:Key="ProfitToTextColorConverter"/>
        <converters:PositiveValueToVisibilityConverter x:Key="PositiveValueToVisibilityConverter"/>
        <converters:PriceChangeSignConverter x:Key="PriceChangeSignConverter"/>

        <!-- 窗口控制按钮样式 -->
        <Style x:Key="WindowControlButton" TargetType="Button">
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#40A9FF"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#096DD9"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="MinHeight" Value="36"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#40A9FF"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#096DD9"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 卡片样式 -->
        <Style x:Key="StockCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E8E8E8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="8" ShadowDepth="1" Opacity="0.1" Color="#000000"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 渐变卡片样式 - 盈利（红色） -->
        <Style x:Key="ProfitGradientCard" TargetType="Border">
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="10" ShadowDepth="2" Opacity="0.15" Color="#FF7875"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <EventTrigger RoutedEvent="Border.Loaded">
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0.9" To="1" Duration="0:0:0.5"/>
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>
            </Style.Triggers>
        </Style>

        <!-- 渐变卡片样式 - 亏损（绿色） -->
        <Style x:Key="LossGradientCard" TargetType="Border">
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="10" ShadowDepth="2" Opacity="0.15" Color="#95DE64"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <EventTrigger RoutedEvent="Border.Loaded">
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0.9" To="1" Duration="0:0:0.5"/>
                        </Storyboard>
                    </BeginStoryboard>
                </EventTrigger>
            </Style.Triggers>
        </Style>

        <!-- 标签样式 - 涨幅（红色） -->
        <Style x:Key="UpTagStyle" TargetType="Border">
            <Setter Property="Background" Value="#FFF1F0"/>
            <Setter Property="BorderBrush" Value="#FFCCC7"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="8,4"/>
        </Style>

        <!-- 标签样式 - 跌幅（绿色） -->
        <Style x:Key="DownTagStyle" TargetType="Border">
            <Setter Property="Background" Value="#F6FFED"/>
            <Setter Property="BorderBrush" Value="#B7EB8F"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="8,4"/>
        </Style>

        <!-- 标签样式 - 中性（灰色） -->
        <Style x:Key="NeutralTagStyle" TargetType="Border">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="8,4"/>
        </Style>

        <!-- 总盈亏标签样式 -->
        <Style x:Key="TotalProfitTag" TargetType="Border">
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="12,6"/>
        </Style>
    </Window.Resources>

    <Window.Triggers>
        <EventTrigger RoutedEvent="Window.Loaded">
            <BeginStoryboard>
                <Storyboard>
                    <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.3"/>
                </Storyboard>
            </BeginStoryboard>
        </EventTrigger>
    </Window.Triggers>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 窗口阴影 -->
        <Border x:Name="WindowBorder" Grid.RowSpan="3" Margin="-5" CornerRadius="8" BorderThickness="0">
            <Border.Effect>
                <DropShadowEffect BlurRadius="15" ShadowDepth="0" Opacity="0.2" Color="#000000"/>
            </Border.Effect>
            <Border.Style>
                <Style TargetType="Border">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding WindowState, RelativeSource={RelativeSource AncestorType=Window}}" Value="Maximized">
                            <Setter Property="Margin" Value="0"/>
                            <Setter Property="CornerRadius" Value="0"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
        </Border>

        <!-- 顶部标题栏 -->
        <Border Background="#1890FF" Padding="16,0,16,0" Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 标题和刷新按钮 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="8,12">
                    <TextBlock Text="股票实时监控" FontSize="18" FontWeight="Bold" Foreground="White"/>
                    <TextBlock x:Name="UpdateTimeTextBlock" FontSize="13" Foreground="#E6F7FF" VerticalAlignment="Bottom" Margin="10,0,0,2"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center">
                    <Button x:Name="RefreshButton" Style="{StaticResource ModernButton}"
                            Click="RefreshButton_Click" Content="刷新数据" Margin="10,0,10,0"/>

                    <Button x:Name="UserStockSettingsButton" Style="{StaticResource ModernButton}"
                            Click="UserStockSettingsButton_Click" Content="自选股设置" Margin="0,0,10,0"/>

                    <Button x:Name="NewStockInfoButton" Style="{StaticResource ModernButton}"
                            Click="NewStockInfoButton_Click" Content="新股信息" Margin="0,0,10,0"/>

                    <Button x:Name="MonthlyProfitSettingsButton" Style="{StaticResource ModernButton}"
                            Click="MonthlyProfitSettingsButton_Click" Content="盈亏情况" Margin="0,0,0,0"/>
                </StackPanel>

                <!-- 窗口控制按钮 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                    <Button x:Name="MinimizeButton" Style="{StaticResource WindowControlButton}" Click="MinimizeButton_Click" Margin="0,0,8,0">
                        <Path Data="M0,0 L10,0" Stroke="White" StrokeThickness="1" Margin="0,8,0,0"/>
                    </Button>

                    <Button x:Name="MaximizeButton" Style="{StaticResource WindowControlButton}" Click="MaximizeButton_Click" Margin="0,0,8,0">
                        <Path x:Name="MaximizeIcon" Data="M0,0 L10,0 L10,10 L0,10 Z" Stroke="White" StrokeThickness="1" Fill="Transparent"/>
                    </Button>
                </StackPanel>

                <Button x:Name="CloseButton" Grid.Column="3" Style="{StaticResource WindowControlButton}"
                        Click="CloseButton_Click" Background="#E81123" Margin="0,0,0,0">
                    <Path Data="M0,0 L10,10 M0,10 L10,0" Stroke="White" StrokeThickness="1"/>
                </Button>
            </Grid>
        </Border>

        <!-- 总盈亏区域 -->
        <Border x:Name="TotalProfitArea" Grid.Row="1" Background="White" Margin="0,0,0,0" Padding="12,8" CornerRadius="8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="本日总盈亏：" FontSize="16" FontWeight="Bold" Margin="0,0,12,0"/>
                    <Border x:Name="TotalProfitBorder" Style="{StaticResource TotalProfitTag}">
                        <TextBlock x:Name="TotalProfitTextBlock" FontSize="16" FontWeight="Bold"/>
                    </Border>
                </StackPanel>

                <Button x:Name="ScreenshotButton" Grid.Column="1" Style="{StaticResource ModernButton}"
                        Click="ScreenshotButton_Click" Content="截图" Margin="0,0,0,0"/>
            </Grid>
        </Border>

        <!-- 股票列表区域 -->
        <ScrollViewer Grid.Row="2" Margin="0,0,0,0" VerticalScrollBarVisibility="Auto">
            <ItemsControl x:Name="StockItemsControl">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel Orientation="Vertical"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border BorderBrush="{Binding DailyProfit, Converter={StaticResource ProfitToGradientBorderConverter}}"
                                Background="{Binding DailyProfit, Converter={StaticResource ProfitToGradientBackgroundConverter}}"
                                Margin="0,0,0,8">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="BorderThickness" Value="1"/>
                                    <Setter Property="CornerRadius" Value="12"/>
                                    <Setter Property="Padding" Value="16,12"/>
                                    <Setter Property="Margin" Value="0,0,0,12"/>
                                    <Setter Property="Effect">
                                        <Setter.Value>
                                            <DropShadowEffect BlurRadius="10" ShadowDepth="2" Opacity="0.15" Color="#000000"/>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <EventTrigger RoutedEvent="Border.Loaded">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0.9" To="1" Duration="0:0:0.5"/>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 第一行：股票代码和名称 -->
                                <Grid Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Grid Grid.Column="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto" MinWidth="80"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 股票名称和代码 -->
                                        <StackPanel Grid.Column="0" Orientation="Vertical" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Name}" FontSize="18" FontWeight="Bold"
                                                       Foreground="{Binding DailyProfit, Converter={StaticResource ProfitToTextColorConverter}}"/>
                                            <TextBlock Text="{Binding Code}" FontSize="14" Foreground="#595959" Margin="0,4,0,0"/>
                                        </StackPanel>

                                        <!-- 占位空间 -->
                                        <Grid Grid.Column="1"/>

                                        <!-- 涨跌幅标签 - 新风格，固定位置 -->
                                        <Border Grid.Column="2" Margin="12,0,0,0" Padding="8,4" CornerRadius="6" BorderThickness="0" VerticalAlignment="Center" HorizontalAlignment="Right"
                                                Background="{Binding PercentChangeColor}">
                                            <TextBlock FontSize="15" FontWeight="Bold" Foreground="White">
                                                <TextBlock.Text>
                                                    <MultiBinding StringFormat="{}{0}{1}">
                                                        <Binding Path="ActualPriceChange" Converter="{StaticResource PriceChangeSignConverter}"/>
                                                        <Binding Path="FormattedPercentChange"/>
                                                    </MultiBinding>
                                                </TextBlock.Text>
                                            </TextBlock>
                                        </Border>
                                    </Grid>

                                    <!-- 按钮容器 -->
                                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                        <!-- 解禁限售按钮 -->
                                        <Button Content="解禁限售" Margin="15,0,8,0" Padding="8,4"
                                                Background="#FFFFFF" Foreground="#595959" BorderThickness="1"
                                                BorderBrush="#D9D9D9" FontSize="12" Cursor="Hand"
                                                Tag="{Binding Code}" Click="RestrictionInfoButton_Click">
                                            <Button.Resources>
                                                <Style TargetType="Border">
                                                    <Setter Property="CornerRadius" Value="4"/>
                                                </Style>
                                            </Button.Resources>
                                            <Button.Style>
                                                <Style TargetType="Button">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="Button">
                                                                <Border Background="{TemplateBinding Background}"
                                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                                        CornerRadius="4">
                                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" Margin="{TemplateBinding Padding}"/>
                                                                </Border>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                    <Style.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="#F5F5F5"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed" Value="True">
                                                            <Setter Property="Background" Value="#E8E8E8"/>
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Button.Style>
                                        </Button>

                                        <!-- 各季度利润按钮 -->
                                        <Button Content="各季度利润" Margin="0,0,8,0" Padding="8,4"
                                                Background="#FFFFFF" Foreground="#595959" BorderThickness="1"
                                                BorderBrush="#D9D9D9" FontSize="12" Cursor="Hand"
                                                Tag="{Binding Code}" Click="QuarterlyProfitButton_Click">
                                            <Button.Resources>
                                                <Style TargetType="Border">
                                                    <Setter Property="CornerRadius" Value="4"/>
                                                </Style>
                                            </Button.Resources>
                                            <Button.Style>
                                                <Style TargetType="Button">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="Button">
                                                                <Border Background="{TemplateBinding Background}"
                                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                                        BorderThickness="{TemplateBinding BorderThickness}"
                                                                        CornerRadius="4">
                                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" Margin="{TemplateBinding Padding}"/>
                                                                </Border>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                    <Style.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="#F5F5F5"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed" Value="True">
                                                            <Setter Property="Background" Value="#E8E8E8"/>
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Button.Style>
                                        </Button>
                                    </StackPanel>

                                    <!-- 原涨跌幅标签位置留空 -->
                                    <Grid Grid.Column="2"></Grid>
                                </Grid>

                                <!-- 分隔线 -->
                                <Border Grid.Row="1" BorderBrush="#FFFFFF" BorderThickness="0,1,0,0" Margin="0,12,0,12" Opacity="0.5"/>

                                <!-- 第二行：价格和持仓信息 -->
                                <Grid Grid.Row="2">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 第一行：价格和持仓信息 -->
                                    <Grid Grid.Row="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 当前价格 -->
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="当前价格" FontSize="13" Foreground="#595959"/>
                                            <StackPanel Orientation="Horizontal" Margin="0,6,0,0">
                                                <TextBlock Text="{Binding FormattedPrice}" FontSize="16" FontWeight="SemiBold"
                                                           Foreground="{Binding DailyProfit, Converter={StaticResource ProfitToTextColorConverter}}"/>
                                                <TextBlock Text="{Binding FormattedPriceChange, StringFormat=({0})}" FontSize="13"
                                                           Foreground="{Binding PercentChangeColor}" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </StackPanel>

                                        <!-- 持股数量 -->
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="持股数量" FontSize="13" Foreground="#595959"/>
                                            <TextBlock Text="{Binding SharesHeld}" FontSize="16" FontWeight="SemiBold" Margin="0,6,0,0"
                                                       Foreground="#333333"/>
                                        </StackPanel>

                                        <!-- 持仓市值 -->
                                        <StackPanel Grid.Column="2">
                                            <TextBlock Text="持仓市值" FontSize="13" Foreground="#595959"/>
                                            <TextBlock Text="{Binding FormattedTotalValue}" FontSize="16" FontWeight="SemiBold" Margin="0,6,0,0"
                                                       Foreground="#333333"/>
                                        </StackPanel>

                                        <!-- 当日盈亏 -->
                                        <Border Grid.Column="3" x:Name="DailyProfitPanel" Tag="{Binding}"
                                               Background="Transparent" Cursor="Hand" ToolTip="点击复制盈亏信息"
                                               MouseLeftButtonDown="ProfitPanel_MouseLeftButtonDown">
                                            <StackPanel>
                                                <TextBlock Text="当日盈亏" FontSize="13" Foreground="#595959"/>
                                                <StackPanel Orientation="Vertical" Margin="0,6,0,0">
                                                    <TextBlock Text="{Binding FormattedDailyProfit}" FontSize="16" FontWeight="Bold"
                                                               Foreground="{Binding DailyProfit, Converter={StaticResource ProfitToTextColorConverter}}"/>
                                                    <TextBlock Text="{Binding FormattedDailyProfitPercentage}" FontSize="12" Margin="0,2,0,0"
                                                               Foreground="{Binding DailyProfit, Converter={StaticResource ProfitToTextColorConverter}}"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <!-- 第二行：持仓成本和总盈亏 -->
                                    <Grid Grid.Row="1" Margin="0,12,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 持仓成本 -->
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="持仓成本" FontSize="13" Foreground="#595959"/>
                                            <TextBlock Text="{Binding FormattedTotalCost}" FontSize="16" FontWeight="SemiBold" Margin="0,6,0,0"
                                                       Foreground="#333333"/>
                                        </StackPanel>

                                        <!-- 持仓成本价 -->
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="成本价" FontSize="13" Foreground="#595959"/>
                                            <TextBlock Text="{Binding CostPrice, StringFormat={}{0:F2}}" FontSize="16" FontWeight="SemiBold" Margin="0,6,0,0"
                                                       Foreground="#333333"/>
                                        </StackPanel>

                                        <!-- 总盈亏 -->
                                        <StackPanel Grid.Column="2">
                                            <TextBlock Text="总盈亏" FontSize="13" Foreground="#595959"/>
                                            <TextBlock Text="{Binding FormattedTotalRawProfit}" FontSize="16" FontWeight="SemiBold" Margin="0,6,0,0"
                                                       Foreground="{Binding TotalProfitColor}"/>
                                        </StackPanel>

                                        <!-- 扣除手续费后的总盈亏 -->
                                        <Border Grid.Column="3" x:Name="TotalProfitPanel" Tag="{Binding}"
                                               Background="Transparent" Cursor="Hand" ToolTip="点击复制盈亏信息"
                                               MouseLeftButtonDown="ProfitPanel_MouseLeftButtonDown">
                                            <StackPanel>
                                                <TextBlock Text="净盈亏" FontSize="13" Foreground="#595959"/>
                                                <StackPanel Orientation="Horizontal" Margin="0,6,0,0">
                                                    <TextBlock Text="{Binding FormattedTotalProfit}" FontSize="16" FontWeight="Bold"
                                                               Foreground="{Binding TotalProfitColor}"/>
                                                    <TextBlock Text="{Binding FormattedTotalProfitPercentage, StringFormat=({0})}" FontSize="13"
                                                               Foreground="{Binding TotalProfitColor}" Margin="6,0,0,0" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</Window>
