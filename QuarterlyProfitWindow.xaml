<Window x:Class="StockCrawler.QuarterlyProfitWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="各季度利润" Height="450" Width="800" WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize" ShowInTaskbar="False" Loaded="Window_Loaded"
        Background="White" WindowStyle="None" AllowsTransparency="True">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>
    
    <!-- 添加窗口按钮样式 -->
    <Window.Resources>
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#595959"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FFF5F5F5"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FFE5E5E5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <!-- 添加窗口边框和阴影效果 -->
    <Border BorderThickness="1" BorderBrush="#E5E5E5" CornerRadius="8" Margin="5">
        <Border.Effect>
            <DropShadowEffect BlurRadius="15" ShadowDepth="0" Opacity="0.2" Color="#000000"/>
        </Border.Effect>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏区域 -->
            <Grid Grid.Row="0" Margin="20,15,20,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 标题信息 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="StockNameTextBlock" Text="股票名称" FontSize="18" FontWeight="Bold"/>
                    <TextBlock Text=" (" FontSize="18"/>
                    <TextBlock x:Name="StockCodeTextBlock" Text="股票代码" FontSize="18"/>
                    <TextBlock Text=")" FontSize="18"/>
                    <TextBlock Text=" - 近一年各季度利润" FontSize="18" Margin="10,0,0,0"/>
                </StackPanel>

                <!-- 加载进度条 -->
                <ProgressBar x:Name="LoadingProgressBar" Grid.Column="1" Width="100" Height="2"
                            IsIndeterminate="True" Visibility="Collapsed" Margin="0,0,10,0"/>

                <!-- 窗口控制按钮 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Width="30" Height="30" Click="CloseButton_Click"
                            Background="Transparent" BorderThickness="0" Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FFF5F5F5"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#FFE5E5E5"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                        <Path Data="M0,0 L10,10 M0,10 L10,0" Stroke="#595959" StrokeThickness="1"
                              Margin="10" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- 数据表格区域 -->
            <DataGrid x:Name="ProfitDataGrid" Grid.Row="1" Margin="20,0,20,0"
                      AutoGenerateColumns="False" IsReadOnly="True"
                      GridLinesVisibility="All" CanUserSortColumns="True"
                      CanUserResizeColumns="True" HeadersVisibility="All"
                      AlternatingRowBackground="#F5F5F5" BorderBrush="#E0E0E0"
                      BorderThickness="1" HorizontalGridLinesBrush="#E0E0E0"
                      VerticalGridLinesBrush="#E0E0E0" RowHeight="35">
                <DataGrid.Resources>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#F0F0F0"/>
                        <Setter Property="Padding" Value="10,8"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    </Style>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Background" Value="White"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F0F7FF"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#E6F7FF"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.Resources>

                <DataGrid.Columns>
                    <DataGridTextColumn Header="报告期" Binding="{Binding ReportDate}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="营业收入(元)" Binding="{Binding Revenue}" Width="160">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="0,0,10,0"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="稀释每股收益" Binding="{Binding DilutedEPS}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="0,0,10,0"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="净利润(元)" Binding="{Binding NetProfit}" Width="160">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="0,0,10,0"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="综合收益总额" Binding="{Binding TotalIncome}" Width="160">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="0,0,10,0"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>

            <!-- 状态信息 -->
            <TextBlock x:Name="StatusTextBlock" Grid.Row="2" Margin="20,10,20,15"
                       Text="正在加载数据..." Foreground="#595959"/>
        </Grid>
    </Border>
</Window>
