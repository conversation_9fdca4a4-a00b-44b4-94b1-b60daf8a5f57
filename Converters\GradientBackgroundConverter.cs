using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace StockCrawler.Converters
{
    /// <summary>
    /// 根据盈亏状态转换为渐变背景的转换器
    /// </summary>
    public class ProfitToGradientBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal profit)
            {
                if (profit > 0)
                {
                    // 盈利 - 红色渐变
                    var gradient = new LinearGradientBrush();
                    gradient.StartPoint = new System.Windows.Point(0, 0);
                    gradient.EndPoint = new System.Windows.Point(1, 1);
                    
                    // 从浅红色到深红色的渐变
                    gradient.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString("#FFF1F0"), 0.0));
                    gradient.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString("#FFCCC7"), 0.5));
                    gradient.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString("#FFA39E"), 1.0));
                    
                    return gradient;
                }
                else if (profit < 0)
                {
                    // 亏损 - 绿色渐变
                    var gradient = new LinearGradientBrush();
                    gradient.StartPoint = new System.Windows.Point(0, 0);
                    gradient.EndPoint = new System.Windows.Point(1, 1);
                    
                    // 从浅绿色到深绿色的渐变
                    gradient.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString("#F6FFED"), 0.0));
                    gradient.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString("#D9F7BE"), 0.5));
                    gradient.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString("#B7EB8F"), 1.0));
                    
                    return gradient;
                }
                else
                {
                    // 持平 - 灰色渐变
                    var gradient = new LinearGradientBrush();
                    gradient.StartPoint = new System.Windows.Point(0, 0);
                    gradient.EndPoint = new System.Windows.Point(1, 1);
                    
                    // 从浅灰色到深灰色的渐变
                    gradient.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString("#FAFAFA"), 0.0));
                    gradient.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString("#F5F5F5"), 0.5));
                    gradient.GradientStops.Add(new GradientStop((Color)ColorConverter.ConvertFromString("#E8E8E8"), 1.0));
                    
                    return gradient;
                }
            }
            
            // 默认返回白色背景
            return new SolidColorBrush(Colors.White);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    /// <summary>
    /// 根据盈亏状态转换为边框颜色的转换器
    /// </summary>
    public class ProfitToGradientBorderConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal profit)
            {
                if (profit > 0)
                {
                    // 盈利 - 红色边框
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF7875"));
                }
                else if (profit < 0)
                {
                    // 亏损 - 绿色边框
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#95DE64"));
                }
                else
                {
                    // 持平 - 灰色边框
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D9D9D9"));
                }
            }
            
            // 默认返回灰色边框
            return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E8E8E8"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    /// <summary>
    /// 根据盈亏状态转换为文本颜色的转换器
    /// </summary>
    public class ProfitToTextColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal profit)
            {
                if (profit > 0)
                {
                    // 盈利 - 深红色文本
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#CF1322"));
                }
                else if (profit < 0)
                {
                    // 亏损 - 深绿色文本
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#389E0D"));
                }
                else
                {
                    // 持平 - 深灰色文本
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#595959"));
                }
            }
            
            // 默认返回黑色文本
            return new SolidColorBrush(Colors.Black);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
