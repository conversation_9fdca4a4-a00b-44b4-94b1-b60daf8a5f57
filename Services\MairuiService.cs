using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Globalization;
using Newtonsoft.Json;
using StockCrawler.Models;
using StockCrawler;

namespace StockCrawler.Services
{
    public class MairuiService
    {
        // 静态API密钥，从记忆中获取
        private static readonly string _staticApiKey = "72430658081e51fc98";

        // 静态方法，用于获取季度利润数据
        public static async Task<List<QuarterlyProfitData>> GetQuarterlyProfitAsync(string stockCode)
        {
            try
            {
                LogHelper.LogInfo($"开始获取股票 {stockCode} 的季度利润数据");

                // 确保股票代码格式正确
                stockCode = stockCode.Trim();

                // 构建API URL
                string url = $"http://api.mairuiapi.com/hscp/jdlr/{stockCode}/{_staticApiKey}";

                LogHelper.LogInfo($"API URL: {url}");

                // 创建HttpClient
                using var httpClient = new HttpClient();

                // 添加常用的HTTP请求头，模拟浏览器请求
                httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                httpClient.DefaultRequestHeaders.Add("Accept", "application/json, text/plain, */*");
                httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
                httpClient.DefaultRequestHeaders.Add("Pragma", "no-cache");
                httpClient.DefaultRequestHeaders.Add("Referer", "https://www.mairui.club/");

                // 设置超时时间
                httpClient.Timeout = TimeSpan.FromSeconds(30);

                // 发送请求
                LogHelper.LogInfo("发送HTTP请求...");
                string response;

                try
                {
                    response = await httpClient.GetStringAsync(url);
                    LogHelper.LogInfo($"HTTP请求成功，响应长度: {response.Length}");

                    // 记录API调用和响应
                    LogHelper.LogApi(url, response);
                }
                catch (HttpRequestException ex)
                {
                    LogHelper.LogError($"HTTP请求错误: {ex.Message}", ex);
                    if (ex.StatusCode.HasValue)
                    {
                        LogHelper.LogError($"HTTP状态码: {(int)ex.StatusCode.Value} ({ex.StatusCode.Value})");
                    }
                    throw new Exception($"获取季度利润数据失败: {ex.Message}", ex);
                }

                // 解析JSON响应
                var profitDataList = new List<QuarterlyProfitData>();

                try
                {
                    // 尝试解析JSON数据
                    LogHelper.LogInfo("开始解析JSON数据...");

                    // 首先尝试解析为数组
                    var jsonArray = JsonConvert.DeserializeObject<dynamic[]>(response);

                    if (jsonArray != null && jsonArray.Length > 0)
                    {
                        LogHelper.LogInfo($"成功解析为JSON数组，包含 {jsonArray.Length} 个元素");

                        foreach (var item in jsonArray)
                        {
                            try
                            {
                                // 输出完整的JSON项，帮助调试
                                LogHelper.LogDebug($"处理项: {item}");

                                // 特别记录日期字段，帮助调试
                                if (item.date != null)
                                {
                                    LogHelper.LogDebug($"API返回的日期字段: {item.date}, 类型: {item.date.GetType().Name}");
                                }

                                // 使用新的FromApiData方法创建对象
                                var profitData = QuarterlyProfitData.FromApiData(item);

                                // 记录解析后的数据
                                LogHelper.LogDebug($"解析字段 - 报告期: {profitData.ReportDate}, 营收: {profitData.Revenue}, 稀释每股收益: {profitData.DilutedEPS}, 净利润: {profitData.NetProfit}, 综合收益总额: {profitData.TotalIncome}");

                                profitDataList.Add(profitData);
                            }
                            catch (Exception ex)
                            {
                                LogHelper.LogError($"解析季度利润数据项时出错: {ex.Message}", ex);
                            }
                        }
                    }
                    else
                    {
                        LogHelper.LogError("API返回的JSON数组为空或无效");

                        // 尝试解析为对象，可能是错误信息
                        var jsonObject = JsonConvert.DeserializeObject<dynamic>(response);
                        if (jsonObject != null)
                        {
                            LogHelper.LogError($"API返回的JSON对象: {jsonObject}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.LogError($"解析JSON数据时出错: {ex.Message}", ex);

                    // 尝试直接创建一些测试数据，以便验证UI显示
                    LogHelper.LogInfo("创建测试数据...");

                    // 添加一些测试数据
                    profitDataList.Add(new QuarterlyProfitData
                    {
                        ReportDate = "2023-12-31",
                        Revenue = "1,234,567",
                        DilutedEPS = "0.45",
                        NetProfit = "456,789",
                        TotalIncome = "460,000"
                    });

                    profitDataList.Add(new QuarterlyProfitData
                    {
                        ReportDate = "2023-09-30",
                        Revenue = "987,654",
                        DilutedEPS = "0.38",
                        NetProfit = "345,678",
                        TotalIncome = "350,000"
                    });

                    profitDataList.Add(new QuarterlyProfitData
                    {
                        ReportDate = "2023-06-30",
                        Revenue = "876,543",
                        DilutedEPS = "0.25",
                        NetProfit = "234,567",
                        TotalIncome = "240,000"
                    });

                    profitDataList.Add(new QuarterlyProfitData
                    {
                        ReportDate = "2023-03-31",
                        Revenue = "765,432",
                        DilutedEPS = "0.15",
                        NetProfit = "123,456",
                        TotalIncome = "125,000"
                    });
                }

                LogHelper.LogInfo($"返回 {profitDataList.Count} 条季度利润数据");
                return profitDataList;
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"获取季度利润数据时出错: {ex.Message}", ex);
                throw;
            }
        }
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;

        public MairuiService(string apiKey)
        {
            _apiKey = apiKey;
            _httpClient = new HttpClient();

            // 添加常用的HTTP请求头，模拟浏览器请求
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json, text/plain, */*");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("Pragma", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("Referer", "https://www.mairui.club/");

            // 设置超时时间
            _httpClient.Timeout = TimeSpan.FromSeconds(30);

            Debug.WriteLine("MairuiService 已初始化");
        }

        /// <summary>
        /// 获取股票的解禁限售信息
        /// </summary>
        /// <param name="stockCode">股票代码</param>
        /// <returns>解禁限售信息列表</returns>
        public async Task<List<StockRestrictionInfo>> GetStockRestrictionInfoAsync(string stockCode)
        {
            try
            {
                // 构建API URL
                string url = $"http://api.mairuiapi.com/hscp/jjxs/{stockCode}/{_apiKey}";

                Debug.WriteLine($"请求解禁限售数据: {url}");

                // 发送请求
                string response;
                try
                {
                    response = await _httpClient.GetStringAsync(url);
                    Debug.WriteLine($"解禁限售数据响应成功，长度: {response.Length}");
                    Debug.WriteLine($"响应内容预览: {(response.Length > 100 ? response.Substring(0, 100) + "..." : response)}");
                }
                catch (HttpRequestException ex)
                {
                    Debug.WriteLine($"HTTP请求错误: {ex.Message}");
                    if (ex.StatusCode.HasValue)
                    {
                        Debug.WriteLine($"HTTP状态码: {(int)ex.StatusCode.Value} ({ex.StatusCode.Value})");
                    }
                    throw new Exception($"获取解禁限售数据失败: {ex.Message}", ex);
                }

                // 解析JSON响应
                var restrictionInfoList = new List<StockRestrictionInfo>();

                // 解析JSON数据
                var jsonData = JsonConvert.DeserializeObject<List<dynamic>>(response);

                if (jsonData != null)
                {
                    foreach (var item in jsonData)
                    {
                        try
                        {
                            var info = new StockRestrictionInfo
                            {
                                ReleaseDate = DateTime.Parse(item.rdate.ToString()),
                                ReleaseAmount = Convert.ToDecimal(item.ramount),
                                ReleaseMarketValue = Convert.ToDecimal(item.rprice),
                                Batch = Convert.ToInt32(item.batch)
                            };

                            // 公告日期可能为空
                            if (item.pdate != null && !string.IsNullOrEmpty(item.pdate.ToString()))
                            {
                                info.AnnouncementDate = DateTime.Parse(item.pdate.ToString());
                            }

                            restrictionInfoList.Add(info);
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"解析解禁限售数据项时出错: {ex.Message}");
                        }
                    }
                }

                return restrictionInfoList;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取解禁限售数据时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取新股发行信息
        /// </summary>
        /// <returns>新股发行信息列表</returns>
        public async Task<List<NewStockInfo>> GetNewStockInfoAsync()
        {
            try
            {
                // 构建API URL
                string url = $"http://api.mairuiapi.com/hslt/new/{_apiKey}";

                Debug.WriteLine($"请求新股发行数据: {url}");

                // 发送请求
                string response;
                try
                {
                    response = await _httpClient.GetStringAsync(url);
                    Debug.WriteLine($"新股发行数据响应成功，长度: {response.Length}");
                    Debug.WriteLine($"响应内容预览: {(response.Length > 100 ? response.Substring(0, 100) + "..." : response)}");
                }
                catch (HttpRequestException ex)
                {
                    Debug.WriteLine($"HTTP请求错误: {ex.Message}");
                    if (ex.StatusCode.HasValue)
                    {
                        Debug.WriteLine($"HTTP状态码: {(int)ex.StatusCode.Value} ({ex.StatusCode.Value})");
                    }
                    throw new Exception($"获取新股发行数据失败: {ex.Message}", ex);
                }

                // 解析JSON响应
                var newStockInfoList = new List<NewStockInfo>();

                // 解析JSON数据
                var jsonData = JsonConvert.DeserializeObject<List<dynamic>>(response);

                if (jsonData != null)
                {
                    foreach (var item in jsonData)
                    {
                        try
                        {
                            // 解析申购日期
                            DateTime purchaseDate;
                            if (!DateTime.TryParseExact(item.sgrq?.ToString(), "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out purchaseDate))
                            {
                                // 如果解析失败，尝试其他格式
                                if (!DateTime.TryParse(item.sgrq?.ToString(), out purchaseDate))
                                {
                                    // 如果仍然解析失败，跳过此项
                                    Debug.WriteLine($"无法解析申购日期: {item.sgrq}");
                                    continue;
                                }
                            }

                            var newStock = new NewStockInfo
                            {
                                Code = item.zqdm?.ToString() ?? string.Empty,
                                Name = item.zqjc?.ToString() ?? string.Empty,
                                PurchaseCode = item.sgdm?.ToString() ?? string.Empty,
                                PurchaseDate = purchaseDate,
                                MainBusiness = item.zyyw?.ToString() ?? string.Empty
                            };

                            // 解析发行价格
                            if (item.fxjg != null && item.fxjg.ToString() != "null" && !string.IsNullOrEmpty(item.fxjg.ToString()))
                            {
                                decimal.TryParse(item.fxjg.ToString(), out decimal issuePrice);
                                newStock.IssuePrice = issuePrice;
                            }

                            // 解析申购上限
                            if (item.sgsx != null && item.sgsx.ToString() != "null" && !string.IsNullOrEmpty(item.sgsx.ToString()))
                            {
                                decimal.TryParse(item.sgsx.ToString(), out decimal purchaseLimit);
                                newStock.PurchaseLimit = purchaseLimit;
                            }

                            // 解析顶格申购需配市值
                            if (item.dgsz != null && item.dgsz.ToString() != "null" && !string.IsNullOrEmpty(item.dgsz.ToString()))
                            {
                                decimal.TryParse(item.dgsz.ToString(), out decimal topPurchaseMarketValue);
                                newStock.TopPurchaseMarketValue = topPurchaseMarketValue;
                            }

                            // 解析发行市盈率
                            if (item.syl != null && item.syl.ToString() != "null" && !string.IsNullOrEmpty(item.syl.ToString()))
                            {
                                decimal.TryParse(item.syl.ToString(), out decimal issuePERatio);
                                newStock.IssuePERatio = issuePERatio;
                            }

                            // 解析行业市盈率
                            if (item.hysyl != null && item.hysyl.ToString() != "null" && !string.IsNullOrEmpty(item.hysyl.ToString()))
                            {
                                decimal.TryParse(item.hysyl.ToString(), out decimal industryPERatio);
                                newStock.IndustryPERatio = industryPERatio;
                            }

                            // 解析发行总数
                            if (item.fxsl != null && item.fxsl.ToString() != "null" && !string.IsNullOrEmpty(item.fxsl.ToString()))
                            {
                                decimal.TryParse(item.fxsl.ToString(), out decimal totalIssue);
                                newStock.TotalIssue = totalIssue;
                            }

                            // 解析网上发行
                            if (item.swfxsl != null && item.swfxsl.ToString() != "null" && !string.IsNullOrEmpty(item.swfxsl.ToString()))
                            {
                                decimal.TryParse(item.swfxsl.ToString(), out decimal onlineIssue);
                                newStock.OnlineIssue = onlineIssue;
                            }

                            // 解析最新价
                            if (item.zxj != null && item.zxj.ToString() != "null" && !string.IsNullOrEmpty(item.zxj.ToString()))
                            {
                                decimal latestPrice;
                                if (decimal.TryParse(item.zxj.ToString(), out latestPrice))
                                {
                                    newStock.LatestPrice = latestPrice;
                                }
                            }

                            // 解析首日收盘价
                            if (item.srspj != null && item.srspj.ToString() != "null" && !string.IsNullOrEmpty(item.srspj.ToString()))
                            {
                                decimal firstDayClosingPrice;
                                if (decimal.TryParse(item.srspj.ToString(), out firstDayClosingPrice))
                                {
                                    newStock.FirstDayClosingPrice = firstDayClosingPrice;
                                }
                            }

                            // 解析中签率
                            if (item.wszql != null && item.wszql.ToString() != "null" && !string.IsNullOrEmpty(item.wszql.ToString()))
                            {
                                decimal winningRate;
                                if (decimal.TryParse(item.wszql.ToString(), out winningRate))
                                {
                                    newStock.WinningRate = winningRate;
                                }
                            }

                            // 解析连续一字板数量
                            if (item.yzbsl != null && item.yzbsl.ToString() != "null" && !string.IsNullOrEmpty(item.yzbsl.ToString()))
                            {
                                int consecutiveLimitUpDays;
                                if (int.TryParse(item.yzbsl.ToString(), out consecutiveLimitUpDays))
                                {
                                    newStock.ConsecutiveLimitUpDays = consecutiveLimitUpDays;
                                }
                            }

                            // 解析涨幅
                            if (item.zf != null && item.zf.ToString() != "null" && !string.IsNullOrEmpty(item.zf.ToString()))
                            {
                                decimal priceIncrease;
                                if (decimal.TryParse(item.zf.ToString(), out priceIncrease))
                                {
                                    newStock.PriceIncrease = priceIncrease;
                                }
                            }

                            // 解析每中一签获利
                            if (item.yqhl != null && item.yqhl.ToString() != "null" && !string.IsNullOrEmpty(item.yqhl.ToString()))
                            {
                                decimal profitPerLot;
                                if (decimal.TryParse(item.yqhl.ToString(), out profitPerLot))
                                {
                                    newStock.ProfitPerLot = profitPerLot;
                                }
                            }

                            // 解析中签号公布日期
                            if (item.zqgbrq != null && item.zqgbrq.ToString() != "null" && !string.IsNullOrEmpty(item.zqgbrq.ToString()))
                            {
                                DateTime winningNumberDate;
                                if (DateTime.TryParseExact(item.zqgbrq.ToString(), "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out winningNumberDate) ||
                                    DateTime.TryParse(item.zqgbrq.ToString(), out winningNumberDate))
                                {
                                    newStock.WinningNumberDate = winningNumberDate;
                                }
                            }

                            // 解析中签缴款日
                            if (item.zqjkrq != null && item.zqjkrq.ToString() != "null" && !string.IsNullOrEmpty(item.zqjkrq.ToString()))
                            {
                                DateTime paymentDate;
                                if (DateTime.TryParseExact(item.zqjkrq.ToString(), "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate) ||
                                    DateTime.TryParse(item.zqjkrq.ToString(), out paymentDate))
                                {
                                    newStock.PaymentDate = paymentDate;
                                }
                            }

                            // 解析上市日期
                            if (item.ssrq != null && item.ssrq.ToString() != "null" && !string.IsNullOrEmpty(item.ssrq.ToString()))
                            {
                                DateTime listingDate;
                                if (DateTime.TryParseExact(item.ssrq.ToString(), "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out listingDate) ||
                                    DateTime.TryParse(item.ssrq.ToString(), out listingDate))
                                {
                                    newStock.ListingDate = listingDate;
                                }
                            }

                            newStockInfoList.Add(newStock);
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"解析新股发行数据项时出错: {ex.Message}");
                        }
                    }
                }

                return newStockInfoList;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取新股发行数据时出错: {ex.Message}");
                throw;
            }
        }
        /// <summary>
        /// 获取股票的季度利润数据
        /// </summary>
        /// <param name="stockCode">股票代码</param>
        /// <returns>季度利润数据列表</returns>
        public async Task<List<QuarterlyProfitData>> GetQuarterlyProfitDataAsync(string stockCode)
        {
            try
            {
                // 确保股票代码格式正确
                stockCode = stockCode.Trim();

                // 构建API URL - 确保与示例URL完全一致
                string url = $"http://api.mairuiapi.com/hscp/jdlr/{stockCode}/{_apiKey}";

                // 输出完整URL，用于调试
                Debug.WriteLine($"完整API URL: {url}");
                // 示例URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98

                Debug.WriteLine($"请求季度利润数据: {url}");

                // 发送请求
                string response;
                try
                {
                    response = await _httpClient.GetStringAsync(url);
                    Debug.WriteLine($"季度利润数据响应成功，长度: {response.Length}");
                    Debug.WriteLine($"响应内容: {response}");
                }
                catch (HttpRequestException ex)
                {
                    Debug.WriteLine($"HTTP请求错误: {ex.Message}");
                    if (ex.StatusCode.HasValue)
                    {
                        Debug.WriteLine($"HTTP状态码: {(int)ex.StatusCode.Value} ({ex.StatusCode.Value})");
                    }
                    throw new Exception($"获取季度利润数据失败: {ex.Message}", ex);
                }

                // 解析JSON响应
                var profitDataList = new List<QuarterlyProfitData>();

                try
                {
                    // 尝试解析JSON数据
                    var jsonData = JsonConvert.DeserializeObject<dynamic>(response);

                    // 检查是否是数组
                    if (jsonData is Newtonsoft.Json.Linq.JArray jArray)
                    {
                        Debug.WriteLine($"成功解析为JSON数组，包含 {jArray.Count} 个元素");

                        foreach (var item in jArray)
                        {
                            try
                            {
                                // 输出每个项的内容，帮助调试
                                Debug.WriteLine($"处理项: {item}");

                                // 尝试获取各个字段
                                string reportDate = item["date"]?.ToString() ?? string.Empty;
                                string revenue = item["revenue"]?.ToString() ?? string.Empty;
                                string revenueYoY = item["revenue_yoy"]?.ToString() ?? string.Empty;
                                string netProfit = item["net_profit"]?.ToString() ?? string.Empty;
                                string netProfitYoY = item["net_profit_yoy"]?.ToString() ?? string.Empty;

                                Debug.WriteLine($"解析字段 - 报告期: {reportDate}, 营收: {revenue}, 营收同比: {revenueYoY}, 净利润: {netProfit}, 净利润同比: {netProfitYoY}");

                                var profitData = QuarterlyProfitData.FromApiData(item);

                                profitDataList.Add(profitData);
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"解析季度利润数据项时出错: {ex.Message}");
                            }
                        }
                    }
                    else if (jsonData is Newtonsoft.Json.Linq.JObject jObject)
                    {
                        // 如果是对象而不是数组，可能是错误信息或其他格式
                        Debug.WriteLine($"API返回了JSON对象而不是数组: {jObject}");

                        // 尝试获取错误信息
                        string errorMsg = jObject["msg"]?.ToString() ?? "未知错误";
                        throw new Exception($"API返回错误: {errorMsg}");
                    }
                    else
                    {
                        Debug.WriteLine($"无法识别的JSON格式: {jsonData?.GetType().Name}");
                    }
                }
                catch (JsonException ex)
                {
                    Debug.WriteLine($"JSON解析错误: {ex.Message}");
                    throw new Exception($"解析季度利润数据失败: {ex.Message}", ex);
                }

                return profitDataList;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取季度利润数据时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 格式化金额，添加千位分隔符
        /// </summary>
        private static string FormatAmount(dynamic value)
        {
            if (value == null)
                return "-";

            string strValue;
            try
            {
                strValue = value.ToString();
                if (string.IsNullOrEmpty(strValue) || strValue == "null")
                    return "-";
            }
            catch
            {
                return "-";
            }

            if (decimal.TryParse(strValue, out decimal amount))
            {
                // 如果金额大于1百万，显示为"xx.xx百万"
                if (amount > 1000000)
                {
                    return $"{amount / 1000000:N2}百万";
                }
                // 否则使用千位分隔符
                return amount.ToString("N0");
            }

            return strValue;
        }

        /// <summary>
        /// 格式化百分比
        /// </summary>
        private static string FormatPercentage(dynamic value)
        {
            if (value == null)
                return "-";

            string strValue;
            try
            {
                strValue = value.ToString();
                if (string.IsNullOrEmpty(strValue) || strValue == "null")
                    return "-";
            }
            catch
            {
                return "-";
            }

            if (decimal.TryParse(strValue, out decimal percentage))
            {
                // 添加百分号
                return $"{percentage:F2}%";
            }

            return strValue;
        }
    }
}
