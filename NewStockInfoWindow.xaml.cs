using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Controls;
using System.Diagnostics;
using System.ComponentModel;
using StockCrawler.Models;

namespace StockCrawler
{
    /// <summary>
    /// NewStockInfoWindow.xaml 的交互逻辑
    /// </summary>
    public partial class NewStockInfoWindow : Window
    {
        private List<NewStockInfo> _newStockInfoList;

        public NewStockInfoWindow(List<NewStockInfo> newStockInfoList)
        {
            InitializeComponent();

            _newStockInfoList = newStockInfoList;

            // 过滤出当前日期之后的新股信息
            var filteredList = _newStockInfoList
                .Where(info => info.PurchaseDate >= DateTime.Today)
                .OrderBy(info => info.PurchaseDate)
                .ToList();

            // 设置数据源
            NewStockDataGrid.ItemsSource = filteredList;

            // 更新提示信息
            if (filteredList.Count == 0)
            {
                InfoTextBlock.Text = "暂无未来新股发行信息";
            }
            else
            {
                InfoTextBlock.Text = $"注：仅显示当前日期之后的新股发行信息";
            }

            // 更新记录数量
            CountTextBlock.Text = $"共 {filteredList.Count} 条记录";

            // 设置窗口加载完成事件
            Loaded += NewStockInfoWindow_Loaded;

            // 设置窗口关闭事件
            Closing += NewStockInfoWindow_Closing;

            Debug.WriteLine($"显示 {filteredList.Count} 条新股发行信息");
        }

        private void NewStockInfoWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 优化的窗口加载逻辑
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        // 强制刷新DataGrid
                        NewStockDataGrid.Items.Refresh();

                        // 自动调整所有列宽以适应内容和标题
                        foreach (var column in NewStockDataGrid.Columns)
                        {
                            // 详细按钮列不自动调整宽度
                            if (column == NewStockDataGrid.Columns[NewStockDataGrid.Columns.Count - 1])
                                continue;

                            // 自动调整列宽以适应内容
                            column.Width = new DataGridLength(1, DataGridLengthUnitType.Auto);
                        }

                        // 恢复到设置的固定宽度，确保内容完全显示
                        foreach (var column in NewStockDataGrid.Columns)
                        {
                            if (column.Width.UnitType == DataGridLengthUnitType.Auto)
                            {
                                // 获取自动调整后的宽度
                                double actualWidth = column.ActualWidth;

                                // 设置为固定宽度，但不小于当前宽度，并添加更多额外空间
                                double minWidth = double.Parse(column.Width.DesiredValue.ToString());

                                // 为特定列添加更多空间
                                double extraSpace = 15;
                                if (column.Header.ToString() == "申购日期" ||
                                    column.Header.ToString() == "申购上限" ||
                                    column.Header.ToString() == "中签号公布日" ||
                                    column.Header.ToString() == "中签缴款日" ||
                                    column.Header.ToString() == "上市日期")
                                {
                                    extraSpace = 25;
                                }

                                column.Width = new DataGridLength(Math.Max(actualWidth, minWidth) + extraSpace, DataGridLengthUnitType.Pixel);
                            }
                        }

                        // 设置最小窗口宽度以确保所有列都能显示
                        double totalWidth = NewStockDataGrid.Columns.Sum(c => c.ActualWidth) + 50; // 添加一些额外空间
                        MinWidth = totalWidth + 50; // 窗口边距

                        // 调整窗口大小以适应内容
                        if (Width < MinWidth)
                        {
                            Width = MinWidth;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"调整DataGrid列宽时出错: {ex.Message}");
                    }
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"窗口加载事件处理出错: {ex.Message}");
            }
        }

        private void NewStockInfoWindow_Closing(object sender, CancelEventArgs e)
        {
            // 在窗口关闭时可以保存用户的列宽设置
            // 这里可以添加保存列宽的代码
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void DetailButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取当前行的数据
                Button button = (Button)sender;
                NewStockInfo stockInfo = (NewStockInfo)button.DataContext;

                // 创建并显示公司详情窗口
                var detailWindow = new CompanyDetailWindow(stockInfo);
                detailWindow.Owner = this;
                detailWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示公司详情时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Debug.WriteLine($"显示公司详情异常: {ex}");
            }
        }
    }
}
