using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;

namespace StockCrawler
{
    public static class LogHelper
    {
        private static readonly string LogFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "app_log.txt");
        
        static LogHelper()
        {
            // 确保日志文件存在
            try
            {
                if (!File.Exists(LogFilePath))
                {
                    using (var fs = File.Create(LogFilePath))
                    {
                        // 创建文件
                    }
                }
                
                // 写入日志头
                File.AppendAllText(LogFilePath, $"=== 日志开始于 {DateTime.Now} ===\r\n");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化日志文件时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 记录信息日志
        /// </summary>
        public static void LogInfo(string message)
        {
            Log("INFO", message);
        }
        
        /// <summary>
        /// 记录错误日志
        /// </summary>
        public static void LogError(string message, Exception ex = null)
        {
            string errorMessage = ex != null ? $"{message} - 异常: {ex.Message}\r\n堆栈: {ex.StackTrace}" : message;
            Log("ERROR", errorMessage);
        }
        
        /// <summary>
        /// 记录调试日志
        /// </summary>
        public static void LogDebug(string message)
        {
            Log("DEBUG", message);
        }
        
        /// <summary>
        /// 记录API调用日志
        /// </summary>
        public static void LogApi(string url, string response)
        {
            try
            {
                string logMessage = $"API调用: {url}\r\n响应: {response}";
                Log("API", logMessage);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"记录API日志时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 记录日志
        /// </summary>
        private static void Log(string level, string message)
        {
            try
            {
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level}] {message}\r\n";
                
                // 输出到调试窗口
                Debug.WriteLine(logEntry);
                
                // 写入文件
                Task.Run(() => 
                {
                    try
                    {
                        File.AppendAllText(LogFilePath, logEntry);
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"写入日志文件时出错: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"记录日志时出错: {ex.Message}");
            }
        }
    }
}
