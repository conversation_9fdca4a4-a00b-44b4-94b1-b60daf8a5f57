using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace StockCrawler.Converters
{
    // 将颜色转换为背景色的转换器
    public class ColorToBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SolidColorBrush brush)
            {
                Color color = brush.Color;
                
                if (color == Colors.Red)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFF1F0"));
                }
                else if (color == Colors.Green)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F6FFED"));
                }
                else
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5"));
                }
            }
            return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // 将颜色转换为边框色的转换器
    public class ColorToBorderConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SolidColorBrush brush)
            {
                Color color = brush.Color;
                
                if (color == Colors.Red)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFCCC7"));
                }
                else if (color == Colors.Green)
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#B7EB8F"));
                }
                else
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D9D9D9"));
                }
            }
            return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D9D9D9"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
