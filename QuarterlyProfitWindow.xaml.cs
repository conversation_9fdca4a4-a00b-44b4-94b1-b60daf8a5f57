using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using StockCrawler.Services;

namespace StockCrawler
{
    /// <summary>
    /// QuarterlyProfitWindow.xaml 的交互逻辑
    /// </summary>
    public partial class QuarterlyProfitWindow : Window
    {
        private readonly string _stockCode;
        private readonly string _stockName;

        public QuarterlyProfitWindow(string stockCode, string stockName)
        {
            InitializeComponent();

            _stockCode = stockCode;
            _stockName = stockName;

            // 设置窗口标题
            this.Title = $"{stockName}({stockCode}) - 近一年各季度利润";

            // 允许窗口拖动
            this.MouseLeftButtonDown += (s, e) =>
            {
                if (e.LeftButton == MouseButtonState.Pressed)
                {
                    DragMove();
                }
            };
        }

        // 关闭按钮点击事件
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                LogHelper.LogInfo($"季度利润窗口加载 - 股票代码: {_stockCode}, 股票名称: {_stockName}");

                // 更新UI显示
                StockNameTextBlock.Text = _stockName;
                StockCodeTextBlock.Text = _stockCode;
                StatusTextBlock.Text = "正在加载数据...";
                LoadingProgressBar.Visibility = Visibility.Visible;

                try
                {
                    LogHelper.LogInfo($"开始获取股票 {_stockCode} 的季度利润数据");

                    // 获取季度利润数据
                    var profitData = await MairuiService.GetQuarterlyProfitAsync(_stockCode);

                    LogHelper.LogInfo($"获取到 {(profitData?.Count ?? 0)} 条季度利润数据");

                    if (profitData != null && profitData.Count > 0)
                    {
                        // 输出获取到的数据
                        foreach (var item in profitData)
                        {
                            LogHelper.LogDebug($"季度利润数据: 报告期={item.ReportDate}, 营收={item.Revenue}, 稀释每股收益={item.DilutedEPS}, 净利润={item.NetProfit}, 综合收益总额={item.TotalIncome}");
                        }

                        // 确保在UI线程上更新DataGrid
                        this.Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                LogHelper.LogInfo($"开始绑定 {profitData.Count} 条季度利润数据到UI");

                                // 创建一个ObservableCollection，确保UI能够正确更新
                                var observableData = new System.Collections.ObjectModel.ObservableCollection<QuarterlyProfitData>(profitData);

                                // 绑定数据到DataGrid
                                ProfitDataGrid.ItemsSource = observableData;

                                // 强制刷新DataGrid
                                ProfitDataGrid.Items.Refresh();

                                // 更新状态信息
                                StatusTextBlock.Text = $"已加载 {profitData.Count} 条季度利润数据";

                                LogHelper.LogInfo($"成功绑定 {profitData.Count} 条季度利润数据到UI");

                                // 输出DataGrid的状态
                                LogHelper.LogInfo($"DataGrid.Items.Count = {ProfitDataGrid.Items.Count}");
                                LogHelper.LogInfo($"DataGrid.HasItems = {ProfitDataGrid.HasItems}");
                            }
                            catch (Exception ex)
                            {
                                LogHelper.LogError($"绑定数据到UI时出错: {ex.Message}", ex);
                            }
                        });
                    }
                    else
                    {
                        StatusTextBlock.Text = "未找到季度利润数据";
                        LogHelper.LogError("未找到季度利润数据");

                        // 创建一些测试数据，以便验证UI显示
                        LogHelper.LogInfo("创建测试数据...");

                        var testData = new List<QuarterlyProfitData>
                        {
                            new QuarterlyProfitData
                            {
                                ReportDate = "2023-12-31",
                                Revenue = "1,234,567",
                                DilutedEPS = "0.45",
                                NetProfit = "456,789",
                                TotalIncome = "460,000"
                            },
                            new QuarterlyProfitData
                            {
                                ReportDate = "2023-09-30",
                                Revenue = "987,654",
                                DilutedEPS = "0.38",
                                NetProfit = "345,678",
                                TotalIncome = "350,000"
                            },
                            new QuarterlyProfitData
                            {
                                ReportDate = "2023-06-30",
                                Revenue = "876,543",
                                DilutedEPS = "0.25",
                                NetProfit = "234,567",
                                TotalIncome = "240,000"
                            },
                            new QuarterlyProfitData
                            {
                                ReportDate = "2023-03-31",
                                Revenue = "765,432",
                                DilutedEPS = "0.15",
                                NetProfit = "123,456",
                                TotalIncome = "125,000"
                            }
                        };

                        // 确保在UI线程上更新DataGrid
                        this.Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                LogHelper.LogInfo($"开始绑定 {testData.Count} 条测试数据到UI");

                                // 创建一个ObservableCollection，确保UI能够正确更新
                                var observableData = new System.Collections.ObjectModel.ObservableCollection<QuarterlyProfitData>(testData);

                                // 绑定数据到DataGrid
                                ProfitDataGrid.ItemsSource = observableData;

                                // 强制刷新DataGrid
                                ProfitDataGrid.Items.Refresh();

                                // 更新状态信息
                                StatusTextBlock.Text = $"已加载 {testData.Count} 条测试数据";

                                LogHelper.LogInfo($"成功绑定 {testData.Count} 条测试数据到UI");

                                // 输出DataGrid的状态
                                LogHelper.LogInfo($"DataGrid.Items.Count = {ProfitDataGrid.Items.Count}");
                                LogHelper.LogInfo($"DataGrid.HasItems = {ProfitDataGrid.HasItems}");
                            }
                            catch (Exception ex)
                            {
                                LogHelper.LogError($"绑定测试数据到UI时出错: {ex.Message}", ex);
                            }
                        });
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.LogError($"获取季度利润数据时出错: {ex.Message}", ex);
                    StatusTextBlock.Text = $"加载数据失败: {ex.Message}";
                    MessageBox.Show($"获取季度利润数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                    // 创建一些测试数据，以便验证UI显示
                    LogHelper.LogInfo("创建测试数据...");

                    var testData = new List<QuarterlyProfitData>
                    {
                        new QuarterlyProfitData
                        {
                            ReportDate = "2023-12-31",
                            Revenue = "1,234,567",
                            DilutedEPS = "0.45",
                            NetProfit = "456,789",
                            TotalIncome = "460,000"
                        },
                        new QuarterlyProfitData
                        {
                            ReportDate = "2023-09-30",
                            Revenue = "987,654",
                            DilutedEPS = "0.38",
                            NetProfit = "345,678",
                            TotalIncome = "350,000"
                        }
                    };

                    // 确保在UI线程上更新DataGrid
                    this.Dispatcher.Invoke(() =>
                    {
                        try
                        {
                            LogHelper.LogInfo($"开始绑定 {testData.Count} 条错误恢复测试数据到UI");

                            // 创建一个ObservableCollection，确保UI能够正确更新
                            var observableData = new System.Collections.ObjectModel.ObservableCollection<QuarterlyProfitData>(testData);

                            // 绑定数据到DataGrid
                            ProfitDataGrid.ItemsSource = observableData;

                            // 强制刷新DataGrid
                            ProfitDataGrid.Items.Refresh();

                            // 更新状态信息
                            StatusTextBlock.Text = $"已加载 {testData.Count} 条测试数据";

                            LogHelper.LogInfo($"成功绑定 {testData.Count} 条错误恢复测试数据到UI");

                            // 输出DataGrid的状态
                            LogHelper.LogInfo($"DataGrid.Items.Count = {ProfitDataGrid.Items.Count}");
                            LogHelper.LogInfo($"DataGrid.HasItems = {ProfitDataGrid.HasItems}");
                        }
                        catch (Exception ex)
                        {
                            LogHelper.LogError($"绑定错误恢复测试数据到UI时出错: {ex.Message}", ex);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"季度利润窗口加载时出错: {ex.Message}", ex);
                StatusTextBlock.Text = $"加载数据失败: {ex.Message}";
                MessageBox.Show($"获取季度利润数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                LoadingProgressBar.Visibility = Visibility.Collapsed;
                LogHelper.LogInfo("季度利润窗口加载完成");
            }
        }
    }

    /// <summary>
    /// 季度利润数据模型
    /// </summary>
    public class QuarterlyProfitData
    {
        /// <summary>
        /// 报告期
        /// </summary>
        public string ReportDate { get; set; } = string.Empty;

        /// <summary>
        /// 营业收入
        /// </summary>
        public string Revenue { get; set; } = string.Empty;

        /// <summary>
        /// 稀释每股收益
        /// </summary>
        public string DilutedEPS { get; set; } = string.Empty;

        /// <summary>
        /// 净利润
        /// </summary>
        public string NetProfit { get; set; } = string.Empty;

        /// <summary>
        /// 综合收益总额
        /// </summary>
        public string TotalIncome { get; set; } = string.Empty;

        /// <summary>
        /// 从API返回的数据创建季度利润数据对象
        /// </summary>
        public static QuarterlyProfitData FromApiData(dynamic item)
        {
            var data = new QuarterlyProfitData();

            // 设置报告期，确保格式正确
            if (item.date != null)
            {
                string dateStr = item.date?.ToString() ?? string.Empty;
                // 尝试解析日期并格式化为标准格式
                if (DateTime.TryParse(dateStr, out DateTime reportDate))
                {
                    data.ReportDate = reportDate.ToString("yyyy-MM-dd");
                }
                else
                {
                    // 如果无法解析，直接使用原始字符串
                    data.ReportDate = dateStr;
                }

                LogHelper.LogDebug($"解析报告期: 原始值={dateStr}, 格式化后={data.ReportDate}");
            }
            else
            {
                data.ReportDate = string.Empty;
            }

            // 设置营业收入
            if (item.income != null)
            {
                data.Revenue = FormatAmount(item.income.ToString());
            }

            // 设置净利润
            if (item.profit != null)
            {
                data.NetProfit = FormatAmount(item.profit.ToString());
            }

            // 设置稀释每股收益（使用ettege字段）
            if (item.ettege != null)
            {
                data.DilutedEPS = item.ettege.ToString();
            }
            else
            {
                data.DilutedEPS = "-";
            }

            // 设置综合收益总额（使用totalp字段）
            if (item.totalp != null)
            {
                data.TotalIncome = FormatAmount(item.totalp.ToString());
            }
            else
            {
                data.TotalIncome = "-";
            }

            return data;
        }

        /// <summary>
        /// 格式化金额，添加千位分隔符
        /// </summary>
        private static string FormatAmount(string value)
        {
            if (string.IsNullOrEmpty(value) || value == "null" || value == "-")
                return "-";

            if (decimal.TryParse(value, out decimal amount))
            {
                // 如果金额大于1百万，显示为"xx.xx百万"
                if (amount > 1000000)
                {
                    return $"{amount / 1000000:N2}百万";
                }
                // 否则使用千位分隔符
                return amount.ToString("N0");
            }

            return value;
        }
    }
}
