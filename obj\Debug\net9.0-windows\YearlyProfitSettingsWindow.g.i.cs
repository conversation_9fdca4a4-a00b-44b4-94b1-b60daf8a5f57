﻿#pragma checksum "..\..\..\YearlyProfitSettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "59DF2E8E0DD7552172A6F10B8ED209E6D7679A42"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using StockCrawler;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace StockCrawler {
    
    
    /// <summary>
    /// YearlyProfitSettingsWindow
    /// </summary>
    public partial class YearlyProfitSettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 155 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbUserName;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbYear;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtInitialAsset;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTransferIn;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTransferOut;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtNetTransfer;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPeriodProfit;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtFinalAsset;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtYearlyReturnRate;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSave;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\YearlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/StockCrawler;V1.0.0.0;component/yearlyprofitsettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\YearlyProfitSettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\YearlyProfitSettingsWindow.xaml"
            ((StockCrawler.YearlyProfitSettingsWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.Window_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 121 "..\..\..\YearlyProfitSettingsWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 125 "..\..\..\YearlyProfitSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.cmbUserName = ((System.Windows.Controls.ComboBox)(target));
            
            #line 156 "..\..\..\YearlyProfitSettingsWindow.xaml"
            this.cmbUserName.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbUserName_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.cmbYear = ((System.Windows.Controls.ComboBox)(target));
            
            #line 159 "..\..\..\YearlyProfitSettingsWindow.xaml"
            this.cmbYear.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbYear_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.txtInitialAsset = ((System.Windows.Controls.TextBox)(target));
            
            #line 181 "..\..\..\YearlyProfitSettingsWindow.xaml"
            this.txtInitialAsset.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValueTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.txtTransferIn = ((System.Windows.Controls.TextBox)(target));
            
            #line 186 "..\..\..\YearlyProfitSettingsWindow.xaml"
            this.txtTransferIn.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValueTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.txtTransferOut = ((System.Windows.Controls.TextBox)(target));
            
            #line 191 "..\..\..\YearlyProfitSettingsWindow.xaml"
            this.txtTransferOut.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValueTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.txtNetTransfer = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.txtPeriodProfit = ((System.Windows.Controls.TextBox)(target));
            
            #line 201 "..\..\..\YearlyProfitSettingsWindow.xaml"
            this.txtPeriodProfit.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValueTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.txtFinalAsset = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.txtYearlyReturnRate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.btnSave = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\YearlyProfitSettingsWindow.xaml"
            this.btnSave.Click += new System.Windows.RoutedEventHandler(this.btnSave_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\YearlyProfitSettingsWindow.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.btnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

