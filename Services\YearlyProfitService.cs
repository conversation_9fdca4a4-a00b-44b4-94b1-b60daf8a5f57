using StockCrawler.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace StockCrawler.Services
{
    /// <summary>
    /// 年度盈亏数据服务
    /// </summary>
    public class YearlyProfitService
    {
        private const string CSV_FILE_NAME = "yearly_profits.csv";
        private static readonly string CSV_FILE_PATH = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, CSV_FILE_NAME);

        /// <summary>
        /// 保存年度盈亏数据到CSV文件
        /// </summary>
        public static void SaveYearlyProfitData(List<YearlyProfitData> dataList)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(CSV_FILE_PATH, false, Encoding.UTF8))
                {
                    // 写入CSV头
                    writer.WriteLine("UserName,Year,InitialAsset,TransferIn,TransferOut,PeriodProfit");

                    // 写入数据
                    foreach (var data in dataList)
                    {
                        writer.WriteLine($"{data.UserName},{data.Year},{data.InitialAsset},{data.TransferIn},{data.TransferOut},{data.PeriodProfit}");
                    }
                }

                LogHelper.LogInfo($"成功保存年度盈亏数据到 {CSV_FILE_PATH}");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"保存年度盈亏数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从CSV文件加载年度盈亏数据
        /// </summary>
        public static List<YearlyProfitData> LoadYearlyProfitData()
        {
            List<YearlyProfitData> dataList = new List<YearlyProfitData>();

            try
            {
                if (!File.Exists(CSV_FILE_PATH))
                {
                    LogHelper.LogInfo($"年度盈亏数据文件不存在: {CSV_FILE_PATH}");
                    return dataList;
                }

                using (StreamReader reader = new StreamReader(CSV_FILE_PATH, Encoding.UTF8))
                {
                    // 跳过CSV头
                    string header = reader.ReadLine();

                    // 读取数据行
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        if (string.IsNullOrWhiteSpace(line))
                            continue;

                        string[] values = line.Split(',');
                        if (values.Length < 6)
                            continue;

                        YearlyProfitData data = new YearlyProfitData
                        {
                            UserName = values[0]
                        };

                        // 解析年度数据
                        if (int.TryParse(values[1], out int year))
                            data.Year = year;

                        if (decimal.TryParse(values[2], out decimal initialAsset))
                            data.InitialAsset = initialAsset;

                        if (decimal.TryParse(values[3], out decimal transferIn))
                            data.TransferIn = transferIn;

                        if (decimal.TryParse(values[4], out decimal transferOut))
                            data.TransferOut = transferOut;

                        if (decimal.TryParse(values[5], out decimal periodProfit))
                            data.PeriodProfit = periodProfit;

                        dataList.Add(data);
                    }
                }

                LogHelper.LogInfo($"成功加载年度盈亏数据，共 {dataList.Count} 条记录");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"加载年度盈亏数据失败: {ex.Message}");
            }

            return dataList;
        }

        /// <summary>
        /// 获取或创建用户的年度盈亏数据
        /// </summary>
        public static YearlyProfitData GetOrCreateUserYearData(string userName, int year)
        {
            List<YearlyProfitData> dataList = LoadYearlyProfitData();
            YearlyProfitData userData = dataList.FirstOrDefault(d =>
                d.UserName == userName && d.Year == year);

            if (userData == null)
            {
                userData = new YearlyProfitData
                {
                    UserName = userName,
                    Year = year
                };
                dataList.Add(userData);
                SaveYearlyProfitData(dataList);
            }

            return userData;
        }

        /// <summary>
        /// 更新用户的年度盈亏数据
        /// </summary>
        public static void UpdateUserYearData(YearlyProfitData userData)
        {
            List<YearlyProfitData> dataList = LoadYearlyProfitData();
            YearlyProfitData existingData = dataList.FirstOrDefault(d =>
                d.UserName == userData.UserName &&
                d.Year == userData.Year);

            if (existingData != null)
            {
                // 移除现有数据
                dataList.Remove(existingData);
            }

            // 添加新数据
            dataList.Add(userData);

            SaveYearlyProfitData(dataList);
        }

        /// <summary>
        /// 获取用户从2017年至今的汇总数据
        /// </summary>
        public static Dictionary<string, decimal> GetUserSummaryData(string userName)
        {
            Dictionary<string, decimal> result = new Dictionary<string, decimal>
            {
                { "TotalPeriodProfit", 0 },  // 总盈利值
                { "TotalTransferIn", 0 },    // 总转入值
                { "TotalTransferOut", 0 },   // 总转出值
                { "NetTransfer", 0 },        // 净转入转出值
                { "TotalInitialAsset", 0 },  // 起初资产（2017年）
                { "TotalFinalAsset", 0 }     // 最终资产（当前年）
            };

            try
            {
                // 加载所有年度数据
                List<YearlyProfitData> allData = LoadYearlyProfitData();

                // 筛选当前用户的数据并按年份排序
                var userYearlyData = allData
                    .Where(d => d.UserName == userName)
                    .OrderBy(d => d.Year)
                    .ToList();

                if (userYearlyData.Count == 0)
                {
                    LogHelper.LogInfo($"未找到用户 {userName} 的年度盈亏数据");
                    return result;
                }

                // 计算汇总数据
                foreach (var data in userYearlyData)
                {
                    result["TotalPeriodProfit"] += data.PeriodProfit;
                    result["TotalTransferIn"] += data.TransferIn;
                    result["TotalTransferOut"] += data.TransferOut;
                }

                // 计算净转入转出值
                result["NetTransfer"] = result["TotalTransferIn"] - result["TotalTransferOut"];

                // 获取最早年份的起初资产（如果有2017年数据）
                var earliestData = userYearlyData.FirstOrDefault();
                if (earliestData != null)
                {
                    result["TotalInitialAsset"] = earliestData.InitialAsset;
                }

                // 获取最新年份的期末资产
                var latestData = userYearlyData.LastOrDefault();
                if (latestData != null)
                {
                    result["TotalFinalAsset"] = latestData.FinalAsset;
                }

                // 计算总收益率
                if (result["TotalInitialAsset"] + result["NetTransfer"] / 2 != 0)
                {
                    result["TotalReturnRate"] = result["TotalPeriodProfit"] / (result["TotalInitialAsset"] + result["NetTransfer"] / 2);
                }

                LogHelper.LogInfo($"成功计算用户 {userName} 的汇总数据: " +
                    $"总盈利={result["TotalPeriodProfit"]}, " +
                    $"总转入={result["TotalTransferIn"]}, " +
                    $"总转出={result["TotalTransferOut"]}, " +
                    $"净转入转出={result["NetTransfer"]}");
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"计算用户 {userName} 的汇总数据失败: {ex.Message}");
            }

            return result;
        }
    }
}
