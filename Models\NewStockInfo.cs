using System;

namespace StockCrawler.Models
{
    /// <summary>
    /// 新股信息模型
    /// </summary>
    public class NewStockInfo
    {
        /// <summary>
        /// 股票代码
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 股票名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 申购代码
        /// </summary>
        public string PurchaseCode { get; set; } = string.Empty;

        /// <summary>
        /// 发行价格
        /// </summary>
        public decimal IssuePrice { get; set; }

        /// <summary>
        /// 申购上限（股）
        /// </summary>
        public decimal PurchaseLimit { get; set; }

        /// <summary>
        /// 顶格申购需配市值(元)
        /// </summary>
        public decimal TopPurchaseMarketValue { get; set; }

        /// <summary>
        /// 发行市盈率
        /// </summary>
        public decimal IssuePERatio { get; set; }

        /// <summary>
        /// 行业市盈率
        /// </summary>
        public decimal IndustryPERatio { get; set; }

        /// <summary>
        /// 申购日期
        /// </summary>
        public DateTime PurchaseDate { get; set; }

        /// <summary>
        /// 中签号公布日期
        /// </summary>
        public DateTime? WinningNumberDate { get; set; }

        /// <summary>
        /// 中签缴款日
        /// </summary>
        public DateTime? PaymentDate { get; set; }

        /// <summary>
        /// 上市日期
        /// </summary>
        public DateTime? ListingDate { get; set; }

        /// <summary>
        /// 发行总数（股）
        /// </summary>
        public decimal TotalIssue { get; set; }

        /// <summary>
        /// 网上发行（股）
        /// </summary>
        public decimal OnlineIssue { get; set; }

        /// <summary>
        /// 最新价（元）
        /// </summary>
        public decimal? LatestPrice { get; set; }

        /// <summary>
        /// 首日收盘价（元）
        /// </summary>
        public decimal? FirstDayClosingPrice { get; set; }

        /// <summary>
        /// 中签率（%）
        /// </summary>
        public decimal? WinningRate { get; set; }

        /// <summary>
        /// 连续一字板数量
        /// </summary>
        public int? ConsecutiveLimitUpDays { get; set; }

        /// <summary>
        /// 涨幅（%）
        /// </summary>
        public decimal? PriceIncrease { get; set; }

        /// <summary>
        /// 每中一签获利（元）
        /// </summary>
        public decimal? ProfitPerLot { get; set; }

        /// <summary>
        /// 主营业务
        /// </summary>
        public string MainBusiness { get; set; } = string.Empty;

        /// <summary>
        /// 主营业务摘要（截取前30个字符）
        /// </summary>
        public string MainBusinessSummary
        {
            get
            {
                if (string.IsNullOrEmpty(MainBusiness))
                    return "暂无信息";

                if (MainBusiness.Length <= 30)
                    return MainBusiness;

                return MainBusiness.Substring(0, 30) + "...";
            }
        }

        /// <summary>
        /// 格式化后的申购日期
        /// </summary>
        public string FormattedPurchaseDate => PurchaseDate.ToString("yyyy-MM-dd");

        /// <summary>
        /// 格式化后的中签号公布日期
        /// </summary>
        public string FormattedWinningNumberDate => WinningNumberDate?.ToString("yyyy-MM-dd") ?? "未知";

        /// <summary>
        /// 格式化后的中签缴款日
        /// </summary>
        public string FormattedPaymentDate => PaymentDate?.ToString("yyyy-MM-dd") ?? "未知";

        /// <summary>
        /// 格式化后的上市日期
        /// </summary>
        public string FormattedListingDate => ListingDate?.ToString("yyyy-MM-dd") ?? "未知";

        /// <summary>
        /// 格式化后的发行价格
        /// </summary>
        public string FormattedIssuePrice => IssuePrice > 0 ? $"{IssuePrice:F2}元" : "未知";

        /// <summary>
        /// 格式化后的申购上限
        /// </summary>
        public string FormattedPurchaseLimit => PurchaseLimit > 0 ? $"{PurchaseLimit:N0}股" : "未知";

        /// <summary>
        /// 格式化后的顶格申购需配市值
        /// </summary>
        public string FormattedTopPurchaseMarketValue => TopPurchaseMarketValue > 0 ? $"{TopPurchaseMarketValue:N0}元" : "未知";

        /// <summary>
        /// 格式化后的发行市盈率
        /// </summary>
        public string FormattedIssuePERatio => IssuePERatio > 0 ? $"{IssuePERatio:F2}" : "未知";

        /// <summary>
        /// 格式化后的行业市盈率
        /// </summary>
        public string FormattedIndustryPERatio => IndustryPERatio > 0 ? $"{IndustryPERatio:F2}" : "未知";

        /// <summary>
        /// 格式化后的发行总数
        /// </summary>
        public string FormattedTotalIssue => TotalIssue > 0 ? $"{TotalIssue:N0}股" : "未知";

        /// <summary>
        /// 格式化后的网上发行
        /// </summary>
        public string FormattedOnlineIssue => OnlineIssue > 0 ? $"{OnlineIssue:N0}股" : "未知";

        /// <summary>
        /// 格式化后的最新价
        /// </summary>
        public string FormattedLatestPrice => LatestPrice.HasValue ? $"{LatestPrice.Value:F2}元" : "未知";

        /// <summary>
        /// 格式化后的首日收盘价
        /// </summary>
        public string FormattedFirstDayClosingPrice => FirstDayClosingPrice.HasValue ? $"{FirstDayClosingPrice.Value:F2}元" : "未知";

        /// <summary>
        /// 格式化后的中签率
        /// </summary>
        public string FormattedWinningRate => WinningRate.HasValue ? $"{WinningRate.Value:F4}%" : "未知";

        /// <summary>
        /// 格式化后的涨幅
        /// </summary>
        public string FormattedPriceIncrease => PriceIncrease.HasValue ? $"{PriceIncrease.Value:F2}%" : "未知";

        /// <summary>
        /// 格式化后的每中一签获利
        /// </summary>
        public string FormattedProfitPerLot => ProfitPerLot.HasValue ? $"{ProfitPerLot.Value:F2}元" : "未知";
    }
}
