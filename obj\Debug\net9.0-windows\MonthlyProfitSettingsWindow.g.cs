﻿#pragma checksum "..\..\..\MonthlyProfitSettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6092BCB73E00EA02D5FF5C18B3D1CDBAE8C0BE4A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using StockCrawler;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace StockCrawler {
    
    
    /// <summary>
    /// MonthlyProfitSettingsWindow
    /// </summary>
    public partial class MonthlyProfitSettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 157 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbUserName;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnLoadUser;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCalculate;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnYearlyProfit;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnStatistics;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtJanuary;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtFebruary;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtMarch;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtApril;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtMay;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtJune;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtJuly;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtAugust;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSeptember;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtOctober;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtNovember;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDecember;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtYearlyTotal;
        
        #line default
        #line hidden
        
        
        #line 328 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtYearToDateTotal;
        
        #line default
        #line hidden
        
        
        #line 335 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSave;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\MonthlyProfitSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/StockCrawler;component/monthlyprofitsettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            ((StockCrawler.MonthlyProfitSettingsWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.Window_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 121 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 125 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.cmbUserName = ((System.Windows.Controls.ComboBox)(target));
            
            #line 158 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.cmbUserName.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbUserName_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnLoadUser = ((System.Windows.Controls.Button)(target));
            
            #line 159 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.btnLoadUser.Click += new System.Windows.RoutedEventHandler(this.btnLoadUser_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.btnCalculate = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.btnCalculate.Click += new System.Windows.RoutedEventHandler(this.btnCalculate_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btnYearlyProfit = ((System.Windows.Controls.Button)(target));
            
            #line 163 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.btnYearlyProfit.Click += new System.Windows.RoutedEventHandler(this.btnYearlyProfit_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.btnStatistics = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.btnStatistics.Click += new System.Windows.RoutedEventHandler(this.btnStatistics_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.txtJanuary = ((System.Windows.Controls.TextBox)(target));
            
            #line 204 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtJanuary.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.txtFebruary = ((System.Windows.Controls.TextBox)(target));
            
            #line 208 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtFebruary.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.txtMarch = ((System.Windows.Controls.TextBox)(target));
            
            #line 212 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtMarch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.txtApril = ((System.Windows.Controls.TextBox)(target));
            
            #line 236 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtApril.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.txtMay = ((System.Windows.Controls.TextBox)(target));
            
            #line 240 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtMay.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.txtJune = ((System.Windows.Controls.TextBox)(target));
            
            #line 244 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtJune.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.txtJuly = ((System.Windows.Controls.TextBox)(target));
            
            #line 268 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtJuly.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.txtAugust = ((System.Windows.Controls.TextBox)(target));
            
            #line 272 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtAugust.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.txtSeptember = ((System.Windows.Controls.TextBox)(target));
            
            #line 276 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtSeptember.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.txtOctober = ((System.Windows.Controls.TextBox)(target));
            
            #line 300 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtOctober.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.txtNovember = ((System.Windows.Controls.TextBox)(target));
            
            #line 304 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtNovember.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.txtDecember = ((System.Windows.Controls.TextBox)(target));
            
            #line 308 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.txtDecember.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MonthValue_TextChanged);
            
            #line default
            #line hidden
            return;
            case 21:
            this.txtYearlyTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.txtYearToDateTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.btnSave = ((System.Windows.Controls.Button)(target));
            
            #line 336 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.btnSave.Click += new System.Windows.RoutedEventHandler(this.btnSave_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 338 "..\..\..\MonthlyProfitSettingsWindow.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.btnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

