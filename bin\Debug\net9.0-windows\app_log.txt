=== 日志开始于 2025/4/29 17:21:11 ===
[2025-04-29 17:21:11] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:21:11] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:21:11] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:21:11] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-29 17:21:11] [INFO] 发送HTTP请求...
[2025-04-29 17:21:11] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:21:11] [INFO] 开始解析JSON数据...
[2025-04-29 17:21:11] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-29 17:21:11] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:21:11] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:21:11] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:21:11] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-29 17:21:11] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-29 17:21:11] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:21:11] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-29 17:21:11] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:21:11] [INFO] 返回 5 条季度利润数据
[2025-04-29 17:21:11] [INFO] 获取到 5 条季度利润数据
[2025-04-29 17:21:11] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:21:11] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:21:11] [DEBUG] 季度利润数据: 报告期=2024-03-31, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:21:11] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:21:21] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-29 17:21:21] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-29 17:21:21] [INFO] 开始解析JSON数据...
[2025-04-29 17:21:21] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"99,709.12","expend":"114,486.83","profit":"-12,889.22","totalp":"-13,066.73","reprofit":"-13,296.00","basege":"-0.0471","ettege":"-0.0471","otherp":"--","totalcp":"-13,296.00"},{"date":"2024-12-31","income":"481,546.68","expend":"518,094.66","profit":"-34,218.23","totalp":"-34,464.41","reprofit":"-36,513.97","basege":"-0.1100","ettege":"-0.1100","otherp":"--","totalcp":"-36,513.97"},{"date":"2024-09-30","income":"360,625.69","expend":"389,272.16","profit":"-28,340.64","totalp":"-28,348.39","reprofit":"-29,427.28","basege":"-0.0954","ettege":"-0.0954","otherp":"--","totalcp":"-29,427.28"},{"date":"2024-06-30","income":"242,046.17","expend":"260,552.39","profit":"-22,106.50","totalp":"-22,080.78","reprofit":"-22,882.48","basege":"-0.0768","ettege":"-0.0768","otherp":"--","totalcp":"-22,882.48"},{"date":"2024-03-31","income":"117,292.32","expend":"128,677.84","profit":"-12,844.49","totalp":"-12,848.19","reprofit":"-13,187.71","basege":"-0.0464","ettege":"-0.0464","otherp":"--","totalcp":"-13,187.71"}]
[2025-04-29 17:21:21] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:21:21] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "99,709.12",
  "expend": "114,486.83",
  "profit": "-12,889.22",
  "totalp": "-13,066.73",
  "reprofit": "-13,296.00",
  "basege": "-0.0471",
  "ettege": "-0.0471",
  "otherp": "--",
  "totalcp": "-13,296.00"
}
[2025-04-29 17:21:21] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:21:21] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "481,546.68",
  "expend": "518,094.66",
  "profit": "-34,218.23",
  "totalp": "-34,464.41",
  "reprofit": "-36,513.97",
  "basege": "-0.1100",
  "ettege": "-0.1100",
  "otherp": "--",
  "totalcp": "-36,513.97"
}
[2025-04-29 17:21:21] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:21:21] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "117,292.32",
  "expend": "128,677.84",
  "profit": "-12,844.49",
  "totalp": "-12,848.19",
  "reprofit": "-13,187.71",
  "basege": "-0.0464",
  "ettege": "-0.0464",
  "otherp": "--",
  "totalcp": "-13,187.71"
}
[2025-04-29 17:21:21] [INFO] 获取到 5 条季度利润数据
[2025-04-29 17:21:21] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/4/29 17:26:23 ===
[2025-04-29 17:26:23] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:26:23] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:26:23] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:26:23] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-29 17:26:23] [INFO] 发送HTTP请求...
[2025-04-29 17:26:23] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:26:23] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-29 17:26:23] [INFO] 开始解析JSON数据...
[2025-04-29 17:26:23] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:26:23] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:26:23] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:26:23] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-29 17:26:23] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:26:23] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:26:23] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-29 17:26:23] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-29 17:26:23] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:26:23] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:26:23] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-29 17:26:23] [INFO] 返回 5 条季度利润数据
[2025-04-29 17:26:23] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
=== 日志开始于 2025/4/29 17:27:49 ===
[2025-04-29 17:27:49] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:27:49] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:27:49] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:27:49] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-29 17:27:49] [INFO] 发送HTTP请求...
[2025-04-29 17:27:49] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:27:49] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-29 17:27:49] [INFO] 开始解析JSON数据...
[2025-04-29 17:27:49] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:27:49] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:27:49] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:27:49] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-29 17:27:49] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:27:49] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:27:49] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:27:49] [INFO] 返回 5 条季度利润数据
[2025-04-29 17:27:49] [INFO] 获取到 5 条季度利润数据
[2025-04-29 17:27:49] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:27:49] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:27:49] [DEBUG] 季度利润数据: 报告期=2024-06-30, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:27:49] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-04-29 17:27:49] [INFO] DataGrid.HasItems = True
[2025-04-29 17:27:55] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-04-29 17:27:55] [INFO] 开始解析JSON数据...
[2025-04-29 17:27:55] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:27:55] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "99,709.12",
  "expend": "114,486.83",
  "profit": "-12,889.22",
  "totalp": "-13,066.73",
  "reprofit": "-13,296.00",
  "basege": "-0.0471",
  "ettege": "-0.0471",
  "otherp": "--",
  "totalcp": "-13,296.00"
}
[2025-04-29 17:27:55] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "242,046.17",
  "expend": "260,552.39",
  "profit": "-22,106.50",
  "totalp": "-22,080.78",
  "reprofit": "-22,882.48",
  "basege": "-0.0768",
  "ettege": "-0.0768",
  "otherp": "--",
  "totalcp": "-22,882.48"
}
[2025-04-29 17:27:55] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:27:55] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "117,292.32",
  "expend": "128,677.84",
  "profit": "-12,844.49",
  "totalp": "-12,848.19",
  "reprofit": "-13,187.71",
  "basege": "-0.0464",
  "ettege": "-0.0464",
  "otherp": "--",
  "totalcp": "-13,187.71"
}
[2025-04-29 17:27:55] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:27:55] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:27:55] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:27:55] [DEBUG] 季度利润数据: 报告期=2024-06-30, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
=== 日志开始于 2025/4/29 17:28:32 ===
[2025-04-29 17:28:32] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:28:32] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:28:32] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-29 17:28:32] [INFO] 发送HTTP请求...
[2025-04-29 17:28:32] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:28:32] [INFO] 开始解析JSON数据...
[2025-04-29 17:28:32] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-29 17:28:32] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:28:32] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:28:32] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:28:32] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:28:32] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-29 17:28:32] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-29 17:28:32] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:28:32] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:28:32] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-29 17:28:32] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-29 17:28:32] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: , 营收同比: , 净利润: , 净利润同比: 
[2025-04-29 17:28:32] [INFO] 返回 5 条季度利润数据
[2025-04-29 17:28:32] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:28:32] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=-, 营收同比=-, 净利润=-, 净利润同比=-
[2025-04-29 17:28:32] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-04-29 17:28:32] [INFO] DataGrid.Items.Count = 5
=== 日志开始于 2025/4/29 17:31:42 ===
[2025-04-29 17:31:42] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:31:42] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:31:42] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:31:42] [INFO] 发送HTTP请求...
[2025-04-29 17:31:42] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-29 17:31:42] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:31:42] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-29 17:31:42] [INFO] 开始解析JSON数据...
[2025-04-29 17:31:43] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:31:43] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:31:43] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 营收同比: -, 净利润: 47,819, 净利润同比: -
[2025-04-29 17:31:43] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-29 17:31:43] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 营收同比: -, 净利润: 156,453, 净利润同比: -
[2025-04-29 17:31:43] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-29 17:31:43] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 营收同比: -, 净利润: 115,680, 净利润同比: -
[2025-04-29 17:31:43] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-29 17:31:43] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 营收同比: -, 净利润: 84,990, 净利润同比: -
[2025-04-29 17:31:43] [INFO] 返回 5 条季度利润数据
[2025-04-29 17:31:43] [INFO] 获取到 5 条季度利润数据
[2025-04-29 17:31:43] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=379,418, 营收同比=-, 净利润=47,819, 净利润同比=-
[2025-04-29 17:31:43] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=1.52百万, 营收同比=-, 净利润=156,453, 净利润同比=-
[2025-04-29 17:31:43] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-04-29 17:31:43] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-04-29 17:31:43] [INFO] DataGrid.Items.Count = 5
[2025-04-29 17:31:43] [INFO] 季度利润窗口加载完成
[2025-04-29 17:31:43] [INFO] DataGrid.HasItems = True
[2025-04-29 17:32:05] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-04-29 17:32:05] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-29 17:32:05] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
[2025-04-29 17:32:05] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-29 17:32:06] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-29 17:32:06] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"99,709.12","expend":"114,486.83","profit":"-12,889.22","totalp":"-13,066.73","reprofit":"-13,296.00","basege":"-0.0471","ettege":"-0.0471","otherp":"--","totalcp":"-13,296.00"},{"date":"2024-12-31","income":"481,546.68","expend":"518,094.66","profit":"-34,218.23","totalp":"-34,464.41","reprofit":"-36,513.97","basege":"-0.1100","ettege":"-0.1100","otherp":"--","totalcp":"-36,513.97"},{"date":"2024-09-30","income":"360,625.69","expend":"389,272.16","profit":"-28,340.64","totalp":"-28,348.39","reprofit":"-29,427.28","basege":"-0.0954","ettege":"-0.0954","otherp":"--","totalcp":"-29,427.28"},{"date":"2024-06-30","income":"242,046.17","expend":"260,552.39","profit":"-22,106.50","totalp":"-22,080.78","reprofit":"-22,882.48","basege":"-0.0768","ettege":"-0.0768","otherp":"--","totalcp":"-22,882.48"},{"date":"2024-03-31","income":"117,292.32","expend":"128,677.84","profit":"-12,844.49","totalp":"-12,848.19","reprofit":"-13,187.71","basege":"-0.0464","ettege":"-0.0464","otherp":"--","totalcp":"-13,187.71"}]
[2025-04-29 17:32:06] [INFO] 开始解析JSON数据...
[2025-04-29 17:32:06] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:32:06] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "99,709.12",
  "expend": "114,486.83",
  "profit": "-12,889.22",
  "totalp": "-13,066.73",
  "reprofit": "-13,296.00",
  "basege": "-0.0471",
  "ettege": "-0.0471",
  "otherp": "--",
  "totalcp": "-13,296.00"
}
[2025-04-29 17:32:06] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 99,709, 营收同比: -, 净利润: -12,889, 净利润同比: -
[2025-04-29 17:32:06] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "481,546.68",
  "expend": "518,094.66",
  "profit": "-34,218.23",
  "totalp": "-34,464.41",
  "reprofit": "-36,513.97",
  "basege": "-0.1100",
  "ettege": "-0.1100",
  "otherp": "--",
  "totalcp": "-36,513.97"
}
[2025-04-29 17:32:06] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 481,547, 营收同比: -, 净利润: -34,218, 净利润同比: -
[2025-04-29 17:32:06] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "360,625.69",
  "expend": "389,272.16",
  "profit": "-28,340.64",
  "totalp": "-28,348.39",
  "reprofit": "-29,427.28",
  "basege": "-0.0954",
  "ettege": "-0.0954",
  "otherp": "--",
  "totalcp": "-29,427.28"
}
[2025-04-29 17:32:06] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 360,626, 营收同比: -, 净利润: -28,341, 净利润同比: -
[2025-04-29 17:32:06] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "117,292.32",
  "expend": "128,677.84",
  "profit": "-12,844.49",
  "totalp": "-12,848.19",
  "reprofit": "-13,187.71",
  "basege": "-0.0464",
  "ettege": "-0.0464",
  "otherp": "--",
  "totalcp": "-13,187.71"
}
[2025-04-29 17:32:06] [INFO] 获取到 5 条季度利润数据
=== 日志开始于 2025/4/29 17:40:16 ===
[2025-04-29 17:40:16] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:40:16] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:40:16] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:40:16] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-29 17:40:16] [INFO] 发送HTTP请求...
[2025-04-29 17:40:16] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:40:16] [INFO] 开始解析JSON数据...
[2025-04-29 17:40:16] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-29 17:40:16] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:40:16] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:40:16] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:40:16] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-29 17:40:16] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:40:16] [INFO] 返回 0 条季度利润数据
[2025-04-29 17:40:16] [INFO] 获取到 0 条季度利润数据
[2025-04-29 17:40:16] [ERROR] 未找到季度利润数据
[2025-04-29 17:40:16] [INFO] 创建测试数据...
[2025-04-29 17:40:16] [INFO] 开始绑定 4 条测试数据到UI
[2025-04-29 17:40:16] [INFO] 成功绑定 4 条测试数据到UI
[2025-04-29 17:40:16] [INFO] DataGrid.Items.Count = 4
[2025-04-29 17:40:16] [INFO] 季度利润窗口加载完成
[2025-04-29 17:40:16] [INFO] DataGrid.HasItems = True
[2025-04-29 17:41:39] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-04-29 17:41:39] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-29 17:41:39] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
[2025-04-29 17:41:39] [INFO] 发送HTTP请求...
[2025-04-29 17:41:40] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-29 17:41:40] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"99,709.12","expend":"114,486.83","profit":"-12,889.22","totalp":"-13,066.73","reprofit":"-13,296.00","basege":"-0.0471","ettege":"-0.0471","otherp":"--","totalcp":"-13,296.00"},{"date":"2024-12-31","income":"481,546.68","expend":"518,094.66","profit":"-34,218.23","totalp":"-34,464.41","reprofit":"-36,513.97","basege":"-0.1100","ettege":"-0.1100","otherp":"--","totalcp":"-36,513.97"},{"date":"2024-09-30","income":"360,625.69","expend":"389,272.16","profit":"-28,340.64","totalp":"-28,348.39","reprofit":"-29,427.28","basege":"-0.0954","ettege":"-0.0954","otherp":"--","totalcp":"-29,427.28"},{"date":"2024-06-30","income":"242,046.17","expend":"260,552.39","profit":"-22,106.50","totalp":"-22,080.78","reprofit":"-22,882.48","basege":"-0.0768","ettege":"-0.0768","otherp":"--","totalcp":"-22,882.48"},{"date":"2024-03-31","income":"117,292.32","expend":"128,677.84","profit":"-12,844.49","totalp":"-12,848.19","reprofit":"-13,187.71","basege":"-0.0464","ettege":"-0.0464","otherp":"--","totalcp":"-13,187.71"}]
[2025-04-29 17:41:40] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:41:40] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "99,709.12",
  "expend": "114,486.83",
  "profit": "-12,889.22",
  "totalp": "-13,066.73",
  "reprofit": "-13,296.00",
  "basege": "-0.0471",
  "ettege": "-0.0471",
  "otherp": "--",
  "totalcp": "-13,296.00"
}
[2025-04-29 17:41:40] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "481,546.68",
  "expend": "518,094.66",
  "profit": "-34,218.23",
  "totalp": "-34,464.41",
  "reprofit": "-36,513.97",
  "basege": "-0.1100",
  "ettege": "-0.1100",
  "otherp": "--",
  "totalcp": "-36,513.97"
}
[2025-04-29 17:41:40] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:41:40] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "360,625.69",
  "expend": "389,272.16",
  "profit": "-28,340.64",
  "totalp": "-28,348.39",
  "reprofit": "-29,427.28",
  "basege": "-0.0954",
  "ettege": "-0.0954",
  "otherp": "--",
  "totalcp": "-29,427.28"
}
[2025-04-29 17:41:40] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:41:40] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "242,046.17",
  "expend": "260,552.39",
  "profit": "-22,106.50",
  "totalp": "-22,080.78",
  "reprofit": "-22,882.48",
  "basege": "-0.0768",
  "ettege": "-0.0768",
  "otherp": "--",
  "totalcp": "-22,882.48"
}
[2025-04-29 17:41:40] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:41:40] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "117,292.32",
  "expend": "128,677.84",
  "profit": "-12,844.49",
  "totalp": "-12,848.19",
  "reprofit": "-13,187.71",
  "basege": "-0.0464",
  "ettege": "-0.0464",
  "otherp": "--",
  "totalcp": "-13,187.71"
}
[2025-04-29 17:41:40] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:41:40] [INFO] 创建测试数据...
[2025-04-29 17:41:43] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:41:43] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:41:43] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:41:43] [INFO] 发送HTTP请求...
[2025-04-29 17:41:43] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:41:43] [INFO] 开始解析JSON数据...
[2025-04-29 17:42:09] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:42:09] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:42:09] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:42:09] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-29 17:42:09] [INFO] 发送HTTP请求...
[2025-04-29 17:42:09] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:42:09] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-29 17:42:09] [INFO] 开始解析JSON数据...
[2025-04-29 17:42:09] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:42:09] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:42:09] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-29 17:42:09] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:42:09] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:42:09] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:42:09] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/4/29 17:44:33 ===
[2025-04-29 17:44:33] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:44:33] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:44:33] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:44:33] [INFO] 发送HTTP请求...
[2025-04-29 17:44:33] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-29 17:44:33] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:44:33] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-29 17:44:33] [INFO] 开始解析JSON数据...
[2025-04-29 17:44:33] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:44:33] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:44:33] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:44:33] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:44:33] [ERROR] 解析季度利润数据项时出错: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY' - 异常: 'StockCrawler.QuarterlyProfitData' does not contain a definition for 'RevenueYoY'
堆栈:    at CallSite.Target(Closure, CallSite, Object)
   at StockCrawler.Services.MairuiService.GetQuarterlyProfitAsync(String stockCode) in D:\source\gupiao\SimpleStock\StockCrawler\Services\MairuiService.cs:line 95
[2025-04-29 17:44:33] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-29 17:44:33] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-29 17:44:33] [INFO] 获取到 0 条季度利润数据
[2025-04-29 17:44:33] [INFO] 创建测试数据...
[2025-04-29 17:44:33] [ERROR] 未找到季度利润数据
[2025-04-29 17:44:33] [INFO] 开始绑定 4 条测试数据到UI
[2025-04-29 17:44:33] [INFO] 成功绑定 4 条测试数据到UI
[2025-04-29 17:44:33] [INFO] DataGrid.Items.Count = 4
[2025-04-29 17:44:33] [INFO] DataGrid.HasItems = True
[2025-04-29 17:44:33] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/4/29 17:48:23 ===
[2025-04-29 17:48:23] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:48:23] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:48:23] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:48:23] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-29 17:48:23] [INFO] 发送HTTP请求...
[2025-04-29 17:48:23] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:48:23] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-29 17:48:23] [INFO] 开始解析JSON数据...
[2025-04-29 17:48:23] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:48:23] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:48:23] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-29 17:48:23] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-29 17:48:23] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-04-29 17:48:23] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-29 17:48:23] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-29 17:48:23] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-29 17:48:23] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-29 17:48:23] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-29 17:48:23] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-04-29 17:48:23] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-29 17:48:23] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-29 17:48:23] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-29 17:48:23] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-29 17:48:23] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-04-29 17:48:23] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-29 17:48:23] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-29 17:48:23] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=1.09百万, 稀释每股收益=1.1200, 净利润=115,680, 综合收益总额=116,444
[2025-04-29 17:48:23] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-04-29 17:51:11] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-29 17:51:11] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-29 17:51:11] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-29 17:51:11] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:51:11] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-29 17:51:11] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-29 17:51:11] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "99,709.12",
  "expend": "114,486.83",
  "profit": "-12,889.22",
  "totalp": "-13,066.73",
  "reprofit": "-13,296.00",
  "basege": "-0.0471",
  "ettege": "-0.0471",
  "otherp": "--",
  "totalcp": "-13,296.00"
}
[2025-04-29 17:51:11] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 99,709, 稀释每股收益: -0.0471, 净利润: -12,889, 综合收益总额: -13,067
[2025-04-29 17:51:11] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "481,546.68",
  "expend": "518,094.66",
  "profit": "-34,218.23",
  "totalp": "-34,464.41",
  "reprofit": "-36,513.97",
  "basege": "-0.1100",
  "ettege": "-0.1100",
  "otherp": "--",
  "totalcp": "-36,513.97"
}
[2025-04-29 17:51:11] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-29 17:51:11] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-29 17:51:11] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-29 17:51:11] [INFO] 返回 5 条季度利润数据
[2025-04-29 17:51:11] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=481,547, 稀释每股收益=-0.1100, 净利润=-34,218, 综合收益总额=-34,464
[2025-04-29 17:51:11] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-04-29 17:52:42] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-29 17:52:42] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:52:42] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-29 17:52:42] [INFO] 发送HTTP请求...
[2025-04-29 17:52:42] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-29 17:52:42] [INFO] 开始解析JSON数据...
[2025-04-29 17:52:42] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-29 17:52:42] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-29 17:52:42] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-29 17:52:42] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-29 17:52:42] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-04-29 17:52:42] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-29 17:52:42] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-29 17:52:42] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-04-29 17:52:42] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-29 17:52:42] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-29 17:52:42] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-04-29 17:52:42] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-04-29 17:53:00] [INFO] 开始获取股票 000021 的季度利润数据
[2025-04-29 17:53:00] [INFO] 开始获取股票 000021 的季度利润数据
[2025-04-29 17:53:00] [INFO] HTTP请求成功，响应长度: 1061
[2025-04-29 17:53:00] [INFO] 开始解析JSON数据...
[2025-04-29 17:53:00] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:53:00] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,482,716.65",
  "expend": "1,347,906.19",
  "profit": "123,715.42",
  "totalp": "128,492.56",
  "reprofit": "108,750.44",
  "basege": "0.5962",
  "ettege": "0.5962",
  "otherp": "5,515.26",
  "totalcp": "114,265.70"
}
[2025-04-29 17:53:00] [DEBUG] 解析字段 - 报告期: 2023-12-31, 营收: 1.43百万, 稀释每股收益: 0.4131, 净利润: 96,767, 综合收益总额: 99,113
[2025-04-29 17:53:00] [INFO] 返回 5 条季度利润数据
[2025-04-29 17:53:00] [DEBUG] 季度利润数据: 报告期=2024-03-31, 营收=312,638, 稀释每股收益=0.0780, 净利润=21,812, 综合收益总额=21,828
[2025-04-29 17:53:00] [INFO] 季度利润窗口加载完成
[2025-04-29 17:55:11] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-04-29 17:55:11] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-29 17:55:11] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
[2025-04-29 17:55:11] [INFO] 发送HTTP请求...
[2025-04-29 17:55:11] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-29 17:55:11] [INFO] 开始解析JSON数据...
[2025-04-29 17:55:11] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"99,709.12","expend":"114,486.83","profit":"-12,889.22","totalp":"-13,066.73","reprofit":"-13,296.00","basege":"-0.0471","ettege":"-0.0471","otherp":"--","totalcp":"-13,296.00"},{"date":"2024-12-31","income":"481,546.68","expend":"518,094.66","profit":"-34,218.23","totalp":"-34,464.41","reprofit":"-36,513.97","basege":"-0.1100","ettege":"-0.1100","otherp":"--","totalcp":"-36,513.97"},{"date":"2024-09-30","income":"360,625.69","expend":"389,272.16","profit":"-28,340.64","totalp":"-28,348.39","reprofit":"-29,427.28","basege":"-0.0954","ettege":"-0.0954","otherp":"--","totalcp":"-29,427.28"},{"date":"2024-06-30","income":"242,046.17","expend":"260,552.39","profit":"-22,106.50","totalp":"-22,080.78","reprofit":"-22,882.48","basege":"-0.0768","ettege":"-0.0768","otherp":"--","totalcp":"-22,882.48"},{"date":"2024-03-31","income":"117,292.32","expend":"128,677.84","profit":"-12,844.49","totalp":"-12,848.19","reprofit":"-13,187.71","basege":"-0.0464","ettege":"-0.0464","otherp":"--","totalcp":"-13,187.71"}]
[2025-04-29 17:55:11] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:55:11] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-29 17:55:11] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "99,709.12",
  "expend": "114,486.83",
  "profit": "-12,889.22",
  "totalp": "-13,066.73",
  "reprofit": "-13,296.00",
  "basege": "-0.0471",
  "ettege": "-0.0471",
  "otherp": "--",
  "totalcp": "-13,296.00"
}
[2025-04-29 17:55:11] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-29 17:55:11] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 99,709, 稀释每股收益: -0.0471, 净利润: -12,889, 综合收益总额: -13,067
[2025-04-29 17:55:11] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "481,546.68",
  "expend": "518,094.66",
  "profit": "-34,218.23",
  "totalp": "-34,464.41",
  "reprofit": "-36,513.97",
  "basege": "-0.1100",
  "ettege": "-0.1100",
  "otherp": "--",
  "totalcp": "-36,513.97"
}
[2025-04-29 17:55:11] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-29 17:55:11] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 481,547, 稀释每股收益: -0.1100, 净利润: -34,218, 综合收益总额: -34,464
[2025-04-29 17:55:11] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "360,625.69",
  "expend": "389,272.16",
  "profit": "-28,340.64",
  "totalp": "-28,348.39",
  "reprofit": "-29,427.28",
  "basege": "-0.0954",
  "ettege": "-0.0954",
  "otherp": "--",
  "totalcp": "-29,427.28"
}
[2025-04-29 17:55:11] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-29 17:55:11] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 360,626, 稀释每股收益: -0.0954, 净利润: -28,341, 综合收益总额: -28,348
[2025-04-29 17:55:11] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-29 17:55:11] [INFO] 获取到 5 条季度利润数据
[2025-04-29 17:55:11] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-04-29 17:55:11] [INFO] DataGrid.HasItems = True
[2025-04-29 17:55:43] [INFO] 开始获取股票 000525 的季度利润数据
[2025-04-29 17:55:43] [INFO] 季度利润窗口加载 - 股票代码: 000525, 股票名称: ST红太阳
[2025-04-29 17:55:43] [INFO] 开始获取股票 000525 的季度利润数据
[2025-04-29 17:55:43] [INFO] 发送HTTP请求...
[2025-04-29 17:55:43] [INFO] HTTP请求成功，响应长度: 1006
[2025-04-29 17:55:43] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000525/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"84,773.06","expend":"85,101.02","profit":"2,878.50","totalp":"2,822.16","reprofit":"2,388.26","basege":"0.0173","ettege":"0.0173","otherp":"--","totalcp":"2,388.26"},{"date":"2024-12-31","income":"300,703.28","expend":"414,977.42","profit":"164,547.66","totalp":"13,923.86","reprofit":"35,866.69","basege":"0.3000","ettege":"0.3000","otherp":"-33.78","totalcp":"35,832.92"},{"date":"2024-09-30","income":"270,253.81","expend":"272,103.17","profit":"3,000.74","totalp":"2,230.23","reprofit":"2,116.67","basege":"0.0461","ettege":"0.0461","otherp":"--","totalcp":"2,116.67"},{"date":"2024-06-30","income":"164,238.64","expend":"164,600.42","profit":"2,722.97","totalp":"1,407.17","reprofit":"1,990.99","basege":"0.0366","ettege":"0.0366","otherp":"-347.54","totalcp":"1,643.44"},{"date":"2024-03-31","income":"84,569.59","expend":"84,633.52","profit":"3,064.12","totalp":"2,807.14","reprofit":"1,903.50","basege":"0.0346","ettege":"0.0346","otherp":"--","totalcp":"1,903.50"}]
[2025-04-29 17:55:43] [INFO] 开始解析JSON数据...
[2025-04-29 17:55:43] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:55:43] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-29 17:55:43] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "84,773.06",
  "expend": "85,101.02",
  "profit": "2,878.50",
  "totalp": "2,822.16",
  "reprofit": "2,388.26",
  "basege": "0.0173",
  "ettege": "0.0173",
  "otherp": "--",
  "totalcp": "2,388.26"
}
[2025-04-29 17:55:43] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-29 17:55:43] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 84,773, 稀释每股收益: 0.0173, 净利润: 2,879, 综合收益总额: 2,822
[2025-04-29 17:55:43] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "300,703.28",
  "expend": "414,977.42",
  "profit": "164,547.66",
  "totalp": "13,923.86",
  "reprofit": "35,866.69",
  "basege": "0.3000",
  "ettege": "0.3000",
  "otherp": "-33.78",
  "totalcp": "35,832.92"
}
[2025-04-29 17:55:43] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-29 17:55:43] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 300,703, 稀释每股收益: 0.3000, 净利润: 164,548, 综合收益总额: 13,924
[2025-04-29 17:55:43] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-29 17:55:43] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-29 17:55:43] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-29 17:55:43] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=84,773, 稀释每股收益=0.0173, 净利润=2,879, 综合收益总额=2,822
[2025-04-29 17:55:43] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=270,254, 稀释每股收益=0.0461, 净利润=3,001, 综合收益总额=2,230
[2025-04-29 17:55:43] [INFO] DataGrid.HasItems = True
[2025-04-29 17:56:03] [INFO] 季度利润窗口加载 - 股票代码: 000564, 股票名称: 供销大集
[2025-04-29 17:56:03] [INFO] 开始获取股票 000564 的季度利润数据
[2025-04-29 17:56:03] [INFO] 发送HTTP请求...
[2025-04-29 17:56:03] [INFO] 开始获取股票 000564 的季度利润数据
[2025-04-29 17:56:03] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000564/72430658081e51fc98
[2025-04-29 17:56:03] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000564/72430658081e51fc98
响应: [{"date":"2024-09-30","income":"113,088.34","expend":"155,911.80","profit":"-15,945.49","totalp":"-16,296.96","reprofit":"-16,171.39","basege":"-0.0058","ettege":"-0.0058","otherp":"196.91","totalcp":"-15,974.49"},{"date":"2024-06-30","income":"73,458.60","expend":"103,028.01","profit":"-6,926.51","totalp":"-8,184.53","reprofit":"-8,964.40","basege":"-0.0023","ettege":"-0.0023","otherp":"1,078.80","totalcp":"-7,885.60"},{"date":"2024-03-31","income":"39,023.72","expend":"52,559.58","profit":"-13,980.29","totalp":"-14,617.38","reprofit":"-13,632.95","basege":"-0.0068","ettege":"-0.0068","otherp":"979.32","totalcp":"-12,653.63"},{"date":"2023-12-31","income":"140,000.95","expend":"204,404.71","profit":"-268,751.96","totalp":"-276,753.36","reprofit":"-255,164.31","basege":"-0.1315","ettege":"-0.1315","otherp":"62,310.32","totalcp":"-192,853.99"},{"date":"2023-09-30","income":"104,044.84","expend":"148,253.90","profit":"-42,201.66","totalp":"-42,818.34","reprofit":"-39,787.44","basege":"-0.0198","ettege":"-0.0198","otherp":"-791.50","totalcp":"-40,578.94"}]
[2025-04-29 17:56:03] [INFO] HTTP请求成功，响应长度: 1069
[2025-04-29 17:56:03] [INFO] 开始解析JSON数据...
[2025-04-29 17:56:03] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-29 17:56:03] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "113,088.34",
  "expend": "155,911.80",
  "profit": "-15,945.49",
  "totalp": "-16,296.96",
  "reprofit": "-16,171.39",
  "basege": "-0.0058",
  "ettege": "-0.0058",
  "otherp": "196.91",
  "totalcp": "-15,974.49"
}
[2025-04-29 17:56:03] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-29 17:56:03] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-29 17:56:03] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 113,088, 稀释每股收益: -0.0058, 净利润: -15,945, 综合收益总额: -16,297
[2025-04-29 17:56:03] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-29 17:56:03] [DEBUG] API返回的日期字段: 2023-12-31, 类型: JValue
[2025-04-29 17:56:03] [INFO] 季度利润窗口加载完成
[2025-04-29 17:56:03] [INFO] DataGrid.HasItems = True
=== 日志开始于 2025/4/30 9:01:27 ===
[2025-04-30 09:01:27] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-30 09:01:27] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 09:01:27] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 09:01:27] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-30 09:01:27] [INFO] 发送HTTP请求...
[2025-04-30 09:01:27] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-30 09:01:27] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-30 09:01:27] [INFO] 开始解析JSON数据...
[2025-04-30 09:01:27] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 09:01:27] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-30 09:01:27] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 09:01:27] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 09:01:27] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-04-30 09:01:27] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-30 09:01:27] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 09:01:27] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-04-30 09:01:27] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-30 09:01:27] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 09:01:27] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 09:01:27] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-04-30 09:01:27] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 09:01:27] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 09:01:27] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-30 09:01:27] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-04-30 09:01:27] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 09:01:27] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 09:01:27] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: 348,253, 稀释每股收益: 0.3700, 净利润: 38,723, 综合收益总额: 38,841
[2025-04-30 09:01:27] [INFO] 获取到 5 条季度利润数据
[2025-04-30 09:01:27] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=379,418, 稀释每股收益=0.4300, 净利润=47,819, 综合收益总额=47,950
[2025-04-30 09:01:27] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=1.09百万, 稀释每股收益=1.1200, 净利润=115,680, 综合收益总额=116,444
[2025-04-30 09:01:27] [DEBUG] 季度利润数据: 报告期=2024-03-31, 营收=348,253, 稀释每股收益=0.3700, 净利润=38,723, 综合收益总额=38,841
[2025-04-30 09:01:27] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-04-30 09:01:27] [INFO] 季度利润窗口加载完成
[2025-04-30 09:02:00] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-04-30 09:02:00] [INFO] 发送HTTP请求...
[2025-04-30 09:02:00] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
[2025-04-30 09:02:00] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-30 09:02:00] [INFO] 开始解析JSON数据...
[2025-04-30 09:02:00] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "99,709.12",
  "expend": "114,486.83",
  "profit": "-12,889.22",
  "totalp": "-13,066.73",
  "reprofit": "-13,296.00",
  "basege": "-0.0471",
  "ettege": "-0.0471",
  "otherp": "--",
  "totalcp": "-13,296.00"
}
[2025-04-30 09:02:00] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 09:02:00] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 09:02:00] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 09:02:00] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 99,709, 稀释每股收益: -0.0471, 净利润: -12,889, 综合收益总额: -13,067
[2025-04-30 09:02:00] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 09:02:00] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 09:02:00] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "360,625.69",
  "expend": "389,272.16",
  "profit": "-28,340.64",
  "totalp": "-28,348.39",
  "reprofit": "-29,427.28",
  "basege": "-0.0954",
  "ettege": "-0.0954",
  "otherp": "--",
  "totalcp": "-29,427.28"
}
[2025-04-30 09:02:00] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 09:02:00] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 09:02:00] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 360,626, 稀释每股收益: -0.0954, 净利润: -28,341, 综合收益总额: -28,348
[2025-04-30 09:02:00] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 09:02:00] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "242,046.17",
  "expend": "260,552.39",
  "profit": "-22,106.50",
  "totalp": "-22,080.78",
  "reprofit": "-22,882.48",
  "basege": "-0.0768",
  "ettege": "-0.0768",
  "otherp": "--",
  "totalcp": "-22,882.48"
}
[2025-04-30 09:02:00] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 242,046, 稀释每股收益: -0.0768, 净利润: -22,107, 综合收益总额: -22,081
[2025-04-30 09:02:00] [INFO] DataGrid.HasItems = True
[2025-04-30 09:02:00] [INFO] 季度利润窗口加载完成
[2025-04-30 09:02:08] [INFO] 季度利润窗口加载 - 股票代码: 000525, 股票名称: ST红太阳
[2025-04-30 09:02:08] [INFO] 开始获取股票 000525 的季度利润数据
[2025-04-30 09:02:08] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000525/72430658081e51fc98
[2025-04-30 09:02:08] [INFO] HTTP请求成功，响应长度: 1006
[2025-04-30 09:02:08] [INFO] 开始解析JSON数据...
[2025-04-30 09:02:08] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 09:02:08] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "84,773.06",
  "expend": "85,101.02",
  "profit": "2,878.50",
  "totalp": "2,822.16",
  "reprofit": "2,388.26",
  "basege": "0.0173",
  "ettege": "0.0173",
  "otherp": "--",
  "totalcp": "2,388.26"
}
[2025-04-30 09:02:08] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 09:02:08] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 09:02:08] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "300,703.28",
  "expend": "414,977.42",
  "profit": "164,547.66",
  "totalp": "13,923.86",
  "reprofit": "35,866.69",
  "basege": "0.3000",
  "ettege": "0.3000",
  "otherp": "-33.78",
  "totalcp": "35,832.92"
}
[2025-04-30 09:02:08] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 09:02:08] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "84,569.59",
  "expend": "84,633.52",
  "profit": "3,064.12",
  "totalp": "2,807.14",
  "reprofit": "1,903.50",
  "basege": "0.0346",
  "ettege": "0.0346",
  "otherp": "--",
  "totalcp": "1,903.50"
}
[2025-04-30 09:02:08] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=300,703, 稀释每股收益=0.3000, 净利润=164,548, 综合收益总额=13,924
[2025-04-30 09:02:08] [INFO] 季度利润窗口加载完成
[2025-04-30 09:02:16] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-04-30 09:02:16] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-30 09:02:16] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
[2025-04-30 09:02:16] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-30 09:02:16] [INFO] 开始解析JSON数据...
[2025-04-30 09:02:16] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"99,709.12","expend":"114,486.83","profit":"-12,889.22","totalp":"-13,066.73","reprofit":"-13,296.00","basege":"-0.0471","ettege":"-0.0471","otherp":"--","totalcp":"-13,296.00"},{"date":"2024-12-31","income":"481,546.68","expend":"518,094.66","profit":"-34,218.23","totalp":"-34,464.41","reprofit":"-36,513.97","basege":"-0.1100","ettege":"-0.1100","otherp":"--","totalcp":"-36,513.97"},{"date":"2024-09-30","income":"360,625.69","expend":"389,272.16","profit":"-28,340.64","totalp":"-28,348.39","reprofit":"-29,427.28","basege":"-0.0954","ettege":"-0.0954","otherp":"--","totalcp":"-29,427.28"},{"date":"2024-06-30","income":"242,046.17","expend":"260,552.39","profit":"-22,106.50","totalp":"-22,080.78","reprofit":"-22,882.48","basege":"-0.0768","ettege":"-0.0768","otherp":"--","totalcp":"-22,882.48"},{"date":"2024-03-31","income":"117,292.32","expend":"128,677.84","profit":"-12,844.49","totalp":"-12,848.19","reprofit":"-13,187.71","basege":"-0.0464","ettege":"-0.0464","otherp":"--","totalcp":"-13,187.71"}]
[2025-04-30 09:02:16] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 09:02:16] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 09:02:16] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 09:02:16] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 09:02:16] [INFO] DataGrid.Items.Count = 5
=== 日志开始于 2025/4/30 9:51:05 ===
[2025-04-30 09:51:05] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-30 09:51:05] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 09:51:05] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 09:51:05] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-30 09:51:05] [INFO] 发送HTTP请求...
[2025-04-30 09:51:05] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-30 09:51:05] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-30 09:51:05] [INFO] 开始解析JSON数据...
[2025-04-30 09:51:05] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 09:51:05] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-30 09:51:05] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 09:51:05] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 09:51:05] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-04-30 09:51:05] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-30 09:51:05] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 09:51:05] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-04-30 09:51:05] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-30 09:51:05] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 09:51:05] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 09:51:05] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-30 09:51:05] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 09:51:05] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-04-30 09:51:05] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 09:51:05] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-30 09:51:05] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 09:51:05] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 09:51:05] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: 348,253, 稀释每股收益: 0.3700, 净利润: 38,723, 综合收益总额: 38,841
[2025-04-30 09:51:05] [INFO] 返回 5 条季度利润数据
[2025-04-30 09:51:05] [INFO] 获取到 5 条季度利润数据
[2025-04-30 09:51:05] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=1.52百万, 稀释每股收益=1.5400, 净利润=156,453, 综合收益总额=158,857
[2025-04-30 09:51:05] [DEBUG] 季度利润数据: 报告期=2024-03-31, 营收=348,253, 稀释每股收益=0.3700, 净利润=38,723, 综合收益总额=38,841
[2025-04-30 11:01:13] [INFO] 开始获取股票 000021 的季度利润数据
[2025-04-30 11:01:13] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
[2025-04-30 11:01:13] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"336,463.82","expend":"302,999.03","profit":"28,045.86","totalp":"28,092.01","reprofit":"22,790.40","basege":"0.1147","ettege":"0.1147","otherp":"-2,553.17","totalcp":"20,237.23"},{"date":"2024-12-31","income":"1,482,716.65","expend":"1,347,906.19","profit":"123,715.42","totalp":"128,492.56","reprofit":"108,750.44","basege":"0.5962","ettege":"0.5962","otherp":"5,515.26","totalcp":"114,265.70"},{"date":"2024-09-30","income":"1,085,168.56","expend":"991,505.11","profit":"95,267.92","totalp":"96,803.44","reprofit":"81,211.98","basege":"0.4238","ettege":"0.4238","otherp":"3,715.15","totalcp":"84,927.13"},{"date":"2024-06-30","income":"705,456.41","expend":"652,810.88","profit":"56,153.66","totalp":"57,222.06","reprofit":"46,845.05","basege":"0.2309","ettege":"0.2309","otherp":"-14,796.33","totalcp":"32,048.72"},{"date":"2024-03-31","income":"312,637.90","expend":"293,190.19","profit":"21,811.85","totalp":"21,828.44","reprofit":"17,355.01","basege":"0.0780","ettege":"0.0780","otherp":"-11,244.20","totalcp":"6,110.81"}]
[2025-04-30 11:01:13] [INFO] HTTP请求成功，响应长度: 1060
[2025-04-30 11:01:13] [INFO] 开始解析JSON数据...
[2025-04-30 11:01:13] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 11:01:13] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "336,463.82",
  "expend": "302,999.03",
  "profit": "28,045.86",
  "totalp": "28,092.01",
  "reprofit": "22,790.40",
  "basege": "0.1147",
  "ettege": "0.1147",
  "otherp": "-2,553.17",
  "totalcp": "20,237.23"
}
[2025-04-30 11:01:13] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 11:01:13] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 11:01:13] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 336,464, 稀释每股收益: 0.1147, 净利润: 28,046, 综合收益总额: 28,092
[2025-04-30 11:01:13] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,482,716.65",
  "expend": "1,347,906.19",
  "profit": "123,715.42",
  "totalp": "128,492.56",
  "reprofit": "108,750.44",
  "basege": "0.5962",
  "ettege": "0.5962",
  "otherp": "5,515.26",
  "totalcp": "114,265.70"
}
[2025-04-30 11:01:13] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 11:01:13] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 11:01:13] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.48百万, 稀释每股收益: 0.5962, 净利润: 123,715, 综合收益总额: 128,493
[2025-04-30 11:01:13] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,085,168.56",
  "expend": "991,505.11",
  "profit": "95,267.92",
  "totalp": "96,803.44",
  "reprofit": "81,211.98",
  "basege": "0.4238",
  "ettege": "0.4238",
  "otherp": "3,715.15",
  "totalcp": "84,927.13"
}
[2025-04-30 11:01:13] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 11:01:13] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "705,456.41",
  "expend": "652,810.88",
  "profit": "56,153.66",
  "totalp": "57,222.06",
  "reprofit": "46,845.05",
  "basege": "0.2309",
  "ettege": "0.2309",
  "otherp": "-14,796.33",
  "totalcp": "32,048.72"
}
[2025-04-30 11:01:13] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 11:01:13] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 11:01:13] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: 312,638, 稀释每股收益: 0.0780, 净利润: 21,812, 综合收益总额: 21,828
[2025-04-30 11:01:13] [INFO] 返回 5 条季度利润数据
[2025-04-30 11:01:13] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=1.48百万, 稀释每股收益=0.5962, 净利润=123,715, 综合收益总额=128,493
[2025-04-30 11:01:13] [DEBUG] 季度利润数据: 报告期=2024-06-30, 营收=705,456, 稀释每股收益=0.2309, 净利润=56,154, 综合收益总额=57,222
[2025-04-30 11:01:13] [INFO] DataGrid.HasItems = True
[2025-04-30 11:04:57] [INFO] 季度利润窗口加载 - 股票代码: 000021, 股票名称: 深科技
[2025-04-30 11:04:57] [INFO] 开始获取股票 000021 的季度利润数据
[2025-04-30 11:04:57] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
[2025-04-30 11:04:57] [INFO] 开始获取股票 000021 的季度利润数据
[2025-04-30 11:04:57] [INFO] 发送HTTP请求...
[2025-04-30 11:04:57] [INFO] HTTP请求成功，响应长度: 1060
[2025-04-30 11:04:57] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"336,463.82","expend":"302,999.03","profit":"28,045.86","totalp":"28,092.01","reprofit":"22,790.40","basege":"0.1147","ettege":"0.1147","otherp":"-2,553.17","totalcp":"20,237.23"},{"date":"2024-12-31","income":"1,482,716.65","expend":"1,347,906.19","profit":"123,715.42","totalp":"128,492.56","reprofit":"108,750.44","basege":"0.5962","ettege":"0.5962","otherp":"5,515.26","totalcp":"114,265.70"},{"date":"2024-09-30","income":"1,085,168.56","expend":"991,505.11","profit":"95,267.92","totalp":"96,803.44","reprofit":"81,211.98","basege":"0.4238","ettege":"0.4238","otherp":"3,715.15","totalcp":"84,927.13"},{"date":"2024-06-30","income":"705,456.41","expend":"652,810.88","profit":"56,153.66","totalp":"57,222.06","reprofit":"46,845.05","basege":"0.2309","ettege":"0.2309","otherp":"-14,796.33","totalcp":"32,048.72"},{"date":"2024-03-31","income":"312,637.90","expend":"293,190.19","profit":"21,811.85","totalp":"21,828.44","reprofit":"17,355.01","basege":"0.0780","ettege":"0.0780","otherp":"-11,244.20","totalcp":"6,110.81"}]
[2025-04-30 11:04:57] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 11:04:57] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 11:04:57] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,482,716.65",
  "expend": "1,347,906.19",
  "profit": "123,715.42",
  "totalp": "128,492.56",
  "reprofit": "108,750.44",
  "basege": "0.5962",
  "ettege": "0.5962",
  "otherp": "5,515.26",
  "totalcp": "114,265.70"
}
[2025-04-30 11:04:57] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 11:04:57] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.48百万, 稀释每股收益: 0.5962, 净利润: 123,715, 综合收益总额: 128,493
[2025-04-30 11:04:57] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,085,168.56",
  "expend": "991,505.11",
  "profit": "95,267.92",
  "totalp": "96,803.44",
  "reprofit": "81,211.98",
  "basege": "0.4238",
  "ettege": "0.4238",
  "otherp": "3,715.15",
  "totalcp": "84,927.13"
}
[2025-04-30 11:04:57] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 11:04:57] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 11:04:57] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 0.4238, 净利润: 95,268, 综合收益总额: 96,803
[2025-04-30 11:04:57] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 11:04:57] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "705,456.41",
  "expend": "652,810.88",
  "profit": "56,153.66",
  "totalp": "57,222.06",
  "reprofit": "46,845.05",
  "basege": "0.2309",
  "ettege": "0.2309",
  "otherp": "-14,796.33",
  "totalcp": "32,048.72"
}
[2025-04-30 11:04:57] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 705,456, 稀释每股收益: 0.2309, 净利润: 56,154, 综合收益总额: 57,222
[2025-04-30 11:04:57] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "312,637.90",
  "expend": "293,190.19",
  "profit": "21,811.85",
  "totalp": "21,828.44",
  "reprofit": "17,355.01",
  "basege": "0.0780",
  "ettege": "0.0780",
  "otherp": "-11,244.20",
  "totalcp": "6,110.81"
}
[2025-04-30 11:04:57] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 11:04:57] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=1.48百万, 稀释每股收益=0.5962, 净利润=123,715, 综合收益总额=128,493
[2025-04-30 11:04:57] [DEBUG] 季度利润数据: 报告期=2024-03-31, 营收=312,638, 稀释每股收益=0.0780, 净利润=21,812, 综合收益总额=21,828
[2025-04-30 11:09:14] [INFO] 季度利润窗口加载 - 股票代码: 000564, 股票名称: 供销大集
[2025-04-30 11:09:14] [INFO] 开始获取股票 000564 的季度利润数据
[2025-04-30 11:09:14] [INFO] 开始获取股票 000564 的季度利润数据
[2025-04-30 11:09:14] [INFO] 发送HTTP请求...
[2025-04-30 11:09:15] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 11:09:15] [INFO] HTTP请求成功，响应长度: 1059
[2025-04-30 11:09:15] [INFO] 开始解析JSON数据...
[2025-04-30 11:09:15] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "36,505.65",
  "expend": "42,768.22",
  "profit": "-527.89",
  "totalp": "-591.41",
  "reprofit": "-936.94",
  "basege": "-0.0003",
  "ettege": "-0.0003",
  "otherp": "-1,511.09",
  "totalcp": "-2,448.03"
}
[2025-04-30 11:09:15] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 11:09:15] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 11:09:15] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 11:09:15] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 11:09:15] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 73,459, 稀释每股收益: -0.0023, 净利润: -6,927, 综合收益总额: -8,185
[2025-04-30 11:09:15] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 11:09:15] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-04-30 11:09:39] [INFO] 季度利润窗口加载 - 股票代码: 000525, 股票名称: ST红太阳
[2025-04-30 11:09:39] [INFO] 开始获取股票 000525 的季度利润数据
[2025-04-30 11:09:39] [INFO] 发送HTTP请求...
[2025-04-30 11:09:40] [INFO] HTTP请求成功，响应长度: 1006
[2025-04-30 11:09:40] [INFO] 开始解析JSON数据...
[2025-04-30 11:09:40] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 11:09:40] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "84,773.06",
  "expend": "85,101.02",
  "profit": "2,878.50",
  "totalp": "2,822.16",
  "reprofit": "2,388.26",
  "basege": "0.0173",
  "ettege": "0.0173",
  "otherp": "--",
  "totalcp": "2,388.26"
}
[2025-04-30 11:09:40] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 11:09:40] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 11:09:40] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 84,773, 稀释每股收益: 0.0173, 净利润: 2,879, 综合收益总额: 2,822
[2025-04-30 11:09:40] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 11:09:40] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "270,253.81",
  "expend": "272,103.17",
  "profit": "3,000.74",
  "totalp": "2,230.23",
  "reprofit": "2,116.67",
  "basege": "0.0461",
  "ettege": "0.0461",
  "otherp": "--",
  "totalcp": "2,116.67"
}
[2025-04-30 11:09:40] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 300,703, 稀释每股收益: 0.3000, 净利润: 164,548, 综合收益总额: 13,924
[2025-04-30 11:09:40] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 270,254, 稀释每股收益: 0.0461, 净利润: 3,001, 综合收益总额: 2,230
[2025-04-30 11:09:40] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "164,238.64",
  "expend": "164,600.42",
  "profit": "2,722.97",
  "totalp": "1,407.17",
  "reprofit": "1,990.99",
  "basege": "0.0366",
  "ettege": "0.0366",
  "otherp": "-347.54",
  "totalcp": "1,643.44"
}
[2025-04-30 11:09:40] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 11:09:40] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=270,254, 稀释每股收益=0.0461, 净利润=3,001, 综合收益总额=2,230
[2025-04-30 11:09:40] [INFO] 季度利润窗口加载完成
[2025-04-30 11:15:05] [INFO] 季度利润窗口加载 - 股票代码: 000564, 股票名称: 供销大集
[2025-04-30 11:15:05] [INFO] 开始获取股票 000564 的季度利润数据
[2025-04-30 11:15:05] [INFO] 开始获取股票 000564 的季度利润数据
[2025-04-30 11:15:05] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000564/72430658081e51fc98
[2025-04-30 11:15:05] [INFO] HTTP请求成功，响应长度: 1059
[2025-04-30 11:15:05] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000564/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"36,505.65","expend":"42,768.22","profit":"-527.89","totalp":"-591.41","reprofit":"-936.94","basege":"-0.0003","ettege":"-0.0003","otherp":"-1,511.09","totalcp":"-2,448.03"},{"date":"2024-12-31","income":"142,372.03","expend":"210,586.39","profit":"-190,394.27","totalp":"-171,751.04","reprofit":"-138,361.01","basege":"-0.0694","ettege":"-0.0694","otherp":"19,581.42","totalcp":"-118,779.60"},{"date":"2024-09-30","income":"113,088.34","expend":"155,911.80","profit":"-15,945.49","totalp":"-16,296.96","reprofit":"-16,171.39","basege":"-0.0058","ettege":"-0.0058","otherp":"196.91","totalcp":"-15,974.49"},{"date":"2024-06-30","income":"73,458.60","expend":"103,028.01","profit":"-6,926.51","totalp":"-8,184.53","reprofit":"-8,964.40","basege":"-0.0023","ettege":"-0.0023","otherp":"1,078.80","totalcp":"-7,885.60"},{"date":"2024-03-31","income":"39,023.72","expend":"52,559.58","profit":"-13,980.29","totalp":"-14,617.38","reprofit":"-13,632.95","basege":"-0.0068","ettege":"-0.0068","otherp":"979.32","totalcp":"-12,653.63"}]
[2025-04-30 11:15:05] [INFO] 开始解析JSON数据...
[2025-04-30 11:15:05] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 11:15:05] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "36,505.65",
  "expend": "42,768.22",
  "profit": "-527.89",
  "totalp": "-591.41",
  "reprofit": "-936.94",
  "basege": "-0.0003",
  "ettege": "-0.0003",
  "otherp": "-1,511.09",
  "totalcp": "-2,448.03"
}
[2025-04-30 11:15:05] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 11:15:05] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 11:15:05] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 36,506, 稀释每股收益: -0.0003, 净利润: -528, 综合收益总额: -591
[2025-04-30 11:15:05] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 11:15:05] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 11:15:05] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 142,372, 稀释每股收益: -0.0694, 净利润: -190,394, 综合收益总额: -171,751
[2025-04-30 11:15:05] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "113,088.34",
  "expend": "155,911.80",
  "profit": "-15,945.49",
  "totalp": "-16,296.96",
  "reprofit": "-16,171.39",
  "basege": "-0.0058",
  "ettege": "-0.0058",
  "otherp": "196.91",
  "totalcp": "-15,974.49"
}
[2025-04-30 11:15:05] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 11:15:05] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 113,088, 稀释每股收益: -0.0058, 净利润: -15,945, 综合收益总额: -16,297
[2025-04-30 11:15:05] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 11:15:05] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 11:15:05] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 73,459, 稀释每股收益: -0.0023, 净利润: -6,927, 综合收益总额: -8,185
[2025-04-30 11:15:05] [INFO] 获取到 5 条季度利润数据
[2025-04-30 11:15:05] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=36,506, 稀释每股收益=-0.0003, 净利润=-528, 综合收益总额=-591
[2025-04-30 11:15:05] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/4/30 11:31:36 ===
[2025-04-30 11:31:36] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-30 11:31:36] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 11:31:36] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 11:31:36] [INFO] 发送HTTP请求...
[2025-04-30 11:31:36] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-30 11:31:36] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-30 11:31:36] [INFO] 开始解析JSON数据...
[2025-04-30 11:31:36] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-30 11:31:36] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 11:31:36] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-30 11:31:36] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 11:31:36] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 11:31:36] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-04-30 11:31:36] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-30 11:31:36] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 11:31:36] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-30 11:31:36] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-04-30 11:31:36] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 11:31:36] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 11:31:36] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-04-30 11:31:36] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-30 11:31:36] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 11:31:36] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 11:31:36] [INFO] 返回 5 条季度利润数据
[2025-04-30 11:31:36] [INFO] DataGrid.Items.Count = 5
=== 日志开始于 2025/4/30 11:56:18 ===
[2025-04-30 11:56:18] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-30 11:56:18] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 11:56:18] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-30 11:56:18] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 11:56:18] [INFO] 发送HTTP请求...
[2025-04-30 11:56:19] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-30 11:56:19] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-30 11:56:19] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 11:56:19] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-30 11:56:19] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 11:56:19] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 11:56:19] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-04-30 11:56:19] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-30 11:56:19] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 11:56:19] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-04-30 11:56:19] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 11:56:19] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-30 11:56:19] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 11:56:19] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-04-30 11:56:19] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 11:56:19] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-30 11:56:19] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 11:56:19] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-04-30 11:56:19] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 11:56:19] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-30 11:56:19] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
=== 日志开始于 2025/4/30 12:58:43 ===
[2025-04-30 12:58:43] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-30 12:58:43] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 12:58:43] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 12:58:43] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-30 12:58:43] [INFO] 发送HTTP请求...
[2025-04-30 12:58:43] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-30 12:58:43] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-30 12:58:43] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 12:58:43] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-30 12:58:43] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 12:58:43] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 12:58:43] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-04-30 12:58:43] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-30 12:58:43] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 12:58:43] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 12:58:43] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-04-30 12:58:43] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-30 12:58:43] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 12:58:43] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 12:58:43] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-04-30 12:58:43] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-30 12:58:43] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 12:58:43] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 12:58:43] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-04-30 12:58:43] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-30 12:58:43] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 12:58:43] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 12:58:43] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: 348,253, 稀释每股收益: 0.3700, 净利润: 38,723, 综合收益总额: 38,841
[2025-04-30 12:58:43] [INFO] 返回 5 条季度利润数据
[2025-04-30 12:58:43] [INFO] 获取到 5 条季度利润数据
[2025-04-30 12:58:43] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=379,418, 稀释每股收益=0.4300, 净利润=47,819, 综合收益总额=47,950
[2025-04-30 12:58:43] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=1.52百万, 稀释每股收益=1.5400, 净利润=156,453, 综合收益总额=158,857
[2025-04-30 12:58:43] [DEBUG] 季度利润数据: 报告期=2024-06-30, 营收=717,462, 稀释每股收益=0.8000, 净利润=84,990, 综合收益总额=85,601
[2025-04-30 12:58:43] [DEBUG] 季度利润数据: 报告期=2024-03-31, 营收=348,253, 稀释每股收益=0.3700, 净利润=38,723, 综合收益总额=38,841
[2025-04-30 12:58:43] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-04-30 12:58:43] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-04-30 12:58:43] [INFO] DataGrid.Items.Count = 5
[2025-04-30 12:58:43] [INFO] DataGrid.HasItems = True
[2025-04-30 12:58:43] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/4/30 12:59:07 ===
[2025-04-30 12:59:07] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-30 12:59:07] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 12:59:07] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 12:59:07] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-30 12:59:07] [INFO] 发送HTTP请求...
[2025-04-30 12:59:08] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-30 12:59:08] [INFO] 开始解析JSON数据...
[2025-04-30 12:59:08] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-30 12:59:08] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 12:59:08] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-30 12:59:08] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 12:59:08] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 12:59:08] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-04-30 12:59:08] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-30 12:59:08] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 12:59:08] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 12:59:08] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-04-30 12:59:08] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 12:59:08] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-04-30 12:59:08] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 12:59:08] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-30 12:59:08] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 12:59:08] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-30 12:59:08] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-04-30 12:59:08] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 12:59:08] [INFO] 返回 5 条季度利润数据
[2025-04-30 12:59:08] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=379,418, 稀释每股收益=0.4300, 净利润=47,819, 综合收益总额=47,950
[2025-04-30 12:59:08] [INFO] 季度利润窗口加载完成
[2025-04-30 12:59:08] [INFO] DataGrid.Items.Count = 5
[2025-04-30 12:59:21] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-04-30 12:59:21] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
[2025-04-30 12:59:21] [INFO] 发送HTTP请求...
[2025-04-30 12:59:21] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-30 12:59:21] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"99,709.12","expend":"114,486.83","profit":"-12,889.22","totalp":"-13,066.73","reprofit":"-13,296.00","basege":"-0.0471","ettege":"-0.0471","otherp":"--","totalcp":"-13,296.00"},{"date":"2024-12-31","income":"481,546.68","expend":"518,094.66","profit":"-34,218.23","totalp":"-34,464.41","reprofit":"-36,513.97","basege":"-0.1100","ettege":"-0.1100","otherp":"--","totalcp":"-36,513.97"},{"date":"2024-09-30","income":"360,625.69","expend":"389,272.16","profit":"-28,340.64","totalp":"-28,348.39","reprofit":"-29,427.28","basege":"-0.0954","ettege":"-0.0954","otherp":"--","totalcp":"-29,427.28"},{"date":"2024-06-30","income":"242,046.17","expend":"260,552.39","profit":"-22,106.50","totalp":"-22,080.78","reprofit":"-22,882.48","basege":"-0.0768","ettege":"-0.0768","otherp":"--","totalcp":"-22,882.48"},{"date":"2024-03-31","income":"117,292.32","expend":"128,677.84","profit":"-12,844.49","totalp":"-12,848.19","reprofit":"-13,187.71","basege":"-0.0464","ettege":"-0.0464","otherp":"--","totalcp":"-13,187.71"}]
[2025-04-30 12:59:21] [INFO] 开始解析JSON数据...
[2025-04-30 12:59:21] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 12:59:21] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 99,709, 稀释每股收益: -0.0471, 净利润: -12,889, 综合收益总额: -13,067
[2025-04-30 12:59:21] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "360,625.69",
  "expend": "389,272.16",
  "profit": "-28,340.64",
  "totalp": "-28,348.39",
  "reprofit": "-29,427.28",
  "basege": "-0.0954",
  "ettege": "-0.0954",
  "otherp": "--",
  "totalcp": "-29,427.28"
}
[2025-04-30 12:59:21] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=481,547, 稀释每股收益=-0.1100, 净利润=-34,218, 综合收益总额=-34,464
[2025-04-30 12:59:21] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=360,626, 稀释每股收益=-0.0954, 净利润=-28,341, 综合收益总额=-28,348
[2025-04-30 12:59:21] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-04-30 12:59:21] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/4/30 13:06:06 ===
[2025-04-30 13:06:06] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-04-30 13:06:06] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-30 13:06:06] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-30 13:06:06] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
[2025-04-30 13:06:06] [INFO] 发送HTTP请求...
[2025-04-30 13:06:06] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-30 13:06:06] [INFO] 开始解析JSON数据...
[2025-04-30 13:06:06] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"99,709.12","expend":"114,486.83","profit":"-12,889.22","totalp":"-13,066.73","reprofit":"-13,296.00","basege":"-0.0471","ettege":"-0.0471","otherp":"--","totalcp":"-13,296.00"},{"date":"2024-12-31","income":"481,546.68","expend":"518,094.66","profit":"-34,218.23","totalp":"-34,464.41","reprofit":"-36,513.97","basege":"-0.1100","ettege":"-0.1100","otherp":"--","totalcp":"-36,513.97"},{"date":"2024-09-30","income":"360,625.69","expend":"389,272.16","profit":"-28,340.64","totalp":"-28,348.39","reprofit":"-29,427.28","basege":"-0.0954","ettege":"-0.0954","otherp":"--","totalcp":"-29,427.28"},{"date":"2024-06-30","income":"242,046.17","expend":"260,552.39","profit":"-22,106.50","totalp":"-22,080.78","reprofit":"-22,882.48","basege":"-0.0768","ettege":"-0.0768","otherp":"--","totalcp":"-22,882.48"},{"date":"2024-03-31","income":"117,292.32","expend":"128,677.84","profit":"-12,844.49","totalp":"-12,848.19","reprofit":"-13,187.71","basege":"-0.0464","ettege":"-0.0464","otherp":"--","totalcp":"-13,187.71"}]
[2025-04-30 13:06:06] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 13:06:06] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "99,709.12",
  "expend": "114,486.83",
  "profit": "-12,889.22",
  "totalp": "-13,066.73",
  "reprofit": "-13,296.00",
  "basege": "-0.0471",
  "ettege": "-0.0471",
  "otherp": "--",
  "totalcp": "-13,296.00"
}
[2025-04-30 13:06:06] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 13:06:06] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 13:06:06] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 99,709, 稀释每股收益: -0.0471, 净利润: -12,889, 综合收益总额: -13,067
[2025-04-30 13:06:06] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "481,546.68",
  "expend": "518,094.66",
  "profit": "-34,218.23",
  "totalp": "-34,464.41",
  "reprofit": "-36,513.97",
  "basege": "-0.1100",
  "ettege": "-0.1100",
  "otherp": "--",
  "totalcp": "-36,513.97"
}
[2025-04-30 13:06:06] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "360,625.69",
  "expend": "389,272.16",
  "profit": "-28,340.64",
  "totalp": "-28,348.39",
  "reprofit": "-29,427.28",
  "basege": "-0.0954",
  "ettege": "-0.0954",
  "otherp": "--",
  "totalcp": "-29,427.28"
}
[2025-04-30 13:06:06] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 13:06:06] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 481,547, 稀释每股收益: -0.1100, 净利润: -34,218, 综合收益总额: -34,464
[2025-04-30 13:06:06] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 13:06:06] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 360,626, 稀释每股收益: -0.0954, 净利润: -28,341, 综合收益总额: -28,348
[2025-04-30 13:06:06] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 13:06:06] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 13:06:06] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "242,046.17",
  "expend": "260,552.39",
  "profit": "-22,106.50",
  "totalp": "-22,080.78",
  "reprofit": "-22,882.48",
  "basege": "-0.0768",
  "ettege": "-0.0768",
  "otherp": "--",
  "totalcp": "-22,882.48"
}
[2025-04-30 13:06:06] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 13:06:06] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 242,046, 稀释每股收益: -0.0768, 净利润: -22,107, 综合收益总额: -22,081
[2025-04-30 13:06:06] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "117,292.32",
  "expend": "128,677.84",
  "profit": "-12,844.49",
  "totalp": "-12,848.19",
  "reprofit": "-13,187.71",
  "basege": "-0.0464",
  "ettege": "-0.0464",
  "otherp": "--",
  "totalcp": "-13,187.71"
}
[2025-04-30 13:06:06] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 13:06:06] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 13:06:06] [DEBUG] 季度利润数据: 报告期=2024-09-30, 营收=360,626, 稀释每股收益=-0.0954, 净利润=-28,341, 综合收益总额=-28,348
[2025-04-30 13:06:06] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-04-30 13:06:13] [INFO] 季度利润窗口加载 - 股票代码: 000021, 股票名称: 深科技
[2025-04-30 13:06:13] [INFO] 开始获取股票 000021 的季度利润数据
[2025-04-30 13:06:13] [INFO] 开始获取股票 000021 的季度利润数据
[2025-04-30 13:06:13] [INFO] 发送HTTP请求...
[2025-04-30 13:06:13] [INFO] HTTP请求成功，响应长度: 1060
[2025-04-30 13:06:13] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"336,463.82","expend":"302,999.03","profit":"28,045.86","totalp":"28,092.01","reprofit":"22,790.40","basege":"0.1147","ettege":"0.1147","otherp":"-2,553.17","totalcp":"20,237.23"},{"date":"2024-12-31","income":"1,482,716.65","expend":"1,347,906.19","profit":"123,715.42","totalp":"128,492.56","reprofit":"108,750.44","basege":"0.5962","ettege":"0.5962","otherp":"5,515.26","totalcp":"114,265.70"},{"date":"2024-09-30","income":"1,085,168.56","expend":"991,505.11","profit":"95,267.92","totalp":"96,803.44","reprofit":"81,211.98","basege":"0.4238","ettege":"0.4238","otherp":"3,715.15","totalcp":"84,927.13"},{"date":"2024-06-30","income":"705,456.41","expend":"652,810.88","profit":"56,153.66","totalp":"57,222.06","reprofit":"46,845.05","basege":"0.2309","ettege":"0.2309","otherp":"-14,796.33","totalcp":"32,048.72"},{"date":"2024-03-31","income":"312,637.90","expend":"293,190.19","profit":"21,811.85","totalp":"21,828.44","reprofit":"17,355.01","basege":"0.0780","ettege":"0.0780","otherp":"-11,244.20","totalcp":"6,110.81"}]
[2025-04-30 13:06:13] [INFO] 开始解析JSON数据...
[2025-04-30 13:06:13] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 13:06:13] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 13:06:13] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "336,463.82",
  "expend": "302,999.03",
  "profit": "28,045.86",
  "totalp": "28,092.01",
  "reprofit": "22,790.40",
  "basege": "0.1147",
  "ettege": "0.1147",
  "otherp": "-2,553.17",
  "totalcp": "20,237.23"
}
[2025-04-30 13:06:13] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 13:06:13] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 13:06:13] [DEBUG] 季度利润数据: 报告期=2024-06-30, 营收=705,456, 稀释每股收益=0.2309, 净利润=56,154, 综合收益总额=57,222
[2025-04-30 13:06:13] [INFO] DataGrid.Items.Count = 5
[2025-04-30 13:06:13] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-04-30 13:08:12] [INFO] 开始获取股票 000021 的季度利润数据
[2025-04-30 13:08:12] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
[2025-04-30 13:08:12] [INFO] 发送HTTP请求...
[2025-04-30 13:08:12] [INFO] HTTP请求成功，响应长度: 1060
[2025-04-30 13:08:12] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"336,463.82","expend":"302,999.03","profit":"28,045.86","totalp":"28,092.01","reprofit":"22,790.40","basege":"0.1147","ettege":"0.1147","otherp":"-2,553.17","totalcp":"20,237.23"},{"date":"2024-12-31","income":"1,482,716.65","expend":"1,347,906.19","profit":"123,715.42","totalp":"128,492.56","reprofit":"108,750.44","basege":"0.5962","ettege":"0.5962","otherp":"5,515.26","totalcp":"114,265.70"},{"date":"2024-09-30","income":"1,085,168.56","expend":"991,505.11","profit":"95,267.92","totalp":"96,803.44","reprofit":"81,211.98","basege":"0.4238","ettege":"0.4238","otherp":"3,715.15","totalcp":"84,927.13"},{"date":"2024-06-30","income":"705,456.41","expend":"652,810.88","profit":"56,153.66","totalp":"57,222.06","reprofit":"46,845.05","basege":"0.2309","ettege":"0.2309","otherp":"-14,796.33","totalcp":"32,048.72"},{"date":"2024-03-31","income":"312,637.90","expend":"293,190.19","profit":"21,811.85","totalp":"21,828.44","reprofit":"17,355.01","basege":"0.0780","ettege":"0.0780","otherp":"-11,244.20","totalcp":"6,110.81"}]
[2025-04-30 13:08:12] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "336,463.82",
  "expend": "302,999.03",
  "profit": "28,045.86",
  "totalp": "28,092.01",
  "reprofit": "22,790.40",
  "basege": "0.1147",
  "ettege": "0.1147",
  "otherp": "-2,553.17",
  "totalcp": "20,237.23"
}
[2025-04-30 13:08:12] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 13:08:12] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 336,464, 稀释每股收益: 0.1147, 净利润: 28,046, 综合收益总额: 28,092
[2025-04-30 13:08:12] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 13:08:12] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 13:08:12] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 13:08:12] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.48百万, 稀释每股收益: 0.5962, 净利润: 123,715, 综合收益总额: 128,493
[2025-04-30 13:08:12] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 0.4238, 净利润: 95,268, 综合收益总额: 96,803
[2025-04-30 13:08:12] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 13:08:12] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 13:08:12] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 705,456, 稀释每股收益: 0.2309, 净利润: 56,154, 综合收益总额: 57,222
[2025-04-30 13:08:12] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "312,637.90",
  "expend": "293,190.19",
  "profit": "21,811.85",
  "totalp": "21,828.44",
  "reprofit": "17,355.01",
  "basege": "0.0780",
  "ettege": "0.0780",
  "otherp": "-11,244.20",
  "totalcp": "6,110.81"
}
[2025-04-30 13:08:12] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 13:08:12] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 13:08:12] [INFO] 返回 5 条季度利润数据
[2025-04-30 13:08:12] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=1.48百万, 稀释每股收益=0.5962, 净利润=123,715, 综合收益总额=128,493
[2025-04-30 13:08:12] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=336,464, 稀释每股收益=0.1147, 净利润=28,046, 综合收益总额=28,092
[2025-04-30 13:08:12] [INFO] DataGrid.HasItems = True
=== 日志开始于 2025/4/30 13:20:08 ===
[2025-04-30 13:20:08] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-04-30 13:20:08] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 13:20:08] [INFO] 开始获取股票 600298 的季度利润数据
[2025-04-30 13:20:08] [INFO] 发送HTTP请求...
[2025-04-30 13:20:08] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-04-30 13:20:08] [INFO] HTTP请求成功，响应长度: 1028
[2025-04-30 13:20:08] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-04-30 13:20:08] [INFO] 开始解析JSON数据...
[2025-04-30 13:20:08] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 13:20:08] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-04-30 13:20:08] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 13:20:08] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 13:20:08] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-04-30 13:20:08] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-04-30 13:20:08] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 13:20:08] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 13:20:08] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-04-30 13:20:08] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-04-30 13:20:08] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 13:20:08] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 13:20:08] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-04-30 13:20:08] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-04-30 13:20:08] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 13:20:08] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 13:20:08] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-04-30 13:20:08] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-04-30 13:20:08] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-04-30 13:20:08] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: 348,253, 稀释每股收益: 0.3700, 净利润: 38,723, 综合收益总额: 38,841
[2025-04-30 13:20:08] [INFO] 返回 5 条季度利润数据
[2025-04-30 13:20:08] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/4/30 15:19:58 ===
[2025-04-30 15:19:58] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-04-30 15:19:58] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-30 15:19:58] [INFO] 开始获取股票 000516 的季度利润数据
[2025-04-30 15:19:58] [INFO] 发送HTTP请求...
[2025-04-30 15:19:58] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
[2025-04-30 15:19:58] [INFO] HTTP请求成功，响应长度: 1045
[2025-04-30 15:19:58] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"99,709.12","expend":"114,486.83","profit":"-12,889.22","totalp":"-13,066.73","reprofit":"-13,296.00","basege":"-0.0471","ettege":"-0.0471","otherp":"--","totalcp":"-13,296.00"},{"date":"2024-12-31","income":"481,546.68","expend":"518,094.66","profit":"-34,218.23","totalp":"-34,464.41","reprofit":"-36,513.97","basege":"-0.1100","ettege":"-0.1100","otherp":"--","totalcp":"-36,513.97"},{"date":"2024-09-30","income":"360,625.69","expend":"389,272.16","profit":"-28,340.64","totalp":"-28,348.39","reprofit":"-29,427.28","basege":"-0.0954","ettege":"-0.0954","otherp":"--","totalcp":"-29,427.28"},{"date":"2024-06-30","income":"242,046.17","expend":"260,552.39","profit":"-22,106.50","totalp":"-22,080.78","reprofit":"-22,882.48","basege":"-0.0768","ettege":"-0.0768","otherp":"--","totalcp":"-22,882.48"},{"date":"2024-03-31","income":"117,292.32","expend":"128,677.84","profit":"-12,844.49","totalp":"-12,848.19","reprofit":"-13,187.71","basege":"-0.0464","ettege":"-0.0464","otherp":"--","totalcp":"-13,187.71"}]
[2025-04-30 15:19:58] [INFO] 开始解析JSON数据...
[2025-04-30 15:19:58] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-04-30 15:19:58] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "99,709.12",
  "expend": "114,486.83",
  "profit": "-12,889.22",
  "totalp": "-13,066.73",
  "reprofit": "-13,296.00",
  "basege": "-0.0471",
  "ettege": "-0.0471",
  "otherp": "--",
  "totalcp": "-13,296.00"
}
[2025-04-30 15:19:58] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-04-30 15:19:59] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-04-30 15:19:59] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 99,709, 稀释每股收益: -0.0471, 净利润: -12,889, 综合收益总额: -13,067
[2025-04-30 15:19:59] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "481,546.68",
  "expend": "518,094.66",
  "profit": "-34,218.23",
  "totalp": "-34,464.41",
  "reprofit": "-36,513.97",
  "basege": "-0.1100",
  "ettege": "-0.1100",
  "otherp": "--",
  "totalcp": "-36,513.97"
}
[2025-04-30 15:19:59] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-04-30 15:19:59] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-04-30 15:19:59] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-04-30 15:19:59] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-04-30 15:19:59] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "242,046.17",
  "expend": "260,552.39",
  "profit": "-22,106.50",
  "totalp": "-22,080.78",
  "reprofit": "-22,882.48",
  "basege": "-0.0768",
  "ettege": "-0.0768",
  "otherp": "--",
  "totalcp": "-22,882.48"
}
[2025-04-30 15:19:59] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-04-30 15:19:59] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-04-30 15:19:59] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-04-30 15:19:59] [INFO] 获取到 5 条季度利润数据
[2025-04-30 15:19:59] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=481,547, 稀释每股收益=-0.1100, 净利润=-34,218, 综合收益总额=-34,464
[2025-04-30 15:19:59] [DEBUG] 季度利润数据: 报告期=2024-03-31, 营收=117,292, 稀释每股收益=-0.0464, 净利润=-12,844, 综合收益总额=-12,848
[2025-04-30 15:19:59] [DEBUG] 季度利润数据: 报告期=2024-06-30, 营收=242,046, 稀释每股收益=-0.0768, 净利润=-22,107, 综合收益总额=-22,081
[2025-04-30 15:19:59] [INFO] DataGrid.HasItems = True
[2025-04-30 15:19:59] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-04-30 15:19:59] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/5/6 9:35:26 ===
[2025-05-06 09:35:26] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-05-06 09:35:26] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-06 09:35:26] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-06 09:35:26] [INFO] 发送HTTP请求...
[2025-05-06 09:35:26] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-05-06 09:35:26] [INFO] HTTP请求成功，响应长度: 1028
[2025-05-06 09:35:26] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-05-06 09:35:26] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-06 09:35:26] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-05-06 09:35:26] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-06 09:35:26] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-06 09:35:26] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-05-06 09:35:26] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-05-06 09:35:26] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-06 09:35:26] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-05-06 09:35:26] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-05-06 09:35:26] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-05-06 09:35:26] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-05-06 09:35:26] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-05-06 09:35:26] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-05-06 09:35:26] [INFO] 获取到 5 条季度利润数据
[2025-05-06 09:35:26] [DEBUG] 季度利润数据: 报告期=2024-03-31, 营收=348,253, 稀释每股收益=0.3700, 净利润=38,723, 综合收益总额=38,841
[2025-05-06 09:35:26] [INFO] 成功绑定 5 条季度利润数据到UI
[2025-05-06 09:35:26] [INFO] DataGrid.Items.Count = 5
[2025-05-06 09:35:26] [INFO] DataGrid.HasItems = True
[2025-05-06 09:35:26] [INFO] 季度利润窗口加载完成
[2025-05-06 10:17:49] [INFO] 开始获取股票 000525 的季度利润数据
[2025-05-06 10:17:49] [INFO] 季度利润窗口加载 - 股票代码: 000525, 股票名称: ST红太阳
[2025-05-06 10:17:49] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000525/72430658081e51fc98
[2025-05-06 10:17:49] [INFO] 开始获取股票 000525 的季度利润数据
[2025-05-06 10:17:49] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-06 10:17:49] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 84,773, 稀释每股收益: 0.0173, 净利润: 2,879, 综合收益总额: 2,822
[2025-05-06 10:17:49] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "300,703.28",
  "expend": "414,977.42",
  "profit": "164,547.66",
  "totalp": "13,923.86",
  "reprofit": "35,866.69",
  "basege": "0.3000",
  "ettege": "0.3000",
  "otherp": "-33.78",
  "totalcp": "35,832.92"
}
[2025-05-06 10:17:49] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-05-06 10:17:49] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 300,703, 稀释每股收益: 0.3000, 净利润: 164,548, 综合收益总额: 13,924
[2025-05-06 10:17:49] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-06 10:17:49] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "270,253.81",
  "expend": "272,103.17",
  "profit": "3,000.74",
  "totalp": "2,230.23",
  "reprofit": "2,116.67",
  "basege": "0.0461",
  "ettege": "0.0461",
  "otherp": "--",
  "totalcp": "2,116.67"
}
[2025-05-06 10:17:49] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-05-06 10:17:49] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 164,239, 稀释每股收益: 0.0366, 净利润: 2,723, 综合收益总额: 1,407
[2025-05-06 10:17:49] [INFO] 获取到 5 条季度利润数据
[2025-05-06 10:17:49] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=84,773, 稀释每股收益=0.0173, 净利润=2,879, 综合收益总额=2,822
[2025-05-06 10:17:53] [INFO] 季度利润窗口加载 - 股票代码: 000021, 股票名称: 深科技
[2025-05-06 10:17:53] [INFO] 开始获取股票 000021 的季度利润数据
[2025-05-06 10:17:53] [INFO] 开始获取股票 000021 的季度利润数据
[2025-05-06 10:17:53] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"336,463.82","expend":"302,999.03","profit":"28,045.86","totalp":"28,092.01","reprofit":"22,790.40","basege":"0.1147","ettege":"0.1147","otherp":"-2,553.17","totalcp":"20,237.23"},{"date":"2024-12-31","income":"1,482,716.65","expend":"1,347,906.19","profit":"123,715.42","totalp":"128,492.56","reprofit":"108,750.44","basege":"0.5962","ettege":"0.5962","otherp":"5,515.26","totalcp":"114,265.70"},{"date":"2024-09-30","income":"1,085,168.56","expend":"991,505.11","profit":"95,267.92","totalp":"96,803.44","reprofit":"81,211.98","basege":"0.4238","ettege":"0.4238","otherp":"3,715.15","totalcp":"84,927.13"},{"date":"2024-06-30","income":"705,456.41","expend":"652,810.88","profit":"56,153.66","totalp":"57,222.06","reprofit":"46,845.05","basege":"0.2309","ettege":"0.2309","otherp":"-14,796.33","totalcp":"32,048.72"},{"date":"2024-03-31","income":"312,637.90","expend":"293,190.19","profit":"21,811.85","totalp":"21,828.44","reprofit":"17,355.01","basege":"0.0780","ettege":"0.0780","otherp":"-11,244.20","totalcp":"6,110.81"}]
[2025-05-06 10:17:53] [INFO] 开始解析JSON数据...
[2025-05-06 10:17:53] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-06 10:17:53] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-06 10:17:53] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,482,716.65",
  "expend": "1,347,906.19",
  "profit": "123,715.42",
  "totalp": "128,492.56",
  "reprofit": "108,750.44",
  "basege": "0.5962",
  "ettege": "0.5962",
  "otherp": "5,515.26",
  "totalcp": "114,265.70"
}
[2025-05-06 10:17:53] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,085,168.56",
  "expend": "991,505.11",
  "profit": "95,267.92",
  "totalp": "96,803.44",
  "reprofit": "81,211.98",
  "basege": "0.4238",
  "ettege": "0.4238",
  "otherp": "3,715.15",
  "totalcp": "84,927.13"
}
[2025-05-06 10:17:53] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-05-06 10:17:53] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-05-06 10:17:53] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=336,464, 稀释每股收益=0.1147, 净利润=28,046, 综合收益总额=28,092
[2025-05-06 10:17:53] [INFO] 季度利润窗口加载完成
[2025-05-06 10:17:58] [INFO] 季度利润窗口加载 - 股票代码: 000516, 股票名称: 国际医学
[2025-05-06 10:17:58] [INFO] 开始获取股票 000516 的季度利润数据
[2025-05-06 10:17:58] [INFO] 开始获取股票 000516 的季度利润数据
[2025-05-06 10:17:58] [INFO] HTTP请求成功，响应长度: 1045
[2025-05-06 10:17:58] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000516/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"99,709.12","expend":"114,486.83","profit":"-12,889.22","totalp":"-13,066.73","reprofit":"-13,296.00","basege":"-0.0471","ettege":"-0.0471","otherp":"--","totalcp":"-13,296.00"},{"date":"2024-12-31","income":"481,546.68","expend":"518,094.66","profit":"-34,218.23","totalp":"-34,464.41","reprofit":"-36,513.97","basege":"-0.1100","ettege":"-0.1100","otherp":"--","totalcp":"-36,513.97"},{"date":"2024-09-30","income":"360,625.69","expend":"389,272.16","profit":"-28,340.64","totalp":"-28,348.39","reprofit":"-29,427.28","basege":"-0.0954","ettege":"-0.0954","otherp":"--","totalcp":"-29,427.28"},{"date":"2024-06-30","income":"242,046.17","expend":"260,552.39","profit":"-22,106.50","totalp":"-22,080.78","reprofit":"-22,882.48","basege":"-0.0768","ettege":"-0.0768","otherp":"--","totalcp":"-22,882.48"},{"date":"2024-03-31","income":"117,292.32","expend":"128,677.84","profit":"-12,844.49","totalp":"-12,848.19","reprofit":"-13,187.71","basege":"-0.0464","ettege":"-0.0464","otherp":"--","totalcp":"-13,187.71"}]
[2025-05-06 10:17:58] [INFO] 开始解析JSON数据...
[2025-05-06 10:17:58] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 99,709, 稀释每股收益: -0.0471, 净利润: -12,889, 综合收益总额: -13,067
[2025-05-06 10:17:58] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "242,046.17",
  "expend": "260,552.39",
  "profit": "-22,106.50",
  "totalp": "-22,080.78",
  "reprofit": "-22,882.48",
  "basege": "-0.0768",
  "ettege": "-0.0768",
  "otherp": "--",
  "totalcp": "-22,882.48"
}
[2025-05-06 10:17:58] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-05-06 10:17:58] [INFO] DataGrid.Items.Count = 5
=== 日志开始于 2025/5/6 11:31:32 ===
[2025-05-06 11:31:32] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-05-06 11:31:32] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-06 11:31:32] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-06 11:31:32] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-05-06 11:31:32] [INFO] 发送HTTP请求...
[2025-05-06 11:31:32] [INFO] HTTP请求成功，响应长度: 1028
[2025-05-06 11:31:32] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-05-06 11:31:32] [INFO] 开始解析JSON数据...
[2025-05-06 11:31:32] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-06 11:31:32] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-05-06 11:31:32] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-06 11:31:32] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-06 11:31:32] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-05-06 11:31:32] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-05-06 11:31:32] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-06 11:31:32] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-05-06 11:31:32] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-05-06 11:31:32] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-05-06 11:31:32] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-05-06 11:31:32] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-05-06 11:31:32] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-05-06 11:31:32] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: 348,253, 稀释每股收益: 0.3700, 净利润: 38,723, 综合收益总额: 38,841
[2025-05-06 11:31:32] [INFO] 返回 5 条季度利润数据
[2025-05-06 11:31:32] [INFO] 获取到 5 条季度利润数据
[2025-05-06 11:31:32] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=379,418, 稀释每股收益=0.4300, 净利润=47,819, 综合收益总额=47,950
[2025-05-06 11:31:32] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=1.52百万, 稀释每股收益=1.5400, 净利润=156,453, 综合收益总额=158,857
[2025-05-06 11:31:32] [INFO] 成功绑定 5 条季度利润数据到UI
=== 日志开始于 2025/5/7 15:57:15 ===
[2025-05-07 15:57:15] [INFO] 季度利润窗口加载 - 股票代码: 000021, 股票名称: 深科技
[2025-05-07 15:57:15] [INFO] 开始获取股票 000021 的季度利润数据
[2025-05-07 15:57:15] [INFO] 开始获取股票 000021 的季度利润数据
[2025-05-07 15:57:15] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
[2025-05-07 15:57:15] [INFO] 发送HTTP请求...
[2025-05-07 15:57:15] [INFO] HTTP请求成功，响应长度: 1060
[2025-05-07 15:57:15] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"336,463.82","expend":"302,999.03","profit":"28,045.86","totalp":"28,092.01","reprofit":"22,790.40","basege":"0.1147","ettege":"0.1147","otherp":"-2,553.17","totalcp":"20,237.23"},{"date":"2024-12-31","income":"1,482,716.65","expend":"1,347,906.19","profit":"123,715.42","totalp":"128,492.56","reprofit":"108,750.44","basege":"0.5962","ettege":"0.5962","otherp":"5,515.26","totalcp":"114,265.70"},{"date":"2024-09-30","income":"1,085,168.56","expend":"991,505.11","profit":"95,267.92","totalp":"96,803.44","reprofit":"81,211.98","basege":"0.4238","ettege":"0.4238","otherp":"3,715.15","totalcp":"84,927.13"},{"date":"2024-06-30","income":"705,456.41","expend":"652,810.88","profit":"56,153.66","totalp":"57,222.06","reprofit":"46,845.05","basege":"0.2309","ettege":"0.2309","otherp":"-14,796.33","totalcp":"32,048.72"},{"date":"2024-03-31","income":"312,637.90","expend":"293,190.19","profit":"21,811.85","totalp":"21,828.44","reprofit":"17,355.01","basege":"0.0780","ettege":"0.0780","otherp":"-11,244.20","totalcp":"6,110.81"}]
[2025-05-07 15:57:15] [INFO] 开始解析JSON数据...
[2025-05-07 15:57:15] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-07 15:57:15] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "336,463.82",
  "expend": "302,999.03",
  "profit": "28,045.86",
  "totalp": "28,092.01",
  "reprofit": "22,790.40",
  "basege": "0.1147",
  "ettege": "0.1147",
  "otherp": "-2,553.17",
  "totalcp": "20,237.23"
}
[2025-05-07 15:57:15] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-07 15:57:15] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-07 15:57:15] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 336,464, 稀释每股收益: 0.1147, 净利润: 28,046, 综合收益总额: 28,092
[2025-05-07 15:57:15] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,482,716.65",
  "expend": "1,347,906.19",
  "profit": "123,715.42",
  "totalp": "128,492.56",
  "reprofit": "108,750.44",
  "basege": "0.5962",
  "ettege": "0.5962",
  "otherp": "5,515.26",
  "totalcp": "114,265.70"
}
[2025-05-07 15:57:15] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.48百万, 稀释每股收益: 0.5962, 净利润: 123,715, 综合收益总额: 128,493
[2025-05-07 15:57:15] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,085,168.56",
  "expend": "991,505.11",
  "profit": "95,267.92",
  "totalp": "96,803.44",
  "reprofit": "81,211.98",
  "basege": "0.4238",
  "ettege": "0.4238",
  "otherp": "3,715.15",
  "totalcp": "84,927.13"
}
[2025-05-07 15:57:15] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-05-07 15:57:15] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-07 15:57:15] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 0.4238, 净利润: 95,268, 综合收益总额: 96,803
[2025-05-07 15:57:15] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "705,456.41",
  "expend": "652,810.88",
  "profit": "56,153.66",
  "totalp": "57,222.06",
  "reprofit": "46,845.05",
  "basege": "0.2309",
  "ettege": "0.2309",
  "otherp": "-14,796.33",
  "totalcp": "32,048.72"
}
[2025-05-07 15:57:15] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-05-07 15:57:15] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-05-07 15:57:15] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-05-07 15:57:15] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "312,637.90",
  "expend": "293,190.19",
  "profit": "21,811.85",
  "totalp": "21,828.44",
  "reprofit": "17,355.01",
  "basege": "0.0780",
  "ettege": "0.0780",
  "otherp": "-11,244.20",
  "totalcp": "6,110.81"
}
[2025-05-07 15:57:15] [INFO] 返回 5 条季度利润数据
[2025-05-07 15:57:15] [INFO] 获取到 5 条季度利润数据
[2025-05-07 15:57:15] [INFO] 季度利润窗口加载完成
[2025-05-07 15:57:19] [INFO] 季度利润窗口加载 - 股票代码: 000525, 股票名称: ST红太阳
[2025-05-07 15:57:19] [INFO] HTTP请求成功，响应长度: 1006
[2025-05-07 15:57:19] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000525/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"84,773.06","expend":"85,101.02","profit":"2,878.50","totalp":"2,822.16","reprofit":"2,388.26","basege":"0.0173","ettege":"0.0173","otherp":"--","totalcp":"2,388.26"},{"date":"2024-12-31","income":"300,703.28","expend":"414,977.42","profit":"164,547.66","totalp":"13,923.86","reprofit":"35,866.69","basege":"0.3000","ettege":"0.3000","otherp":"-33.78","totalcp":"35,832.92"},{"date":"2024-09-30","income":"270,253.81","expend":"272,103.17","profit":"3,000.74","totalp":"2,230.23","reprofit":"2,116.67","basege":"0.0461","ettege":"0.0461","otherp":"--","totalcp":"2,116.67"},{"date":"2024-06-30","income":"164,238.64","expend":"164,600.42","profit":"2,722.97","totalp":"1,407.17","reprofit":"1,990.99","basege":"0.0366","ettege":"0.0366","otherp":"-347.54","totalcp":"1,643.44"},{"date":"2024-03-31","income":"84,569.59","expend":"84,633.52","profit":"3,064.12","totalp":"2,807.14","reprofit":"1,903.50","basege":"0.0346","ettege":"0.0346","otherp":"--","totalcp":"1,903.50"}]
[2025-05-07 15:57:19] [INFO] 开始解析JSON数据...
[2025-05-07 15:57:19] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-07 15:57:19] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "84,773.06",
  "expend": "85,101.02",
  "profit": "2,878.50",
  "totalp": "2,822.16",
  "reprofit": "2,388.26",
  "basege": "0.0173",
  "ettege": "0.0173",
  "otherp": "--",
  "totalcp": "2,388.26"
}
[2025-05-07 15:57:19] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-07 15:57:19] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-05-07 15:57:19] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 270,254, 稀释每股收益: 0.0461, 净利润: 3,001, 综合收益总额: 2,230
[2025-05-07 15:57:19] [DEBUG] 季度利润数据: 报告期=2024-12-31, 营收=300,703, 稀释每股收益=0.3000, 净利润=164,548, 综合收益总额=13,924
[2025-05-07 15:57:19] [INFO] DataGrid.Items.Count = 5
[2025-05-07 15:57:19] [INFO] 季度利润窗口加载完成
[2025-05-07 15:57:23] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-05-07 15:57:23] [INFO] 发送HTTP请求...
[2025-05-07 15:57:23] [INFO] 开始解析JSON数据...
[2025-05-07 15:57:23] [INFO] HTTP请求成功，响应长度: 1028
[2025-05-07 15:57:23] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-07 15:57:23] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-07 15:57:23] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-05-07 15:57:23] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-05-07 15:57:23] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-07 15:57:23] [INFO] 开始绑定 5 条季度利润数据到UI
[2025-05-07 15:57:23] [INFO] DataGrid.Items.Count = 5
=== 日志开始于 2025/5/12 10:54:08 ===
[2025-05-12 10:54:08] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-05-12 10:54:08] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-12 10:54:08] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-12 10:54:08] [INFO] 发送HTTP请求...
[2025-05-12 10:54:09] [INFO] HTTP请求成功，响应长度: 1028
[2025-05-12 10:54:09] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-05-12 10:54:09] [INFO] 开始解析JSON数据...
[2025-05-12 10:54:09] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-12 10:54:09] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-05-12 10:54:09] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-12 10:54:09] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-12 10:54:09] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-05-12 10:54:09] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-05-12 10:54:09] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-05-12 10:54:09] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-12 10:54:09] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-05-12 10:54:09] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-05-12 10:54:09] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-05-12 10:54:09] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-05-12 10:54:09] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-05-12 10:54:09] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-05-12 10:54:09] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-05-12 10:54:09] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-05-12 10:54:09] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-05-12 10:54:09] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-05-12 10:54:09] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: 348,253, 稀释每股收益: 0.3700, 净利润: 38,723, 综合收益总额: 38,841
[2025-05-12 10:54:09] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=379,418, 稀释每股收益=0.4300, 净利润=47,819, 综合收益总额=47,950
[2025-05-12 10:54:09] [DEBUG] 季度利润数据: 报告期=2024-06-30, 营收=717,462, 稀释每股收益=0.8000, 净利润=84,990, 综合收益总额=85,601
[2025-05-12 10:54:13] [INFO] 季度利润窗口加载 - 股票代码: 000021, 股票名称: 深科技
[2025-05-12 10:54:13] [INFO] HTTP请求成功，响应长度: 1060
[2025-05-12 10:54:13] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"336,463.82","expend":"302,999.03","profit":"28,045.86","totalp":"28,092.01","reprofit":"22,790.40","basege":"0.1147","ettege":"0.1147","otherp":"-2,553.17","totalcp":"20,237.23"},{"date":"2024-12-31","income":"1,482,716.65","expend":"1,347,906.19","profit":"123,715.42","totalp":"128,492.56","reprofit":"108,750.44","basege":"0.5962","ettege":"0.5962","otherp":"5,515.26","totalcp":"114,265.70"},{"date":"2024-09-30","income":"1,085,168.56","expend":"991,505.11","profit":"95,267.92","totalp":"96,803.44","reprofit":"81,211.98","basege":"0.4238","ettege":"0.4238","otherp":"3,715.15","totalcp":"84,927.13"},{"date":"2024-06-30","income":"705,456.41","expend":"652,810.88","profit":"56,153.66","totalp":"57,222.06","reprofit":"46,845.05","basege":"0.2309","ettege":"0.2309","otherp":"-14,796.33","totalcp":"32,048.72"},{"date":"2024-03-31","income":"312,637.90","expend":"293,190.19","profit":"21,811.85","totalp":"21,828.44","reprofit":"17,355.01","basege":"0.0780","ettege":"0.0780","otherp":"-11,244.20","totalcp":"6,110.81"}]
[2025-05-12 10:54:13] [INFO] 开始解析JSON数据...
[2025-05-12 10:54:13] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "336,463.82",
  "expend": "302,999.03",
  "profit": "28,045.86",
  "totalp": "28,092.01",
  "reprofit": "22,790.40",
  "basege": "0.1147",
  "ettege": "0.1147",
  "otherp": "-2,553.17",
  "totalcp": "20,237.23"
}
[2025-05-12 10:54:13] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-12 10:54:13] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 336,464, 稀释每股收益: 0.1147, 净利润: 28,046, 综合收益总额: 28,092
[2025-05-12 10:54:13] [INFO] 获取到 5 条季度利润数据
[2025-05-12 10:54:13] [INFO] DataGrid.HasItems = True
=== 日志开始于 2025/5/13 15:25:37 ===
[2025-05-13 15:25:37] [INFO] 月度盈亏数据文件不存在: D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-13 15:25:37] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-13 15:25:42] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:25:42] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 15:27:55] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:27:55] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-13 15:27:58] [INFO] 月度盈亏设置已更新
[2025-05-13 15:27:58] [INFO] 成功加载月度盈亏数据，共 1 条记录
=== 日志开始于 2025/5/13 15:28:42 ===
[2025-05-13 15:28:42] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:28:46] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:28:46] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 15:29:08] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:29:08] [INFO] 已加载用户 xiekun 的月度盈亏数据
=== 日志开始于 2025/5/13 15:29:39 ===
[2025-05-13 15:29:39] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:29:44] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:29:44] [INFO] 已加载用户 xiekun 的月度盈亏数据
=== 日志开始于 2025/5/13 15:34:41 ===
[2025-05-13 15:34:41] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:34:49] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:34:49] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 15:35:24] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:35:24] [INFO] 已加载用户 xiekun 的月度盈亏数据
=== 日志开始于 2025/5/13 15:44:16 ===
[2025-05-13 15:44:16] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:44:24] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:44:24] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 15:44:28] [INFO] 每日盈亏数据文件不存在: D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:44:28] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:44:28] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 15:44:32] [INFO] 成功加载每日盈亏数据，共 0 条记录
[2025-05-13 15:44:32] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:44:32] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 15:44:48] [INFO] 成功加载每日盈亏数据，共 0 条记录
[2025-05-13 15:44:48] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:44:48] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-13 15:46:09] [INFO] 成功加载每日盈亏数据，共 0 条记录
[2025-05-13 15:46:09] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:46:09] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-13 15:46:21] [INFO] 成功加载每日盈亏数据，共 0 条记录
[2025-05-13 15:46:21] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:46:21] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 15:47:02] [INFO] 成功加载每日盈亏数据，共 0 条记录
[2025-05-13 15:47:02] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:47:02] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-13 15:51:46] [INFO] 成功加载每日盈亏数据，共 0 条记录
[2025-05-13 15:51:46] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:51:52] [INFO] 成功加载每日盈亏数据，共 1 条记录
[2025-05-13 15:51:52] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:51:52] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 15:51:54] [INFO] 成功加载每日盈亏数据，共 1 条记录
[2025-05-13 15:51:54] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-13 15:51:56] [INFO] 成功加载每日盈亏数据，共 1 条记录
[2025-05-13 15:51:56] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:51:56] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-13 15:54:56] [INFO] 成功加载每日盈亏数据，共 1 条记录
[2025-05-13 15:54:56] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:55:03] [INFO] 成功加载每日盈亏数据，共 2 条记录
[2025-05-13 15:55:03] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:55:03] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 15:55:06] [INFO] 成功加载每日盈亏数据，共 2 条记录
[2025-05-13 15:55:06] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-13 15:55:09] [INFO] 成功加载每日盈亏数据，共 2 条记录
[2025-05-13 15:55:09] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-13 15:55:11] [INFO] 成功加载每日盈亏数据，共 2 条记录
[2025-05-13 15:55:11] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:55:11] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 15:57:57] [INFO] 成功加载每日盈亏数据，共 2 条记录
[2025-05-13 15:57:57] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 15:58:29] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:58:29] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-13 15:58:30] [INFO] 月度盈亏设置已更新
[2025-05-13 15:58:30] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:58:35] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 15:58:35] [INFO] 已加载用户 xiekun 的月度盈亏数据
=== 日志开始于 2025/5/13 16:01:56 ===
[2025-05-13 16:01:56] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 16:03:43] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 16:03:43] [INFO] 已加载用户 xiekun 的月度盈亏数据
=== 日志开始于 2025/5/13 16:10:34 ===
[2025-05-13 16:10:34] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 16:11:03] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 16:11:03] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 16:11:10] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:11:10] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 16:11:17] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:11:17] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-13 16:11:40] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:11:40] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-13 16:13:35] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:13:35] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 16:14:01] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:14:01] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-13 16:14:01] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 16:14:04] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:14:04] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
=== 日志开始于 2025/5/13 16:21:01 ===
[2025-05-13 16:21:01] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 16:21:19] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 16:21:19] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 16:21:24] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 16:21:24] [INFO] 成功加载月度盈亏数据，共 1 条记录
[2025-05-13 16:21:24] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-13 16:21:24] [INFO] 成功加载月度盈亏数据，共 2 条记录
[2025-05-13 16:21:24] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-13 16:21:24] [INFO] 成功加载月度盈亏数据，共 3 条记录
[2025-05-13 16:21:24] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-13 16:21:49] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:21:49] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 16:21:52] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:21:52] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-13 16:21:55] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:21:55] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:21:55] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:21:55] [INFO] 成功加载月度盈亏数据，共 4 条记录
=== 日志开始于 2025/5/13 16:25:12 ===
[2025-05-13 16:25:12] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:35:26] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:35:26] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 16:35:29] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 16:35:29] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:35:29] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:35:29] [INFO] 三人当月总盈亏数据: 谢羚羚=0, 景丝丝=0, 钟鹮鹮=0
=== 日志开始于 2025/5/13 16:38:25 ===
[2025-05-13 16:38:25] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:50:43] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:50:43] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 16:50:46] [INFO] 开始创建三人盈亏测试数据
[2025-05-13 16:50:46] [INFO] 当前CSV文件中有 4 条用户数据
[2025-05-13 16:50:46] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:50:46] [INFO] 现有用户: xiekun
[2025-05-13 16:50:46] [INFO] 现有用户: 谢羚羚
[2025-05-13 16:50:46] [INFO] 现有用户: 钟鹮鹮
[2025-05-13 16:50:46] [INFO] 现有用户: 景丝丝
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 已存在，检查月度数据
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 2 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 3 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 5 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 4 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 6 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 7 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 8 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 12 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 5 更新盈亏值: -324
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 6 更新盈亏值: 7573
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 7 更新盈亏值: -440
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 8 更新盈亏值: 9933
[2025-05-13 16:50:46] [INFO] 用户 谢羚羚 月份 12 更新盈亏值: 6265
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 已存在，检查月度数据
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 2 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 4 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 3 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 8 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 9 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 11 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 12 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 3 更新盈亏值: -8600
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 5 更新盈亏值: 1865
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 8 更新盈亏值: 2979
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 9 更新盈亏值: -7068
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 11 更新盈亏值: 5700
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 10 更新盈亏值: -2072
[2025-05-13 16:50:46] [INFO] 用户 景丝丝 月份 12 更新盈亏值: -4645
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 已存在，检查月度数据
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 月份 1 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 月份 2 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 月份 6 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 月份 9 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 月份 11 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 月份 12 现有盈亏值: 0
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 月份 7 更新盈亏值: 1948
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 月份 10 更新盈亏值: -4022
[2025-05-13 16:50:46] [INFO] 用户 钟鹮鹮 月份 12 更新盈亏值: -944
[2025-05-13 16:50:46] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-13 16:50:46] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:50:46] [INFO] 验证用户 谢羚羚 数据:
[2025-05-13 16:50:46] [INFO]   月份 1: -6864
[2025-05-13 16:50:46] [INFO]   月份 4: -5013
[2025-05-13 16:50:46] [INFO]   月份 5: -324
[2025-05-13 16:50:46] [INFO]   月份 6: 7573
[2025-05-13 16:50:46] [INFO]   月份 7: -440
[2025-05-13 16:50:46] [INFO]   月份 8: 9933
[2025-05-13 16:50:46] [INFO]   月份 10: -7683
[2025-05-13 16:50:46] [INFO]   月份 11: -8122
[2025-05-13 16:50:46] [INFO] 验证用户 景丝丝 数据:
[2025-05-13 16:50:46] [INFO]   月份 1: 7718
[2025-05-13 16:50:46] [INFO]   月份 4: -4578
[2025-05-13 16:50:46] [INFO]   月份 5: 1865
[2025-05-13 16:50:46] [INFO]   月份 6: -7944
[2025-05-13 16:50:46] [INFO]   月份 8: 2979
[2025-05-13 16:50:46] [INFO]   月份 9: -7068
[2025-05-13 16:50:46] [INFO]   月份 10: -2072
[2025-05-13 16:50:46] [INFO] 验证用户 钟鹮鹮 数据:
[2025-05-13 16:50:46] [INFO]   月份 3: -8022
[2025-05-13 16:50:46] [INFO]   月份 4: -4680
[2025-05-13 16:50:46] [INFO]   月份 5: 9446
[2025-05-13 16:50:46] [INFO]   月份 6: -8830
[2025-05-13 16:50:46] [INFO]   月份 8: 3651
[2025-05-13 16:50:46] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=-324, 景丝丝=1865, 钟鹮鹮=9446
[2025-05-13 16:50:46] [INFO] 三人当月总盈亏数据: 谢羚羚=-324, 景丝丝=1865, 钟鹮鹮=9446
[2025-05-13 16:51:09] [INFO] 开始创建三人盈亏测试数据
[2025-05-13 16:51:09] [INFO] 现有用户: xiekun
[2025-05-13 16:51:09] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:51:09] [INFO] 现有用户: 景丝丝
[2025-05-13 16:51:09] [INFO] 当前CSV文件中有 4 条用户数据
[2025-05-13 16:51:09] [INFO] 现有用户: 谢羚羚
[2025-05-13 16:51:09] [INFO] 现有用户: 钟鹮鹮
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 1 现有盈亏值: -6864
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 已存在，检查月度数据
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 3 现有盈亏值: -3085
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 4 现有盈亏值: -5013
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 2 现有盈亏值: 4807
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 6 现有盈亏值: 7573
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 5 现有盈亏值: -324
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 7 现有盈亏值: -440
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 9 现有盈亏值: 4239
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 10 现有盈亏值: -7683
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 11 现有盈亏值: -8122
[2025-05-13 16:51:09] [INFO] 用户 景丝丝 月份 4 现有盈亏值: -4578
[2025-05-13 16:51:09] [INFO] 用户 景丝丝 月份 11 现有盈亏值: 5700
[2025-05-13 16:51:09] [INFO] 用户 钟鹮鹮 月份 6 现有盈亏值: -8830
[2025-05-13 16:51:09] [INFO] 用户 钟鹮鹮 月份 8 现有盈亏值: 3651
[2025-05-13 16:51:09] [INFO] 用户 钟鹮鹮 月份 12 现有盈亏值: -944
[2025-05-13 16:51:09] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:51:09] [INFO] 验证用户 谢羚羚 数据:
[2025-05-13 16:51:09] [INFO]   月份 1: -6864
[2025-05-13 16:51:09] [INFO]   月份 2: 4807
[2025-05-13 16:51:09] [INFO]   月份 4: -5013
[2025-05-13 16:51:09] [INFO]   月份 6: 7573
[2025-05-13 16:51:09] [INFO]   月份 8: 9933
[2025-05-13 16:51:09] [INFO]   月份 11: -8122
[2025-05-13 16:51:09] [INFO]   月份 12: 6265
[2025-05-13 16:51:09] [INFO]   月份 1: 7718
[2025-05-13 16:51:09] [INFO]   月份 9: -7068
[2025-05-13 16:51:09] [INFO]   月份 10: -2072
[2025-05-13 16:51:09] [INFO]   月份 11: 5700
[2025-05-13 16:51:09] [INFO]   月份 12: -4645
[2025-05-13 16:51:09] [INFO] 验证用户 钟鹮鹮 数据:
[2025-05-13 16:51:09] [INFO]   月份 1: 2869
[2025-05-13 16:51:09] [INFO]   月份 2: 2138
[2025-05-13 16:51:09] [INFO]   月份 7: 1948
[2025-05-13 16:51:09] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:51:09] [INFO] 用户 谢羚羚 月份 5 盈亏值: -324
[2025-05-13 16:51:12] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:51:12] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 16:51:17] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:51:17] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-13 16:51:19] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:51:19] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-13 16:51:21] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:51:21] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 16:51:26] [INFO] 开始创建三人盈亏测试数据
[2025-05-13 16:51:26] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:51:26] [INFO] 当前CSV文件中有 4 条用户数据
[2025-05-13 16:51:26] [INFO] 现有用户: 景丝丝
[2025-05-13 16:51:26] [INFO] 用户 景丝丝 月份 4 现有盈亏值: -4578
[2025-05-13 16:51:26] [INFO] 用户 景丝丝 月份 6 现有盈亏值: -7944
[2025-05-13 16:51:26] [INFO] 用户 景丝丝 月份 10 现有盈亏值: -2072
[2025-05-13 16:51:26] [INFO] 用户 景丝丝 月份 12 现有盈亏值: -4645
[2025-05-13 16:51:26] [INFO] 用户 钟鹮鹮 月份 1 现有盈亏值: 2869
[2025-05-13 16:51:26] [INFO] 用户 钟鹮鹮 已存在，检查月度数据
[2025-05-13 16:51:26] [INFO] 用户 钟鹮鹮 月份 3 现有盈亏值: -8022
[2025-05-13 16:51:26] [INFO] 用户 钟鹮鹮 月份 4 现有盈亏值: -4680
[2025-05-13 16:51:26] [INFO] 用户 钟鹮鹮 月份 2 现有盈亏值: 2138
[2025-05-13 16:51:26] [INFO] 用户 钟鹮鹮 月份 6 现有盈亏值: -8830
[2025-05-13 16:51:26] [INFO] 用户 钟鹮鹮 月份 7 现有盈亏值: 1948
[2025-05-13 16:51:26] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:51:26] [INFO]   月份 6: 7573
[2025-05-13 16:51:26] [INFO]   月份 8: 9933
[2025-05-13 16:51:26] [INFO]   月份 11: -8122
[2025-05-13 16:51:26] [INFO]   月份 12: 6265
[2025-05-13 16:51:26] [INFO]   月份 4: -4578
[2025-05-13 16:51:26] [INFO]   月份 3: -8600
[2025-05-13 16:51:26] [INFO]   月份 5: 1865
[2025-05-13 16:51:26] [INFO]   月份 6: -7944
[2025-05-13 16:51:26] [INFO]   月份 7: -7781
[2025-05-13 16:51:26] [INFO]   月份 8: 2979
[2025-05-13 16:51:26] [INFO]   月份 9: -7068
[2025-05-13 16:51:26] [INFO]   月份 10: -2072
[2025-05-13 16:51:26] [INFO]   月份 12: -4645
[2025-05-13 16:51:26] [INFO]   月份 2: 2138
[2025-05-13 16:51:26] [INFO]   月份 7: 1948
[2025-05-13 16:51:26] [INFO]   月份 11: 4141
[2025-05-13 16:51:26] [INFO]   月份 12: -944
[2025-05-13 16:51:26] [INFO] 三人当月总盈亏数据: 谢羚羚=-324, 景丝丝=1865, 钟鹮鹮=9446
[2025-05-13 16:52:17] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:52:17] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 16:52:22] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:52:22] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
=== 日志开始于 2025/5/13 16:54:41 ===
[2025-05-13 16:54:41] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:54:45] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:54:45] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 16:54:47] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 16:54:47] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:54:47] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 16:54:47] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:54:47] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 16:54:47] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 16:54:47] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 16:54:47] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 16:54:47] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 16:54:47] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 16:54:47] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 16:55:18] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:55:18] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 16:55:23] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:55:23] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-13 16:55:27] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 16:55:27] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:55:27] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 16:55:27] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:55:27] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 16:55:27] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 16:55:27] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 16:55:27] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 16:55:27] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 16:55:47] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:55:47] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 16:57:38] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 16:57:38] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:57:38] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 16:57:38] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:57:38] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 16:57:38] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 16:57:38] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 16:58:18] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-05-13 16:58:18] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-13 16:58:18] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-13 16:58:18] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-05-13 16:58:18] [INFO] 发送HTTP请求...
[2025-05-13 16:58:18] [INFO] HTTP请求成功，响应长度: 1028
[2025-05-13 16:58:18] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-05-13 16:58:18] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-13 16:58:18] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-05-13 16:58:18] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-13 16:58:18] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-13 16:58:18] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-05-13 16:58:18] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-05-13 16:58:18] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-13 16:58:18] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-05-13 16:58:18] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-05-13 16:58:18] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-05-13 16:58:18] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-05-13 16:58:18] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-05-13 16:58:18] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-05-13 16:58:18] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-05-13 16:58:25] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:58:25] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 16:58:33] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:58:33] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 16:58:34] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 16:58:34] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 16:58:34] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 16:58:34] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 16:58:34] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 16:58:34] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 16:58:34] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 16:58:34] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
=== 日志开始于 2025/5/13 18:25:30 ===
[2025-05-13 18:25:30] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:25:37] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:25:37] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 18:25:53] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 18:25:53] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
=== 日志开始于 2025/5/13 18:31:02 ===
[2025-05-13 18:31:02] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:31:15] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:31:15] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 18:31:28] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 18:31:28] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
=== 日志开始于 2025/5/13 18:33:47 ===
[2025-05-13 18:33:47] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:33:50] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:33:50] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 18:33:52] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 18:33:52] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-13 18:34:12] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 18:34:12] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-13 18:36:07] [ERROR] 加载月度盈亏数据失败: The process cannot access the file 'D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv' because it is being used by another process.
[2025-05-13 18:36:07] [ERROR] 保存月度盈亏数据失败: The process cannot access the file 'D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv' because it is being used by another process.
[2025-05-13 18:36:07] [INFO] 已加载用户 xiekun 的月度盈亏数据
=== 日志开始于 2025/5/13 18:36:26 ===
[2025-05-13 18:36:26] [ERROR] 加载月度盈亏数据失败: The process cannot access the file 'D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv' because it is being used by another process.
[2025-05-13 18:36:26] [ERROR] 保存月度盈亏数据失败: The process cannot access the file 'D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv' because it is being used by another process.
[2025-05-13 18:36:27] [ERROR] 加载月度盈亏数据失败: The process cannot access the file 'D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv' because it is being used by another process.
[2025-05-13 18:36:27] [ERROR] 保存月度盈亏数据失败: The process cannot access the file 'D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv' because it is being used by another process.
[2025-05-13 18:36:27] [INFO] 已加载用户 xiekun 的月度盈亏数据
=== 日志开始于 2025/5/13 18:36:42 ===
[2025-05-13 18:36:42] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:36:44] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:36:44] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 18:37:04] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:37:04] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-13 18:37:31] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:37:31] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 18:37:38] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 18:37:38] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:37:38] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 18:37:38] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 18:37:38] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 18:37:38] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 18:37:38] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 18:37:38] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 18:37:38] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 18:37:38] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 18:37:45] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-13 18:37:45] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:37:45] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 18:37:45] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 18:37:45] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 18:37:45] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 18:37:45] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 18:37:48] [INFO] 加载统计数据: 用户=景丝丝, 年份=2025, 月份=5
[2025-05-13 18:37:48] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:37:48] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 18:37:48] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 18:37:48] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 18:37:48] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 18:37:48] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 18:37:50] [INFO] 加载统计数据: 用户=钟鹮鹮, 年份=2025, 月份=5
[2025-05-13 18:37:50] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:37:50] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 18:37:50] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 18:37:50] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 18:37:50] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 18:37:54] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:37:54] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 18:37:54] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 18:37:54] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 18:37:54] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 18:37:54] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 18:37:54] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 18:37:54] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
=== 日志开始于 2025/5/13 18:48:26 ===
[2025-05-13 18:48:26] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:48:36] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 18:48:36] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 18:48:42] [INFO] 年度盈亏数据文件不存在: D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:48:42] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:48:42] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:48:51] [INFO] 成功加载年度盈亏数据，共 1 条记录
[2025-05-13 18:48:51] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:48:51] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:49:56] [INFO] 成功加载年度盈亏数据，共 2 条记录
[2025-05-13 18:50:00] [INFO] 成功加载年度盈亏数据，共 2 条记录
[2025-05-13 18:50:00] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:50:02] [INFO] 成功加载年度盈亏数据，共 2 条记录
[2025-05-13 18:50:02] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:50:04] [INFO] 成功加载年度盈亏数据，共 2 条记录
[2025-05-13 18:50:04] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:50:04] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:52:22] [INFO] 成功加载年度盈亏数据，共 3 条记录
[2025-05-13 18:52:22] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:52:26] [INFO] 成功加载年度盈亏数据，共 3 条记录
[2025-05-13 18:52:26] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:52:28] [INFO] 成功加载年度盈亏数据，共 3 条记录
[2025-05-13 18:52:28] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:52:30] [INFO] 成功加载年度盈亏数据，共 3 条记录
[2025-05-13 18:52:30] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:52:30] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:53:34] [INFO] 成功加载年度盈亏数据，共 4 条记录
[2025-05-13 18:53:34] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:53:37] [INFO] 成功加载年度盈亏数据，共 4 条记录
[2025-05-13 18:53:37] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:53:39] [INFO] 成功加载年度盈亏数据，共 4 条记录
[2025-05-13 18:53:39] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:53:43] [INFO] 成功加载年度盈亏数据，共 4 条记录
[2025-05-13 18:53:43] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:53:44] [INFO] 成功加载年度盈亏数据，共 4 条记录
[2025-05-13 18:53:44] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:53:44] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:54:13] [INFO] 成功加载年度盈亏数据，共 5 条记录
[2025-05-13 18:54:13] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:54:17] [INFO] 成功加载年度盈亏数据，共 5 条记录
[2025-05-13 18:54:17] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:54:59] [INFO] 成功加载年度盈亏数据，共 5 条记录
[2025-05-13 18:54:59] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:55:03] [INFO] 成功加载年度盈亏数据，共 5 条记录
[2025-05-13 18:55:03] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:55:05] [INFO] 成功加载年度盈亏数据，共 5 条记录
[2025-05-13 18:55:05] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:56:04] [INFO] 成功加载年度盈亏数据，共 6 条记录
[2025-05-13 18:56:04] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:56:07] [INFO] 成功加载年度盈亏数据，共 6 条记录
[2025-05-13 18:56:07] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:56:12] [INFO] 成功加载年度盈亏数据，共 6 条记录
[2025-05-13 18:56:12] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:56:12] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:57:17] [INFO] 成功加载年度盈亏数据，共 7 条记录
[2025-05-13 18:57:17] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:57:20] [INFO] 成功加载年度盈亏数据，共 7 条记录
[2025-05-13 18:57:20] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:57:23] [INFO] 成功加载年度盈亏数据，共 7 条记录
[2025-05-13 18:57:23] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:57:23] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:58:15] [INFO] 成功加载年度盈亏数据，共 8 条记录
[2025-05-13 18:58:15] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:58:17] [INFO] 成功加载年度盈亏数据，共 8 条记录
[2025-05-13 18:58:17] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:58:19] [INFO] 成功加载年度盈亏数据，共 8 条记录
[2025-05-13 18:58:19] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 18:58:19] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:59:07] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 18:59:07] [INFO] 成功保存年度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 18:59:19] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 18:59:19] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 19:00:05] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:00:07] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:00:07] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 19:00:09] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:00:09] [INFO] 已加载用户 xiekun 的年度盈亏数据
=== 日志开始于 2025/5/13 19:01:35 ===
[2025-05-13 19:01:35] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:01:37] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:01:37] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 19:01:40] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 19:01:40] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:01:40] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 19:01:40] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 19:01:40] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 19:01:40] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 19:01:40] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 19:01:40] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 19:01:40] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:01:40] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:01:40] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:01:45] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 19:01:45] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:01:45] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 19:01:45] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 19:01:45] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 19:01:45] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 19:01:45] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 19:01:45] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 19:01:45] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:01:45] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:01:45] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
=== 日志开始于 2025/5/13 19:05:45 ===
[2025-05-13 19:05:45] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:05:47] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:05:47] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 19:05:50] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 19:05:50] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:05:50] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 19:05:50] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 19:05:50] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 19:05:50] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 19:05:50] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 19:05:50] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:05:50] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:05:50] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-13 19:05:50] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:05:50] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-13 19:05:50] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-13 19:05:50] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
=== 日志开始于 2025/5/13 19:10:43 ===
[2025-05-13 19:10:43] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:10:48] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:10:48] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 19:10:50] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 19:10:50] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:10:50] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 19:10:50] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 19:10:50] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 19:10:50] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 19:10:50] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 19:10:50] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 19:10:50] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:10:50] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:10:50] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-13 19:10:50] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:10:50] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-13 19:10:50] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-13 19:10:50] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:12:41] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:12:41] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 19:12:45] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:12:45] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 19:12:47] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:12:47] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 19:12:49] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:12:49] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 19:12:51] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:12:51] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 19:12:56] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:12:56] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 19:12:58] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:12:58] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-13 19:13:00] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 19:13:00] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:13:00] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 19:13:00] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 19:13:00] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 19:13:00] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:13:00] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 19:13:00] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:13:00] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-13 19:13:00] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:13:00] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-13 19:13:00] [INFO] 成功显示用户 xiekun 的多年汇总数据
=== 日志开始于 2025/5/13 19:14:05 ===
[2025-05-13 19:14:05] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:14:07] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:14:07] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 19:14:08] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 19:14:08] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:14:08] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 19:14:08] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 19:14:08] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 19:14:08] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 19:14:08] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 19:14:08] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:14:08] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:14:08] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-13 19:14:08] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:14:08] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-13 19:14:08] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-13 19:14:08] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
=== 日志开始于 2025/5/13 19:19:55 ===
[2025-05-13 19:19:55] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:19:58] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:19:58] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 19:20:00] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-13 19:20:00] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 19:20:00] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 19:20:00] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 19:20:00] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 19:20:00] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 19:20:00] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 19:20:00] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:20:00] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 19:20:00] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-13 19:20:00] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:20:00] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-13 19:20:00] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-13 19:20:00] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 19:20:00] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-13 19:20:00] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
=== 日志开始于 2025/5/13 星期二 22:26:59 ===
[2025-05-13 22:26:59] [INFO] 成功加载月度盈亏数据，共 4 条记录
[2025-05-13 22:26:59] [INFO] 成功保存月度盈亏数据到 D:\src\StockCrawler0513new\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-13 22:27:13] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:27:14] [INFO] 已加载用户 Administrator 的月度盈亏数据
[2025-05-13 22:27:25] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:27:25] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 22:27:39] [INFO] 成功加载年度盈亏数据，共 9 条记录
[2025-05-13 22:27:39] [INFO] 成功保存年度盈亏数据到 D:\src\StockCrawler0513new\bin\Debug\net9.0-windows\yearly_profits.csv
[2025-05-13 22:27:39] [INFO] 已加载用户 Administrator 的年度盈亏数据
[2025-05-13 22:27:57] [INFO] 加载统计数据: 用户=Administrator, 年份=2025, 月份=5
[2025-05-13 22:27:57] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:27:57] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:27:57] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 22:27:57] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 22:27:57] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 22:27:57] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 22:27:57] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 22:27:57] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:27:57] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:27:57] [INFO] 加载用户 Administrator 的多年汇总数据
[2025-05-13 22:27:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:27:57] [INFO] 成功计算用户 Administrator 的汇总数据: 总盈利=0, 总转入=0, 总转出=0, 净转入转出=0
[2025-05-13 22:27:57] [INFO] 成功显示用户 Administrator 的多年汇总数据
[2025-05-13 22:27:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:27:57] [INFO] 加载年度盈亏数据: 用户=Administrator, 年份数量=9
[2025-05-13 22:27:57] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:07] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-13 22:28:07] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:28:07] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:28:07] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 22:28:07] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 22:28:07] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 22:28:07] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 22:28:07] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 22:28:07] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:07] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:07] [INFO] 加载用户 谢羚羚 的多年汇总数据
[2025-05-13 22:28:07] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:28:07] [INFO] 未找到用户 谢羚羚 的年度盈亏数据
[2025-05-13 22:28:07] [INFO] 成功显示用户 谢羚羚 的多年汇总数据
[2025-05-13 22:28:07] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:28:07] [INFO] 加载年度盈亏数据: 用户=谢羚羚, 年份数量=9
[2025-05-13 22:28:07] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:13] [INFO] 加载统计数据: 用户=景丝丝, 年份=2025, 月份=5
[2025-05-13 22:28:13] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:28:13] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:28:13] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 22:28:13] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 22:28:13] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 22:28:13] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 22:28:13] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 22:28:13] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:13] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:13] [INFO] 加载用户 景丝丝 的多年汇总数据
[2025-05-13 22:28:13] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:28:13] [INFO] 未找到用户 景丝丝 的年度盈亏数据
[2025-05-13 22:28:13] [INFO] 成功显示用户 景丝丝 的多年汇总数据
[2025-05-13 22:28:13] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:28:13] [INFO] 加载年度盈亏数据: 用户=景丝丝, 年份数量=9
[2025-05-13 22:28:13] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:16] [INFO] 加载统计数据: 用户=钟鹮鹮, 年份=2025, 月份=5
[2025-05-13 22:28:16] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:28:16] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:28:16] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 22:28:16] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 22:28:16] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 22:28:16] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 22:28:16] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 22:28:16] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:16] [INFO] 加载用户 钟鹮鹮 的多年汇总数据
[2025-05-13 22:28:16] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:28:16] [INFO] 未找到用户 钟鹮鹮 的年度盈亏数据
[2025-05-13 22:28:16] [INFO] 成功显示用户 钟鹮鹮 的多年汇总数据
[2025-05-13 22:28:16] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:28:16] [INFO] 加载年度盈亏数据: 用户=钟鹮鹮, 年份数量=9
[2025-05-13 22:28:16] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:16] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:28:53] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:28:53] [INFO] 已加载用户 Administrator 的月度盈亏数据
[2025-05-13 22:28:57] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:28:57] [INFO] 已加载用户 Administrator 的月度盈亏数据
[2025-05-13 22:29:07] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:29:07] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 22:29:11] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:29:11] [INFO] 已加载用户 Administrator 的年度盈亏数据
=== 日志开始于 2025/5/13 星期二 22:37:34 ===
[2025-05-13 22:37:34] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:37:39] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:37:39] [INFO] 已加载用户 Administrator 的月度盈亏数据
=== 日志开始于 2025/5/13 星期二 22:39:53 ===
[2025-05-13 22:39:53] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:39:56] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:39:56] [INFO] 已加载用户 Administrator 的月度盈亏数据
=== 日志开始于 2025/5/13 星期二 22:49:28 ===
[2025-05-13 22:49:28] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:49:39] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:49:39] [INFO] 已加载用户 Administrator 的月度盈亏数据
[2025-05-13 22:50:01] [INFO] 加载统计数据: 用户=Administrator, 年份=2025, 月份=5
[2025-05-13 22:50:01] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:50:01] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:50:01] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 22:50:01] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 22:50:01] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 22:50:01] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 22:50:01] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:50:01] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:50:01] [INFO] 加载用户 Administrator 的多年汇总数据
[2025-05-13 22:50:01] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:50:01] [INFO] 成功计算用户 Administrator 的汇总数据: 总盈利=0, 总转入=0, 总转出=0, 净转入转出=0
[2025-05-13 22:50:01] [INFO] 成功显示用户 Administrator 的多年汇总数据
[2025-05-13 22:50:01] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:50:01] [INFO] 加载年度盈亏数据: 用户=Administrator, 年份数量=9
[2025-05-13 22:50:01] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:50:09] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-13 22:50:09] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:50:09] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:50:09] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 22:50:09] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 22:50:09] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 22:50:09] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 22:50:09] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 22:50:09] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:50:09] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:50:09] [INFO] 加载用户 谢羚羚 的多年汇总数据
[2025-05-13 22:50:09] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:50:09] [INFO] 未找到用户 谢羚羚 的年度盈亏数据
[2025-05-13 22:50:09] [INFO] 成功显示用户 谢羚羚 的多年汇总数据
[2025-05-13 22:50:09] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:50:09] [INFO] 加载年度盈亏数据: 用户=谢羚羚, 年份数量=9
[2025-05-13 22:50:09] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:50:29] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:50:29] [INFO] 已加载用户 Administrator 的年度盈亏数据
=== 日志开始于 2025/5/13 星期二 22:53:13 ===
[2025-05-13 22:53:13] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:53:16] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:53:16] [INFO] 已加载用户 Administrator 的月度盈亏数据
[2025-05-13 22:53:30] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:53:30] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-13 22:53:49] [INFO] 加载统计数据: 用户=Administrator, 年份=2025, 月份=5
[2025-05-13 22:53:49] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:53:49] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:53:49] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 22:53:49] [INFO] 加载到 3 条每日盈亏数据
[2025-05-13 22:53:49] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 22:53:49] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 22:53:49] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 22:53:49] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:53:49] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:53:49] [INFO] 加载用户 Administrator 的多年汇总数据
[2025-05-13 22:53:49] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:53:49] [INFO] 成功计算用户 Administrator 的汇总数据: 总盈利=0, 总转入=0, 总转出=0, 净转入转出=0
[2025-05-13 22:53:49] [INFO] 成功显示用户 Administrator 的多年汇总数据
[2025-05-13 22:53:49] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:53:49] [INFO] 加载年度盈亏数据: 用户=Administrator, 年份数量=9
[2025-05-13 22:53:49] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:55:38] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-13 22:55:38] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:55:38] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:55:38] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 22:55:38] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 22:55:38] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:55:38] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:55:38] [INFO] 加载用户 谢羚羚 的多年汇总数据
[2025-05-13 22:55:38] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:55:38] [INFO] 未找到用户 谢羚羚 的年度盈亏数据
[2025-05-13 22:55:38] [INFO] 成功显示用户 谢羚羚 的多年汇总数据
[2025-05-13 22:55:38] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:55:38] [INFO] 加载年度盈亏数据: 用户=谢羚羚, 年份数量=9
[2025-05-13 22:55:38] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:55:55] [INFO] 加载统计数据: 用户=Administrator, 年份=2025, 月份=5
[2025-05-13 22:55:55] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:55:55] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:55:55] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-13 22:55:55] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-13 22:55:55] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 22:55:55] [INFO] 加载用户 Administrator 的多年汇总数据
[2025-05-13 22:55:55] [INFO] 成功显示用户 Administrator 的多年汇总数据
[2025-05-13 22:55:55] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:55:55] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:55:55] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:55:57] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-13 22:55:57] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-13 22:55:57] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-13 22:55:57] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-13 22:55:57] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-13 22:55:57] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-13 22:55:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:55:57] [INFO] 未找到用户 谢羚羚 的年度盈亏数据
[2025-05-13 22:55:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-13 22:55:57] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
=== 日志开始于 2025/5/14 8:47:01 ===
[2025-05-14 08:47:01] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:47:02] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:47:02] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 08:47:09] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:47:09] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 08:47:13] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:47:13] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 08:47:16] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:47:16] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 08:47:22] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:47:22] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 08:47:28] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:47:28] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 08:47:34] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 08:47:34] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:47:34] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:47:34] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:47:34] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:47:34] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:47:34] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-14 08:47:34] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 08:47:34] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:47:34] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:47:34] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 08:47:34] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:47:34] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 08:47:34] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 08:47:34] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:47:34] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 08:47:34] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:48:47] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:48:47] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
=== 日志开始于 2025/5/14 8:51:21 ===
[2025-05-14 08:51:21] [INFO] 成功加载月度盈亏数据，共 5 条记录
=== 日志开始于 2025/5/14 8:55:59 ===
[2025-05-14 08:55:59] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:56:01] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:56:01] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 08:56:03] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 08:56:03] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:56:03] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:56:03] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:56:03] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:56:03] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:56:03] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-14 08:56:03] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 08:56:03] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:56:03] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:56:03] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 08:56:03] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:56:03] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 08:56:03] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 08:56:03] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:56:03] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 08:56:03] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
=== 日志开始于 2025/5/14 8:57:19 ===
[2025-05-14 08:57:19] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:20] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:20] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 08:57:22] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 08:57:22] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:22] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:57:22] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:57:22] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:57:22] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:57:22] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-14 08:57:22] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 08:57:22] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:22] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:22] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 08:57:22] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:57:22] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 08:57:22] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 08:57:22] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:57:22] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 08:57:22] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:31] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 08:57:31] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:31] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:57:31] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:57:31] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:57:31] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 08:57:31] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:57:31] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:31] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 08:57:31] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:31] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:57:31] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 08:57:31] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:57:31] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:38] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:38] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 08:57:40] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 08:57:40] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:40] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:57:40] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:57:40] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:57:40] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:57:40] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-14 08:57:40] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 08:57:40] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 08:57:40] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:57:40] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:44] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-14 08:57:44] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:44] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:57:44] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:57:44] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:57:44] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:57:44] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 08:57:44] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-14 08:57:44] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:44] [INFO] 加载用户 谢羚羚 的多年汇总数据
[2025-05-14 08:57:44] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:57:44] [INFO] 成功显示用户 谢羚羚 的多年汇总数据
[2025-05-14 08:57:44] [INFO] 未找到用户 谢羚羚 的年度盈亏数据
[2025-05-14 08:57:44] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:47] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 08:57:47] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:47] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:57:47] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:57:47] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:57:47] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:57:47] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:47] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 08:57:47] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:47] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 08:57:53] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-14 08:57:53] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:53] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:57:53] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:57:53] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:57:53] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:57:53] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-14 08:57:53] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 08:57:53] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:53] [INFO] 加载用户 谢羚羚 的多年汇总数据
[2025-05-14 08:57:53] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 08:57:53] [INFO] 未找到用户 谢羚羚 的年度盈亏数据
[2025-05-14 08:57:53] [INFO] 加载年度盈亏数据: 用户=谢羚羚, 年份数量=9
[2025-05-14 08:57:56] [INFO] 加载统计数据: 用户=钟鹮鹮, 年份=2025, 月份=5
[2025-05-14 08:57:56] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:56] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:57:56] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:57:56] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:57:56] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:57:56] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:56] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:56] [INFO] 未找到用户 钟鹮鹮 的年度盈亏数据
[2025-05-14 08:57:56] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:57:58] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-14 08:57:58] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:57:58] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:57:58] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:57:58] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:57:58] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:57:58] [INFO] 未找到用户 谢羚羚 的年度盈亏数据
[2025-05-14 08:57:58] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 08:58:01] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 08:58:01] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 08:58:01] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 08:58:01] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 08:58:01] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 08:58:01] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 08:58:01] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 08:58:01] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-14 08:58:01] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 08:58:01] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 08:58:01] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
=== 日志开始于 2025/5/14 9:00:31 ===
[2025-05-14 09:00:31] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:00:57] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:00:57] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 09:01:04] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 09:01:04] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 09:01:17] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 09:01:17] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 09:01:17] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 09:01:20] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 09:01:20] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 09:01:20] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 09:01:23] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 09:01:23] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 09:01:23] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 09:01:27] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 09:01:27] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 09:01:27] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 09:01:30] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 09:01:30] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 09:02:02] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:02:02] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 09:02:23] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:02:23] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-14 09:03:18] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:03:18] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-14 09:03:19] [INFO] 月度盈亏设置已更新
[2025-05-14 09:03:19] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:03:22] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:03:22] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 09:03:40] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:03:45] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:03:45] [INFO] 已加载用户 xiekun 的月度盈亏数据
=== 日志开始于 2025/5/14 9:22:54 ===
[2025-05-14 09:22:54] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:23:13] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:23:13] [INFO] 已加载用户 xiekun 的月度盈亏数据
=== 日志开始于 2025/5/14 9:32:29 ===
[2025-05-14 09:32:29] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:32:29] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 09:32:33] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 09:32:33] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:32:33] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 09:32:33] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 09:32:33] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 09:32:33] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 09:32:33] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-14 09:32:33] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 09:32:33] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 09:32:33] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 09:32:33] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 09:32:33] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 09:32:33] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 09:32:33] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 09:32:33] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 09:32:33] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 09:32:33] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 09:32:38] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 9:32:58 ===
[2025-05-14 09:32:58] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:32:58] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 09:33:09] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 09:33:09] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 09:33:09] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 09:33:09] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 09:33:09] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 09:33:09] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4309.00
[2025-05-14 09:33:09] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3003.00
[2025-05-14 09:33:09] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6123.00
[2025-05-14 09:33:09] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 09:33:09] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 09:33:09] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 09:33:09] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 09:33:09] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 09:33:09] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 09:33:09] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 09:33:09] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 09:33:09] [INFO] 三人当月总盈亏数据: 谢羚羚=4309.00, 景丝丝=-3003.00, 钟鹮鹮=6123.00
[2025-05-14 09:33:12] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 09:33:12] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 09:33:29] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 10:09:09 ===
[2025-05-14 10:09:09] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 10:09:09] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 10:09:13] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 10:09:13] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 10:09:16] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 11:22:33 ===
[2025-05-14 11:22:33] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:22:33] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 11:23:39] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 11:26:44 ===
[2025-05-14 11:26:44] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:26:44] [INFO] 已加载 5 个用户
[2025-05-14 11:26:44] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:26:44] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 11:26:48] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:26:48] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-14 11:26:49] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:26:52] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:26:52] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-14 11:26:55] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:26:55] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-14 11:26:57] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:26:57] [INFO] 已加载用户 Administrator 的月度盈亏数据
[2025-05-14 11:27:01] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:27:01] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-14 11:27:03] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:27:03] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-14 11:27:11] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:27:14] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:27:19] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 11:27:19] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 11:27:30] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 11:34:04 ===
[2025-05-14 11:34:04] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:34:04] [INFO] 已加载 5 个用户
[2025-05-14 11:34:04] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:34:04] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 11:34:09] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:34:09] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-14 11:34:10] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 11:34:10] [INFO] 已加载 2 个用户
[2025-05-14 11:34:10] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 11:34:10] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 11:34:13] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 11:34:13] [INFO] 已加载用户 Administrator 的年度盈亏数据
[2025-05-14 11:34:16] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 11:34:16] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 11:34:18] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 11:34:18] [INFO] 已加载用户 Administrator 的年度盈亏数据
[2025-05-14 11:34:22] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 11:34:22] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 11:34:32] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 11:45:23 ===
[2025-05-14 11:45:23] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:45:23] [INFO] 已加载 5 个用户
[2025-05-14 11:45:23] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:45:23] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 11:45:26] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:45:26] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-14 11:45:34] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:45:34] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-14 11:45:37] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:45:37] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-14 11:45:47] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 11:45:49] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 11:45:49] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 11:46:00] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 11:46:00] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 11:46:04] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 11:46:04] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 11:46:24] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 13:02:53 ===
[2025-05-14 13:02:53] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:02:53] [INFO] 已加载 5 个用户
[2025-05-14 13:02:53] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:02:53] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 13:02:56] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:02:56] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 13:03:00] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:03:00] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-14 13:03:13] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:03:13] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 13:03:18] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:03:18] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 13:03:20] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:03:20] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-14 13:03:31] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:03:31] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 13:03:34] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:03:34] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 13:03:38] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:03:38] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-14 13:03:42] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:03:42] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-14 13:03:45] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:03:45] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-14 13:03:48] [INFO] 月度盈亏设置已更新
[2025-05-14 13:06:21] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:06:21] [INFO] 已加载 5 个用户
[2025-05-14 13:06:21] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:06:21] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 13:06:25] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:06:25] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-14 13:06:28] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:06:28] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-14 13:06:31] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:06:31] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-14 13:07:01] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:07:01] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-14 13:07:02] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 13:15:20 ===
[2025-05-14 13:15:20] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:15:20] [INFO] 已加载 5 个用户
[2025-05-14 13:15:20] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:15:20] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 13:15:22] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 13:15:22] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:15:22] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 13:15:22] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:15:22] [INFO] 加载到 3 条每日盈亏数据
[2025-05-14 13:15:22] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3596.00
[2025-05-14 13:15:22] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 5707.00
[2025-05-14 13:15:22] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2081.00
[2025-05-14 13:15:22] [INFO] 三人当月总盈亏数据: 谢羚羚=3596.00, 景丝丝=-2081.00, 钟鹮鹮=5707.00
[2025-05-14 13:15:22] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=3596.00, 景丝丝=-2081.00, 钟鹮鹮=5707.00
[2025-05-14 13:15:22] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 13:15:22] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 13:15:22] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 13:15:22] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 13:15:22] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 13:15:22] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 13:15:22] [INFO] 三人当月总盈亏数据: 谢羚羚=3596.00, 景丝丝=-2081.00, 钟鹮鹮=5707.00
[2025-05-14 13:15:52] [INFO] 月度盈亏设置已更新
[2025-05-14 13:15:55] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:15:55] [INFO] 已加载 5 个用户
[2025-05-14 13:15:55] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 13:15:55] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 13:15:56] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:15:56] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 13:15:59] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:15:59] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-14 13:16:01] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:16:01] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-14 13:16:05] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 13:16:05] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 13:16:08] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 14:34:07 ===
[2025-05-14 14:34:07] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 14:34:07] [INFO] 已加载 5 个用户
[2025-05-14 14:34:07] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 14:34:07] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 14:34:16] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 14:34:16] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-14 14:34:18] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 14:34:18] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-14 14:34:21] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 14:34:21] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-14 14:34:23] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 14:34:23] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-14 14:34:26] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 15:01:23 ===
[2025-05-14 15:01:23] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:01:23] [INFO] 已加载 5 个用户
[2025-05-14 15:01:23] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:01:23] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 15:01:25] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:01:25] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:01:32] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:01:32] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 15:01:35] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:01:35] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:01:45] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:01:45] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:01:48] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:01:48] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-14 15:01:50] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:01:50] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-14 15:01:52] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:01:52] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:01:55] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 15:02:56 ===
[2025-05-14 15:02:56] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:02:56] [INFO] 已加载 5 个用户
[2025-05-14 15:02:56] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:02:56] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 15:02:58] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:02:58] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:03:01] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:03:01] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-14 15:03:08] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:03:08] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 15:03:11] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 15:11:19 ===
[2025-05-14 15:11:19] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:11:19] [INFO] 已加载 5 个用户
[2025-05-14 15:11:19] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:11:19] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 15:11:22] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:11:22] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:11:25] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:11:25] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-14 15:11:31] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:11:31] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 15:11:34] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 15:47:02 ===
[2025-05-14 15:47:02] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:47:02] [INFO] 已加载 5 个用户
[2025-05-14 15:47:02] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:47:02] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 15:47:10] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:47:10] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:49:48] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 15:50:33 ===
[2025-05-14 15:50:33] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:50:33] [INFO] 已加载 5 个用户
[2025-05-14 15:50:33] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:50:33] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 15:50:38] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:50:38] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:50:40] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:50:40] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-14 15:50:42] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:50:42] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-14 15:50:45] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:50:45] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 15:50:45] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-14 15:50:57] [INFO] 成功加载每日盈亏数据，共 3 条记录
[2025-05-14 15:50:57] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-14 15:51:11] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:51:11] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-14 15:51:16] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:51:16] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-14 15:51:19] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:51:19] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:51:21] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 15:51:21] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:51:21] [INFO] 从每日盈亏数据加载三人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 15:51:21] [INFO] 加载到 4 条每日盈亏数据
[2025-05-14 15:51:21] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4094.00
[2025-05-14 15:51:21] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:51:21] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2168.00
[2025-05-14 15:51:21] [INFO] 三人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71
[2025-05-14 15:51:21] [INFO] 绘制三人当月总盈亏图表: 用户数=3, 数据=谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71
[2025-05-14 15:51:21] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 15:51:21] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 15:51:21] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 15:51:21] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 15:51:21] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 15:51:21] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 15:51:21] [INFO] 三人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71
[2025-05-14 15:53:31] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 15:57:51 ===
[2025-05-14 15:57:51] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:57:51] [INFO] 已加载 5 个用户
[2025-05-14 15:57:51] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:57:51] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 15:57:56] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:57:56] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 15:57:59] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:57:59] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-14 15:58:02] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:02] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-14 15:58:04] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:04] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-14 15:58:07] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:07] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-14 15:58:10] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 15:58:10] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:58:10] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 15:58:10] [INFO] 加载到 4 条每日盈亏数据
[2025-05-14 15:58:10] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4094.00
[2025-05-14 15:58:10] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:10] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2168.00
[2025-05-14 15:58:10] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6527.71
[2025-05-14 15:58:10] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 180.00
[2025-05-14 15:58:10] [INFO] 四人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:10] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:10] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 15:58:10] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 15:58:10] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 15:58:10] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 15:58:10] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 15:58:10] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 15:58:10] [INFO] 四人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:17] [INFO] 加载统计数据: 用户=屈猫猫, 年份=2025, 月份=5
[2025-05-14 15:58:17] [INFO] 成功加载月度盈亏数据，共 5 条记录
[2025-05-14 15:58:17] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-14 15:58:17] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 15:58:17] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:17] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2168.00
[2025-05-14 15:58:17] [INFO] 加载到 4 条每日盈亏数据
[2025-05-14 15:58:17] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4094.00
[2025-05-14 15:58:17] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6527.71
[2025-05-14 15:58:17] [INFO] 四人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:17] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 180.00
[2025-05-14 15:58:17] [INFO] 未找到用户 屈猫猫 的年度盈亏数据
[2025-05-14 15:58:19] [INFO] 加载统计数据: 用户=景丝丝, 年份=2025, 月份=5
[2025-05-14 15:58:19] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 15:58:19] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 15:58:19] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:19] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4094.00
[2025-05-14 15:58:19] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2168.00
[2025-05-14 15:58:19] [INFO] 加载到 4 条每日盈亏数据
[2025-05-14 15:58:19] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 180.00
[2025-05-14 15:58:19] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6527.71
[2025-05-14 15:58:19] [INFO] 加载用户 景丝丝 的多年汇总数据
[2025-05-14 15:58:19] [INFO] 四人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:22] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-14 15:58:22] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 15:58:22] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 15:58:22] [INFO] 加载到 4 条每日盈亏数据
[2025-05-14 15:58:22] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:22] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2168.00
[2025-05-14 15:58:22] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4094.00
[2025-05-14 15:58:22] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:22] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6527.71
[2025-05-14 15:58:22] [INFO] 未找到用户 谢羚羚 的年度盈亏数据
[2025-05-14 15:58:22] [INFO] 四人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:25] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 15:58:25] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 15:58:25] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 15:58:25] [INFO] 加载到 4 条每日盈亏数据
[2025-05-14 15:58:25] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:25] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4094.00
[2025-05-14 15:58:25] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 180.00
[2025-05-14 15:58:25] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:25] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 15:58:28] [INFO] 加载统计数据: 用户=谢羚羚, 年份=2025, 月份=5
[2025-05-14 15:58:28] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 15:58:28] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 15:58:28] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:28] [INFO] 加载到 4 条每日盈亏数据
[2025-05-14 15:58:28] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4094.00
[2025-05-14 15:58:28] [INFO] 四人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:28] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2168.00
[2025-05-14 15:58:28] [INFO] 加载用户 谢羚羚 的多年汇总数据
[2025-05-14 15:58:28] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 15:58:28] [INFO] 成功显示用户 谢羚羚 的多年汇总数据
[2025-05-14 15:58:28] [INFO] 加载年度盈亏数据: 用户=谢羚羚, 年份数量=9
[2025-05-14 15:58:41] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 15:58:41] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 15:58:41] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 15:58:41] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 15:58:41] [INFO] 加载到 4 条每日盈亏数据
[2025-05-14 15:58:41] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4094.00
[2025-05-14 15:58:41] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2168.00
[2025-05-14 15:58:41] [INFO] 四人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:41] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 180.00
[2025-05-14 15:58:41] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 15:58:41] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 15:58:41] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 15:59:19] [INFO] 月度盈亏设置已更新
[2025-05-14 15:59:28] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-05-14 15:59:28] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-14 15:59:28] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-14 15:59:28] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-05-14 15:59:28] [INFO] 发送HTTP请求...
[2025-05-14 15:59:28] [INFO] HTTP请求成功，响应长度: 1028
[2025-05-14 15:59:28] [INFO] 开始解析JSON数据...
[2025-05-14 15:59:28] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-14 15:59:28] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-05-14 15:59:28] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-14 15:59:28] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-14 15:59:28] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-05-14 15:59:28] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-05-14 15:59:28] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-05-14 15:59:28] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-14 15:59:28] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-05-14 15:59:28] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-05-14 15:59:28] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-05-14 15:59:28] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-05-14 15:59:28] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-05-14 15:59:28] [INFO] 季度利润窗口加载完成
[2025-05-14 15:59:44] [INFO] 季度利润窗口加载 - 股票代码: 000021, 股票名称: 深科技
[2025-05-14 15:59:44] [INFO] 开始获取股票 000021 的季度利润数据
[2025-05-14 15:59:44] [INFO] 开始获取股票 000021 的季度利润数据
[2025-05-14 15:59:44] [INFO] 发送HTTP请求...
[2025-05-14 15:59:44] [INFO] 开始解析JSON数据...
[2025-05-14 15:59:44] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"336,463.82","expend":"302,999.03","profit":"28,045.86","totalp":"28,092.01","reprofit":"22,790.40","basege":"0.1147","ettege":"0.1147","otherp":"-2,553.17","totalcp":"20,237.23"},{"date":"2024-12-31","income":"1,482,716.65","expend":"1,347,906.19","profit":"123,715.42","totalp":"128,492.56","reprofit":"108,750.44","basege":"0.5962","ettege":"0.5962","otherp":"5,515.26","totalcp":"114,265.70"},{"date":"2024-09-30","income":"1,085,168.56","expend":"991,505.11","profit":"95,267.92","totalp":"96,803.44","reprofit":"81,211.98","basege":"0.4238","ettege":"0.4238","otherp":"3,715.15","totalcp":"84,927.13"},{"date":"2024-06-30","income":"705,456.41","expend":"652,810.88","profit":"56,153.66","totalp":"57,222.06","reprofit":"46,845.05","basege":"0.2309","ettege":"0.2309","otherp":"-14,796.33","totalcp":"32,048.72"},{"date":"2024-03-31","income":"312,637.90","expend":"293,190.19","profit":"21,811.85","totalp":"21,828.44","reprofit":"17,355.01","basege":"0.0780","ettege":"0.0780","otherp":"-11,244.20","totalcp":"6,110.81"}]
[2025-05-14 15:59:44] [INFO] HTTP请求成功，响应长度: 1060
[2025-05-14 15:59:44] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-14 15:59:44] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-14 15:59:44] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-05-14 15:59:44] [INFO] DataGrid.HasItems = True
[2025-05-14 15:59:44] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/5/14 17:54:21 ===
[2025-05-14 17:54:21] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 17:54:21] [INFO] 已加载 6 个用户
[2025-05-14 17:54:21] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 17:54:21] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 17:54:26] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 17:54:26] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 17:54:31] [INFO] 月度盈亏设置已更新
[2025-05-14 18:12:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:12:32] [INFO] 已加载 6 个用户
[2025-05-14 18:12:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:12:32] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 18:12:38] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:12:38] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-14 18:12:42] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 18:12:42] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-14 18:12:50] [INFO] 月度盈亏设置已更新
[2025-05-14 18:12:55] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:12:55] [INFO] 已加载 6 个用户
[2025-05-14 18:12:55] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:12:55] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 18:13:20] [INFO] 月度盈亏设置已更新
[2025-05-14 18:13:26] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:13:26] [INFO] 已加载 6 个用户
[2025-05-14 18:13:26] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:13:26] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 18:13:28] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 18:13:28] [INFO] 已加载 2 个用户
[2025-05-14 18:13:28] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 18:13:28] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 18:13:32] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 18:13:32] [INFO] 已加载用户 Administrator 的年度盈亏数据
[2025-05-14 18:13:34] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 18:13:34] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 18:13:36] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 18:13:36] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 18:13:38] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 18:13:38] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-14 18:13:42] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/14 18:33:24 ===
[2025-05-14 18:33:24] [INFO] 季度利润窗口加载 - 股票代码: 600298, 股票名称: 安琪酵母
[2025-05-14 18:33:24] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-14 18:33:24] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-14 18:33:24] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-05-14 18:33:24] [INFO] 发送HTTP请求...
[2025-05-14 18:33:24] [INFO] HTTP请求成功，响应长度: 1028
[2025-05-14 18:33:24] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-05-14 18:33:24] [INFO] 开始解析JSON数据...
[2025-05-14 18:33:24] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-14 18:33:24] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-05-14 18:33:24] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-14 18:33:24] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-14 18:33:24] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-05-14 18:33:24] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-05-14 18:33:24] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-05-14 18:33:24] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-05-14 18:33:24] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-14 18:33:24] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-05-14 18:33:24] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-05-14 18:33:24] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-05-14 18:33:24] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-05-14 18:33:24] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-05-14 18:33:24] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-05-14 18:33:24] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-05-14 18:33:24] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-05-14 18:33:24] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-05-14 18:33:24] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-05-14 18:33:24] [DEBUG] 解析字段 - 报告期: 2024-03-31, 营收: 348,253, 稀释每股收益: 0.3700, 净利润: 38,723, 综合收益总额: 38,841
[2025-05-14 18:33:24] [INFO] DataGrid.Items.Count = 5
[2025-05-14 18:33:30] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:33:30] [INFO] 已加载 6 个用户
[2025-05-14 18:33:30] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:33:30] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-14 18:33:32] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-14 18:33:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-14 18:33:32] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-14 18:33:32] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-14 18:33:32] [INFO] 加载到 4 条每日盈亏数据
[2025-05-14 18:33:32] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4094.00
[2025-05-14 18:33:32] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2168.00
[2025-05-14 18:33:32] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 180.00
[2025-05-14 18:33:32] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6527.71
[2025-05-14 18:33:32] [INFO] 四人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 18:33:32] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 18:33:32] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-14 18:33:32] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 18:33:32] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-14 18:33:32] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-14 18:33:32] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-14 18:33:32] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-14 18:33:32] [INFO] 四人当月总盈亏数据: 谢羚羚=4094.00, 景丝丝=-2168.00, 钟鹮鹮=6527.71, 屈猫猫=180.00
[2025-05-14 18:33:35] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/15 15:14:07 ===
[2025-05-15 15:14:07] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:14:07] [INFO] 已加载 6 个用户
[2025-05-15 15:14:07] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:14:07] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-15 15:14:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:14:08] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-15 15:14:20] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:14:20] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-15 15:14:23] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:14:23] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-15 15:14:25] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:14:25] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-15 15:14:41] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:14:41] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-15 15:14:47] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:14:47] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-15 15:14:53] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:14:53] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-15 15:14:57] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:14:57] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-15 15:15:04] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:15:04] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-15 15:15:11] [INFO] 月度盈亏设置已更新
[2025-05-15 15:15:14] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:15:14] [INFO] 已加载 6 个用户
[2025-05-15 15:15:14] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:15:14] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-15 15:15:15] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-15 15:15:15] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:15:15] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-15 15:15:15] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:15:15] [INFO] 加载到 4 条每日盈亏数据
[2025-05-15 15:15:15] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3806.00
[2025-05-15 15:15:15] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3304.00
[2025-05-15 15:15:15] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3715.71
[2025-05-15 15:15:15] [INFO] 四人当月总盈亏数据: 谢羚羚=3806.00, 景丝丝=-3304.00, 钟鹮鹮=3715.71, 屈猫猫=180.00
[2025-05-15 15:15:15] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3806.00, 景丝丝=-3304.00, 钟鹮鹮=3715.71, 屈猫猫=180.00
[2025-05-15 15:15:15] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-15 15:15:15] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-15 15:15:15] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-15 15:15:15] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-15 15:15:15] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-15 15:15:15] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-15 15:15:15] [INFO] 四人当月总盈亏数据: 谢羚羚=3806.00, 景丝丝=-3304.00, 钟鹮鹮=3715.71, 屈猫猫=180.00
[2025-05-15 15:15:41] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-15 15:15:41] [INFO] 已加载 2 个用户
[2025-05-15 15:15:41] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-15 15:15:41] [INFO] 已加载用户 xiekun 的年度盈亏数据
[2025-05-15 15:15:44] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/15 15:36:22 ===
[2025-05-15 15:36:22] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:36:22] [INFO] 已加载 6 个用户
[2025-05-15 15:36:22] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:36:22] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-15 15:36:24] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:36:24] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-15 15:36:26] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:36:26] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-15 15:36:54] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:36:54] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-15 15:37:02] [INFO] 月度盈亏设置已更新
[2025-05-15 15:37:06] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:37:06] [INFO] 已加载 6 个用户
[2025-05-15 15:37:06] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:37:06] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-15 15:37:08] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-15 15:37:08] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 15:37:08] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-15 15:37:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 15:37:08] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3806.00
[2025-05-15 15:37:08] [INFO] 加载到 4 条每日盈亏数据
[2025-05-15 15:37:08] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3304.00
[2025-05-15 15:37:08] [INFO] 四人当月总盈亏数据: 谢羚羚=3806.00, 景丝丝=-3304.00, 钟鹮鹮=3715.71, 屈猫猫=180.00
[2025-05-15 15:37:08] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3806.00, 景丝丝=-3304.00, 钟鹮鹮=3715.71, 屈猫猫=180.00
[2025-05-15 15:37:08] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-15 15:37:08] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-15 15:37:08] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-15 15:37:08] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-15 15:37:08] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-15 15:37:08] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-15 15:37:08] [INFO] 四人当月总盈亏数据: 谢羚羚=3806.00, 景丝丝=-3304.00, 钟鹮鹮=3715.71, 屈猫猫=180.00
[2025-05-15 15:37:18] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/15 16:51:59 ===
[2025-05-15 16:51:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 16:51:59] [INFO] 已加载 6 个用户
[2025-05-15 16:51:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-15 16:51:59] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-15 16:52:01] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 16:52:01] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-15 16:52:03] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 16:52:03] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-15 16:52:10] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-15 16:52:10] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-15 16:52:13] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/16 10:07:03 ===
[2025-05-16 10:07:03] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 10:07:03] [INFO] 已加载 6 个用户
[2025-05-16 10:07:03] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 10:07:03] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 10:07:07] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-16 10:07:07] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 10:07:07] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-16 10:07:07] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 10:07:07] [INFO] 加载到 4 条每日盈亏数据
[2025-05-16 10:07:07] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3806.00
[2025-05-16 10:07:07] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3304.00
[2025-05-16 10:07:07] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -280.00
[2025-05-16 10:07:07] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3715.71
[2025-05-16 10:07:07] [INFO] 四人当月总盈亏数据: 谢羚羚=3806.00, 景丝丝=-3304.00, 钟鹮鹮=3715.71, 屈猫猫=-280.00
[2025-05-16 10:07:07] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3806.00, 景丝丝=-3304.00, 钟鹮鹮=3715.71, 屈猫猫=-280.00
[2025-05-16 10:07:07] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-16 10:07:07] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 10:07:07] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-16 10:07:07] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-16 10:07:07] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 10:07:07] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-16 10:07:07] [INFO] 四人当月总盈亏数据: 谢羚羚=3806.00, 景丝丝=-3304.00, 钟鹮鹮=3715.71, 屈猫猫=-280.00
[2025-05-16 10:07:22] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 10:07:22] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 10:07:27] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 10:07:27] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 10:07:35] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 10:07:35] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-16 10:07:36] [INFO] 月度盈亏设置已更新
[2025-05-16 10:07:40] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 10:07:40] [INFO] 已加载 6 个用户
[2025-05-16 10:07:40] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 10:07:40] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 10:07:42] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/16 11:36:47 ===
[2025-05-16 11:36:47] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 11:36:47] [INFO] 已加载 6 个用户
[2025-05-16 11:36:47] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 11:36:47] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 11:36:49] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 11:36:49] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 11:36:52] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 11:36:52] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-16 11:36:58] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 11:36:58] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-16 11:37:01] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 11:37:01] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 11:37:12] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 11:37:12] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-16 11:37:17] [INFO] 月度盈亏设置已更新
[2025-05-16 11:37:19] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 11:37:19] [INFO] 已加载 6 个用户
[2025-05-16 11:37:19] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 11:37:19] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 11:37:21] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-16 11:37:21] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 11:37:21] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-16 11:37:21] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 11:37:21] [INFO] 加载到 4 条每日盈亏数据
[2025-05-16 11:37:21] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2834.00
[2025-05-16 11:37:21] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3304.00
[2025-05-16 11:37:21] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4996.71
[2025-05-16 11:37:21] [INFO] 四人当月总盈亏数据: 谢羚羚=2834.00, 景丝丝=-3304.00, 钟鹮鹮=4996.71, 屈猫猫=-280.00
[2025-05-16 11:37:21] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2834.00, 景丝丝=-3304.00, 钟鹮鹮=4996.71, 屈猫猫=-280.00
[2025-05-16 11:37:21] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-16 11:37:21] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 11:37:21] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-16 11:37:21] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-16 11:37:21] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 11:37:21] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-16 11:37:21] [INFO] 四人当月总盈亏数据: 谢羚羚=2834.00, 景丝丝=-3304.00, 钟鹮鹮=4996.71, 屈猫猫=-280.00
[2025-05-16 11:37:33] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/16 13:58:26 ===
[2025-05-16 13:58:26] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 13:58:26] [INFO] 已加载 6 个用户
[2025-05-16 13:58:26] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 13:58:26] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 13:58:29] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-16 13:58:29] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 13:58:29] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-16 13:58:29] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 13:58:29] [INFO] 加载到 4 条每日盈亏数据
[2025-05-16 13:58:29] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2834.00
[2025-05-16 13:58:29] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -3304.00
[2025-05-16 13:58:29] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -280.00
[2025-05-16 13:58:29] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4996.71
[2025-05-16 13:58:29] [INFO] 四人当月总盈亏数据: 谢羚羚=2834.00, 景丝丝=-3304.00, 钟鹮鹮=4996.71, 屈猫猫=-280.00
[2025-05-16 13:58:29] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2834.00, 景丝丝=-3304.00, 钟鹮鹮=4996.71, 屈猫猫=-280.00
[2025-05-16 13:58:29] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-16 13:58:29] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 13:58:29] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-16 13:58:29] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-16 13:58:29] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 13:58:29] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-16 13:58:29] [INFO] 四人当月总盈亏数据: 谢羚羚=2834.00, 景丝丝=-3304.00, 钟鹮鹮=4996.71, 屈猫猫=-280.00
[2025-05-16 13:58:34] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/16 15:03:57 ===
[2025-05-16 15:03:57] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:03:57] [INFO] 已加载 6 个用户
[2025-05-16 15:03:57] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:03:57] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 15:04:01] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:04:01] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:04:11] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:04:11] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-16 15:04:19] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:04:19] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:04:28] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:04:28] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:04:42] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:04:42] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-16 15:04:43] [INFO] 月度盈亏设置已更新
[2025-05-16 15:04:45] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:04:45] [INFO] 已加载 6 个用户
[2025-05-16 15:04:45] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:04:45] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 15:05:38] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/16 15:16:03 ===
[2025-05-16 15:16:03] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:16:03] [INFO] 已加载 6 个用户
[2025-05-16 15:16:03] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:16:03] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 15:16:06] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:16:06] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:16:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:16:08] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-16 15:17:03] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:17:03] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-16 15:17:06] [INFO] 月度盈亏设置已更新
[2025-05-16 15:18:04] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:18:04] [INFO] 已加载 6 个用户
[2025-05-16 15:18:04] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:18:04] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 15:18:11] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:18:11] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:18:15] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:18:15] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-16 15:18:23] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:18:23] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-16 15:18:27] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:18:27] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-16 15:18:30] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:18:30] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:18:34] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:18:34] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-16 15:18:53] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:18:53] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-16 15:19:16] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:19:16] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-16 15:19:20] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:19:20] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-16 15:19:22] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:19:22] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:19:37] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:19:37] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:19:41] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:19:41] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-16 15:20:23] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:20:23] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-16 15:20:24] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:20:24] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-16 15:21:09] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:21:09] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-16 15:21:18] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-16 15:21:18] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:21:18] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-16 15:21:18] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:21:18] [INFO] 加载到 4 条每日盈亏数据
[2025-05-16 15:21:18] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3032.00
[2025-05-16 15:21:18] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1790.00
[2025-05-16 15:21:18] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4528.71
[2025-05-16 15:21:18] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -280.00
[2025-05-16 15:21:18] [INFO] 四人当月总盈亏数据: 谢羚羚=3032.00, 景丝丝=-1790.00, 钟鹮鹮=4528.71, 屈猫猫=-280.00
[2025-05-16 15:21:18] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3032.00, 景丝丝=-1790.00, 钟鹮鹮=4528.71, 屈猫猫=-280.00
[2025-05-16 15:21:18] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-16 15:21:18] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 15:21:18] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-16 15:21:18] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-16 15:21:18] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 15:21:18] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-16 15:21:18] [INFO] 四人当月总盈亏数据: 谢羚羚=3032.00, 景丝丝=-1790.00, 钟鹮鹮=4528.71, 屈猫猫=-280.00
[2025-05-16 15:22:44] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:22:44] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:22:58] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:22:58] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 15:23:04] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:23:04] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-16 15:23:09] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:23:09] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-16 15:23:10] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/16 15:26:20 ===
[2025-05-16 15:26:20] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:26:20] [INFO] 已加载 6 个用户
[2025-05-16 15:26:20] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:26:20] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 15:26:50] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:26:50] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-16 15:26:52] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:26:52] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-16 15:26:57] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:26:57] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-16 15:26:59] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:26:59] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-16 15:27:32] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-16 15:27:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-16 15:27:32] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-16 15:27:32] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-16 15:27:32] [INFO] 加载到 4 条每日盈亏数据
[2025-05-16 15:27:32] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3032.00
[2025-05-16 15:27:32] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1790.00
[2025-05-16 15:27:32] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4528.71
[2025-05-16 15:27:32] [INFO] 四人当月总盈亏数据: 谢羚羚=3032.00, 景丝丝=-1790.00, 钟鹮鹮=4528.71, 屈猫猫=-280.00
[2025-05-16 15:27:32] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3032.00, 景丝丝=-1790.00, 钟鹮鹮=4528.71, 屈猫猫=-280.00
[2025-05-16 15:27:32] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-16 15:27:32] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 15:27:32] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-16 15:27:32] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-16 15:27:32] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-16 15:27:32] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-16 15:27:32] [INFO] 四人当月总盈亏数据: 谢羚羚=3032.00, 景丝丝=-1790.00, 钟鹮鹮=4528.71, 屈猫猫=-280.00
[2025-05-16 15:34:40] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/19 11:51:16 ===
[2025-05-19 11:51:16] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 11:51:16] [INFO] 已加载 6 个用户
[2025-05-19 11:51:16] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 11:51:16] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-19 11:51:19] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 11:51:19] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 11:51:29] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 11:51:29] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-19 11:51:37] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 11:51:37] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 11:51:43] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 11:51:43] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-05-19 11:51:45] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 11:51:45] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-19 11:51:53] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 11:51:53] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-19 11:51:54] [INFO] 月度盈亏设置已更新
[2025-05-19 11:51:56] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 11:51:56] [INFO] 已加载 6 个用户
[2025-05-19 11:51:56] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 11:51:56] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-19 11:52:00] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 11:52:01] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 11:52:04] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-19 11:52:04] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 11:52:04] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-19 11:52:04] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 11:52:04] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3213.00
[2025-05-19 11:52:04] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4528.71
[2025-05-19 11:52:04] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1790.00
[2025-05-19 11:52:04] [INFO] 四人当月总盈亏数据: 谢羚羚=3213.00, 景丝丝=-1790.00, 钟鹮鹮=4528.71, 屈猫猫=-280.00
[2025-05-19 11:52:04] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3213.00, 景丝丝=-1790.00, 钟鹮鹮=4528.71, 屈猫猫=-280.00
[2025-05-19 11:52:04] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-19 11:52:04] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-19 11:52:04] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-19 11:52:04] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-19 11:52:04] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-19 11:52:04] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-19 11:52:04] [INFO] 四人当月总盈亏数据: 谢羚羚=3213.00, 景丝丝=-1790.00, 钟鹮鹮=4528.71, 屈猫猫=-280.00
[2025-05-19 11:52:10] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/19 12:01:47 ===
[2025-05-19 12:01:47] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 12:01:47] [INFO] 已加载 6 个用户
[2025-05-19 12:01:47] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 12:01:47] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-19 12:01:50] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:01:50] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 12:01:53] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:01:53] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-19 12:02:04] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:02:04] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-19 12:02:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:02:08] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 12:02:10] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:02:10] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-19 12:02:23] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/19 12:40:39 ===
[2025-05-19 12:40:39] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 12:40:39] [INFO] 已加载 6 个用户
[2025-05-19 12:40:39] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 12:40:39] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-19 12:40:41] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:40:41] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 12:40:44] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:40:44] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-19 12:40:54] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:40:54] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-19 12:40:58] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 12:40:58] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-19 12:41:03] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:41:03] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 12:41:06] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:41:06] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-19 12:41:17] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:41:17] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-19 12:41:21] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-19 12:41:21] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 12:41:21] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-19 12:41:21] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 12:41:21] [INFO] 加载到 4 条每日盈亏数据
[2025-05-19 12:41:21] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3213.00
[2025-05-19 12:41:21] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1685.00
[2025-05-19 12:41:21] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -100.00
[2025-05-19 12:41:21] [INFO] 四人当月总盈亏数据: 谢羚羚=3213.00, 景丝丝=-1685.00, 钟鹮鹮=3946.71, 屈猫猫=-100.00
[2025-05-19 12:41:21] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3213.00, 景丝丝=-1685.00, 钟鹮鹮=3946.71, 屈猫猫=-100.00
[2025-05-19 12:41:21] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-19 12:41:21] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-19 12:41:21] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-19 12:41:21] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-19 12:41:21] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-19 12:41:21] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-19 12:41:21] [INFO] 四人当月总盈亏数据: 谢羚羚=3213.00, 景丝丝=-1685.00, 钟鹮鹮=3946.71, 屈猫猫=-100.00
[2025-05-19 12:41:34] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/19 13:45:20 ===
[2025-05-19 13:45:20] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 13:45:20] [INFO] 已加载 6 个用户
[2025-05-19 13:45:20] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 13:45:20] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-19 13:45:22] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 13:45:22] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 13:45:29] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/19 19:16:06 ===
[2025-05-19 19:16:06] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 19:16:06] [INFO] 已加载 6 个用户
[2025-05-19 19:16:06] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 19:16:06] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-19 19:16:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:16:08] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 19:16:17] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:16:17] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-19 19:16:22] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 19:16:22] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-19 19:16:24] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:16:24] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 19:16:26] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:16:26] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-19 19:16:40] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:16:40] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-19 19:16:44] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:16:44] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 19:16:46] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:16:46] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-19 19:16:54] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:16:54] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-19 19:16:58] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:16:58] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-19 19:17:00] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:17:00] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-19 19:17:06] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:17:06] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-19 19:17:09] [INFO] 月度盈亏设置已更新
[2025-05-19 19:17:11] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 19:17:11] [INFO] 已加载 6 个用户
[2025-05-19 19:17:11] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 19:17:11] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-19 19:17:12] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-19 19:17:12] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-19 19:17:12] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-19 19:17:12] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-19 19:17:12] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3139.00
[2025-05-19 19:17:12] [INFO] 加载到 4 条每日盈亏数据
[2025-05-19 19:17:12] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1709.00
[2025-05-19 19:17:12] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 0.00
[2025-05-19 19:17:12] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4964.71
[2025-05-19 19:17:12] [INFO] 四人当月总盈亏数据: 谢羚羚=3139.00, 景丝丝=-1709.00, 钟鹮鹮=4964.71, 屈猫猫=0.00
[2025-05-19 19:17:12] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3139.00, 景丝丝=-1709.00, 钟鹮鹮=4964.71, 屈猫猫=0.00
[2025-05-19 19:17:12] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-19 19:17:12] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-19 19:17:12] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-19 19:17:12] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-19 19:17:12] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-19 19:17:12] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-19 19:17:12] [INFO] 四人当月总盈亏数据: 谢羚羚=3139.00, 景丝丝=-1709.00, 钟鹮鹮=4964.71, 屈猫猫=0.00
[2025-05-19 19:17:24] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/20 11:40:23 ===
[2025-05-20 11:40:23] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 11:40:23] [INFO] 已加载 6 个用户
[2025-05-20 11:40:23] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 11:40:23] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-20 11:40:25] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:40:25] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-20 11:40:39] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:40:39] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-20 11:40:43] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:40:43] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-20 11:40:46] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:40:46] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-20 11:40:51] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:40:51] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-20 11:40:56] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-20 11:40:56] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 11:40:56] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-20 11:40:56] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:40:56] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4829.00
[2025-05-20 11:40:56] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1709.00
[2025-05-20 11:40:56] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4964.71
[2025-05-20 11:40:56] [INFO] 四人当月总盈亏数据: 谢羚羚=4829.00, 景丝丝=-1709.00, 钟鹮鹮=4964.71, 屈猫猫=0.00
[2025-05-20 11:40:56] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=4829.00, 景丝丝=-1709.00, 钟鹮鹮=4964.71, 屈猫猫=0.00
[2025-05-20 11:40:56] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-20 11:40:56] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-20 11:40:56] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-20 11:40:56] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-20 11:40:56] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-20 11:40:56] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-20 11:40:56] [INFO] 四人当月总盈亏数据: 谢羚羚=4829.00, 景丝丝=-1709.00, 钟鹮鹮=4964.71, 屈猫猫=0.00
[2025-05-20 11:41:07] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/20 11:49:35 ===
[2025-05-20 11:49:35] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 11:49:35] [INFO] 已加载 6 个用户
[2025-05-20 11:49:35] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 11:49:35] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-20 11:49:38] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:49:38] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-20 11:49:40] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:49:40] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-20 11:49:47] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:49:47] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-20 11:49:51] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-20 11:49:51] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 11:49:51] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-20 11:49:51] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:49:51] [INFO] 加载到 4 条每日盈亏数据
[2025-05-20 11:49:51] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4829.00
[2025-05-20 11:49:51] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -928.00
[2025-05-20 11:49:51] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4964.71
[2025-05-20 11:49:51] [INFO] 四人当月总盈亏数据: 谢羚羚=4829.00, 景丝丝=-928.00, 钟鹮鹮=4964.71, 屈猫猫=0.00
[2025-05-20 11:49:51] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=4829.00, 景丝丝=-928.00, 钟鹮鹮=4964.71, 屈猫猫=0.00
[2025-05-20 11:49:51] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-20 11:49:51] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-20 11:49:51] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-20 11:49:51] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-20 11:49:51] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-20 11:49:51] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-20 11:49:51] [INFO] 四人当月总盈亏数据: 谢羚羚=4829.00, 景丝丝=-928.00, 钟鹮鹮=4964.71, 屈猫猫=0.00
[2025-05-20 11:49:58] [INFO] 月度盈亏设置已更新
[2025-05-20 11:57:48] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 11:57:48] [INFO] 已加载 6 个用户
[2025-05-20 11:57:48] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 11:57:48] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-20 11:57:53] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:57:53] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-20 11:57:55] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:57:55] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-20 11:58:03] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:58:03] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-20 11:58:07] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-20 11:58:07] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 11:58:07] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-20 11:58:07] [INFO] 加载到 4 条每日盈亏数据
[2025-05-20 11:58:07] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 11:58:07] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6185.71
[2025-05-20 11:58:07] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4829.00
[2025-05-20 11:58:07] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 0.00
[2025-05-20 11:58:07] [INFO] 四人当月总盈亏数据: 谢羚羚=4829.00, 景丝丝=-928.00, 钟鹮鹮=6185.71, 屈猫猫=0.00
[2025-05-20 11:58:07] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-20 11:58:07] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=4829.00, 景丝丝=-928.00, 钟鹮鹮=6185.71, 屈猫猫=0.00
[2025-05-20 11:58:07] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-20 11:58:07] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-20 11:58:24] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/20 15:28:40 ===
[2025-05-20 15:28:40] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 15:28:40] [INFO] 已加载 6 个用户
[2025-05-20 15:28:40] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 15:28:40] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-20 15:28:54] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 15:28:54] [INFO] 已加载用户 钟鹮鹮 的月度盈亏数据
[2025-05-20 15:29:01] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 15:29:01] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-20 15:29:04] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 15:29:04] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-20 15:29:12] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 15:29:12] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-20 15:29:15] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 15:29:15] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-20 15:29:28] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 15:29:28] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-20 15:29:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 15:29:32] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-20 15:29:34] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 15:29:34] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-20 15:29:36] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 15:29:36] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-20 15:29:50] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 15:29:50] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-20 15:29:53] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-20 15:29:53] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-20 15:29:53] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-20 15:29:53] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-20 15:29:53] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3608.00
[2025-05-20 15:29:53] [INFO] 加载到 4 条每日盈亏数据
[2025-05-20 15:29:53] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1364.00
[2025-05-20 15:29:53] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 5423.71
[2025-05-20 15:29:53] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 0.00
[2025-05-20 15:29:53] [INFO] 四人当月总盈亏数据: 谢羚羚=3608.00, 景丝丝=-1364.00, 钟鹮鹮=5423.71, 屈猫猫=0.00
[2025-05-20 15:29:53] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3608.00, 景丝丝=-1364.00, 钟鹮鹮=5423.71, 屈猫猫=0.00
[2025-05-20 15:29:53] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-20 15:29:53] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-20 15:29:53] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-20 15:29:53] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-20 15:29:53] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-20 15:29:53] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-20 15:29:53] [INFO] 四人当月总盈亏数据: 谢羚羚=3608.00, 景丝丝=-1364.00, 钟鹮鹮=5423.71, 屈猫猫=0.00
[2025-05-20 15:30:06] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/21 14:44:51 ===
[2025-05-21 14:44:51] [INFO] 开始获取股票 600298 的季度利润数据
[2025-05-21 14:44:52] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
[2025-05-21 14:44:52] [INFO] 发送HTTP请求...
[2025-05-21 14:44:52] [INFO] HTTP请求成功，响应长度: 1028
[2025-05-21 14:44:52] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/600298/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"379,417.76","expend":"331,668.59","profit":"47,819.36","totalp":"47,949.88","reprofit":"38,696.10","basege":"0.4300","ettege":"0.4300","otherp":"--","totalcp":"38,696.10"},{"date":"2024-12-31","income":"1,519,691.32","expend":"1,375,387.81","profit":"156,452.55","totalp":"158,856.68","reprofit":"135,291.60","basege":"1.5500","ettege":"1.5400","otherp":"--","totalcp":"135,291.60"},{"date":"2024-09-30","income":"1,091,224.20","expend":"986,886.44","profit":"115,680.26","totalp":"116,443.54","reprofit":"96,974.61","basege":"1.1200","ettege":"1.1200","otherp":"--","totalcp":"96,974.61"},{"date":"2024-06-30","income":"717,461.78","expend":"640,735.80","profit":"84,989.61","totalp":"85,601.26","reprofit":"70,879.89","basege":"0.8000","ettege":"0.8000","otherp":"--","totalcp":"70,879.89"},{"date":"2024-03-31","income":"348,252.89","expend":"310,876.99","profit":"38,723.22","totalp":"38,841.09","reprofit":"32,993.08","basege":"0.3700","ettege":"0.3700","otherp":"--","totalcp":"32,993.08"}]
[2025-05-21 14:44:52] [INFO] 开始解析JSON数据...
[2025-05-21 14:44:52] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-21 14:44:52] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "379,417.76",
  "expend": "331,668.59",
  "profit": "47,819.36",
  "totalp": "47,949.88",
  "reprofit": "38,696.10",
  "basege": "0.4300",
  "ettege": "0.4300",
  "otherp": "--",
  "totalcp": "38,696.10"
}
[2025-05-21 14:44:52] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-21 14:44:52] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-21 14:44:52] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 379,418, 稀释每股收益: 0.4300, 净利润: 47,819, 综合收益总额: 47,950
[2025-05-21 14:44:52] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,519,691.32",
  "expend": "1,375,387.81",
  "profit": "156,452.55",
  "totalp": "158,856.68",
  "reprofit": "135,291.60",
  "basege": "1.5500",
  "ettege": "1.5400",
  "otherp": "--",
  "totalcp": "135,291.60"
}
[2025-05-21 14:44:52] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-05-21 14:44:52] [DEBUG] 解析字段 - 报告期: 2024-12-31, 营收: 1.52百万, 稀释每股收益: 1.5400, 净利润: 156,453, 综合收益总额: 158,857
[2025-05-21 14:44:52] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-05-21 14:44:52] [DEBUG] 处理项: {
  "date": "2024-09-30",
  "income": "1,091,224.20",
  "expend": "986,886.44",
  "profit": "115,680.26",
  "totalp": "116,443.54",
  "reprofit": "96,974.61",
  "basege": "1.1200",
  "ettege": "1.1200",
  "otherp": "--",
  "totalcp": "96,974.61"
}
[2025-05-21 14:44:52] [DEBUG] 解析报告期: 原始值=2024-09-30, 格式化后=2024-09-30
[2025-05-21 14:44:52] [DEBUG] API返回的日期字段: 2024-06-30, 类型: JValue
[2025-05-21 14:44:52] [DEBUG] 处理项: {
  "date": "2024-06-30",
  "income": "717,461.78",
  "expend": "640,735.80",
  "profit": "84,989.61",
  "totalp": "85,601.26",
  "reprofit": "70,879.89",
  "basege": "0.8000",
  "ettege": "0.8000",
  "otherp": "--",
  "totalcp": "70,879.89"
}
[2025-05-21 14:44:52] [DEBUG] 解析字段 - 报告期: 2024-09-30, 营收: 1.09百万, 稀释每股收益: 1.1200, 净利润: 115,680, 综合收益总额: 116,444
[2025-05-21 14:44:52] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 717,462, 稀释每股收益: 0.8000, 净利润: 84,990, 综合收益总额: 85,601
[2025-05-21 14:44:52] [DEBUG] 处理项: {
  "date": "2024-03-31",
  "income": "348,252.89",
  "expend": "310,876.99",
  "profit": "38,723.22",
  "totalp": "38,841.09",
  "reprofit": "32,993.08",
  "basege": "0.3700",
  "ettege": "0.3700",
  "otherp": "--",
  "totalcp": "32,993.08"
}
[2025-05-21 14:44:52] [DEBUG] 解析报告期: 原始值=2024-06-30, 格式化后=2024-06-30
[2025-05-21 14:44:52] [DEBUG] API返回的日期字段: 2024-03-31, 类型: JValue
[2025-05-21 14:44:52] [DEBUG] 解析报告期: 原始值=2024-03-31, 格式化后=2024-03-31
[2025-05-21 14:44:52] [INFO] 返回 5 条季度利润数据
[2025-05-21 14:44:52] [DEBUG] 季度利润数据: 报告期=2025-03-31, 营收=379,418, 稀释每股收益=0.4300, 净利润=47,819, 综合收益总额=47,950
[2025-05-21 14:44:52] [INFO] 获取到 5 条季度利润数据
[2025-05-21 14:44:52] [DEBUG] 季度利润数据: 报告期=2024-03-31, 营收=348,253, 稀释每股收益=0.3700, 净利润=38,723, 综合收益总额=38,841
[2025-05-21 14:44:52] [INFO] DataGrid.Items.Count = 5
=== 日志开始于 2025/5/22 11:58:26 ===
[2025-05-22 11:58:26] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 11:58:26] [INFO] 已加载 6 个用户
[2025-05-22 11:58:26] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 11:58:26] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-22 11:58:28] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:58:28] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 11:58:32] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:58:32] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-22 11:58:42] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:58:42] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 11:58:59] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:58:59] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 11:59:01] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:59:01] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-22 11:59:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:59:08] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 11:59:12] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:59:12] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 11:59:29] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:59:29] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 11:59:35] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:59:35] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 11:59:38] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:59:38] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-22 11:59:55] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 11:59:55] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 12:00:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:00:08] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 12:00:21] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:00:21] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 12:00:40] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:00:40] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 12:00:45] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:00:45] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-22 12:00:58] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:00:58] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 12:54:43] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:54:43] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 12:54:45] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:54:45] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-22 12:54:55] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:54:55] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 12:54:58] [INFO] 月度盈亏设置已更新
[2025-05-22 12:55:22] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 12:55:22] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 12:55:22] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-22 12:55:25] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-22 12:55:25] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 12:55:25] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-22 12:55:25] [INFO] 加载到 4 条每日盈亏数据
[2025-05-22 12:55:25] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:55:25] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3362.00
[2025-05-22 12:55:25] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -5038.00
[2025-05-22 12:55:25] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4384.71
[2025-05-22 12:55:25] [INFO] 四人当月总盈亏数据: 谢羚羚=3362.00, 景丝丝=-5038.00, 钟鹮鹮=4384.71, 屈猫猫=100.00
[2025-05-22 12:55:25] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3362.00, 景丝丝=-5038.00, 钟鹮鹮=4384.71, 屈猫猫=100.00
[2025-05-22 12:55:25] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-22 12:55:25] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-22 12:55:25] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-22 12:55:25] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-22 12:55:25] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-22 12:55:25] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-22 12:55:25] [INFO] 四人当月总盈亏数据: 谢羚羚=3362.00, 景丝丝=-5038.00, 钟鹮鹮=4384.71, 屈猫猫=100.00
[2025-05-22 12:55:59] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:55:59] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 12:56:01] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:56:01] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-22 12:56:32] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-22 12:56:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 12:56:32] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-22 12:56:32] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:56:32] [INFO] 加载到 4 条每日盈亏数据
[2025-05-22 12:56:32] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3362.00
[2025-05-22 12:56:32] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4384.71
[2025-05-22 12:56:32] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3362.00, 景丝丝=-5038.00, 钟鹮鹮=4384.71, 屈猫猫=100.00
[2025-05-22 12:56:32] [INFO] 四人当月总盈亏数据: 谢羚羚=3362.00, 景丝丝=-5038.00, 钟鹮鹮=4384.71, 屈猫猫=100.00
[2025-05-22 12:56:32] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-22 12:56:32] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-22 12:56:32] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-22 12:56:32] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-22 12:56:32] [INFO] 四人当月总盈亏数据: 谢羚羚=3362.00, 景丝丝=-5038.00, 钟鹮鹮=4384.71, 屈猫猫=100.00
[2025-05-22 12:56:42] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 12:56:42] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 12:56:50] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/22 15:06:17 ===
[2025-05-22 15:06:17] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:06:17] [INFO] 已加载 6 个用户
[2025-05-22 15:06:17] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:06:17] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-22 15:06:19] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:06:19] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 15:06:27] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:06:27] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 15:06:30] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/22 15:09:00 ===
[2025-05-22 15:09:00] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:09:00] [INFO] 已加载 6 个用户
[2025-05-22 15:09:00] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:09:00] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-22 15:09:02] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:09:02] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 15:09:04] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:09:04] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-22 15:09:12] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:09:12] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 15:09:15] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-22 15:09:15] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:09:15] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-22 15:09:15] [INFO] 加载到 4 条每日盈亏数据
[2025-05-22 15:09:15] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:09:15] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2583.00
[2025-05-22 15:09:15] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -5534.00
[2025-05-22 15:09:15] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 4384.71
[2025-05-22 15:09:15] [INFO] 四人当月总盈亏数据: 谢羚羚=2583.00, 景丝丝=-5534.00, 钟鹮鹮=4384.71, 屈猫猫=100.00
[2025-05-22 15:09:15] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2583.00, 景丝丝=-5534.00, 钟鹮鹮=4384.71, 屈猫猫=100.00
[2025-05-22 15:09:15] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-22 15:09:15] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-22 15:09:15] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-22 15:09:15] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-22 15:09:15] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-22 15:09:15] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-22 15:09:15] [INFO] 四人当月总盈亏数据: 谢羚羚=2583.00, 景丝丝=-5534.00, 钟鹮鹮=4384.71, 屈猫猫=100.00
[2025-05-22 15:09:21] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/22 15:09:41 ===
[2025-05-22 15:09:41] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:09:41] [INFO] 已加载 6 个用户
[2025-05-22 15:09:41] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:09:41] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-22 15:09:43] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:09:43] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 15:09:46] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/22 15:22:14 ===
[2025-05-22 15:22:14] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:22:14] [INFO] 已加载 6 个用户
[2025-05-22 15:22:14] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:22:14] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-22 15:22:16] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:22:16] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-22 15:22:18] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:22:18] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-22 15:22:27] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:22:27] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-22 15:22:30] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-22 15:22:30] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-22 15:22:30] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-22 15:22:30] [INFO] 加载到 4 条每日盈亏数据
[2025-05-22 15:22:30] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2583.00
[2025-05-22 15:22:30] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-22 15:22:30] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -5534.00
[2025-05-22 15:22:30] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3112.71
[2025-05-22 15:22:30] [INFO] 四人当月总盈亏数据: 谢羚羚=2583.00, 景丝丝=-5534.00, 钟鹮鹮=3112.71, 屈猫猫=100.00
[2025-05-22 15:22:30] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2583.00, 景丝丝=-5534.00, 钟鹮鹮=3112.71, 屈猫猫=100.00
[2025-05-22 15:22:30] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-22 15:22:30] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-22 15:22:30] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-22 15:22:30] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-22 15:22:30] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-22 15:22:30] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-22 15:22:30] [INFO] 四人当月总盈亏数据: 谢羚羚=2583.00, 景丝丝=-5534.00, 钟鹮鹮=3112.71, 屈猫猫=100.00
[2025-05-22 15:22:38] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/23 11:33:01 ===
[2025-05-23 11:33:01] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-23 11:33:01] [INFO] 已加载 6 个用户
[2025-05-23 11:33:01] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-23 11:33:01] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-23 11:33:02] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:33:02] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-23 11:33:17] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:33:17] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-23 11:33:20] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-23 11:33:20] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-23 11:33:20] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-23 11:33:20] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:33:20] [INFO] 加载到 4 条每日盈亏数据
[2025-05-23 11:33:20] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3011.00
[2025-05-23 11:33:20] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -5534.00
[2025-05-23 11:33:20] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 100.00
[2025-05-23 11:33:20] [INFO] 四人当月总盈亏数据: 谢羚羚=3011.00, 景丝丝=-5534.00, 钟鹮鹮=3112.71, 屈猫猫=100.00
[2025-05-23 11:33:20] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3011.00, 景丝丝=-5534.00, 钟鹮鹮=3112.71, 屈猫猫=100.00
[2025-05-23 11:33:20] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-23 11:33:20] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-23 11:33:20] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-23 11:33:20] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-23 11:33:20] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-23 11:33:20] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-23 11:33:20] [INFO] 四人当月总盈亏数据: 谢羚羚=3011.00, 景丝丝=-5534.00, 钟鹮鹮=3112.71, 屈猫猫=100.00
[2025-05-23 11:33:25] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/23 11:36:59 ===
[2025-05-23 11:36:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-23 11:36:59] [INFO] 已加载 6 个用户
[2025-05-23 11:36:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-23 11:36:59] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-23 11:37:01] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:37:01] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-23 11:37:02] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:37:02] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-23 11:37:13] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:37:13] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-23 11:37:17] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-23 11:37:17] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-23 11:37:17] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-23 11:37:17] [INFO] 加载到 4 条每日盈亏数据
[2025-05-23 11:37:17] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:37:17] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3011.00
[2025-05-23 11:37:17] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-23 11:37:17] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3112.71
[2025-05-23 11:37:17] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 100.00
[2025-05-23 11:37:17] [INFO] 四人当月总盈亏数据: 谢羚羚=3011.00, 景丝丝=-6587.00, 钟鹮鹮=3112.71, 屈猫猫=100.00
[2025-05-23 11:37:17] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3011.00, 景丝丝=-6587.00, 钟鹮鹮=3112.71, 屈猫猫=100.00
[2025-05-23 11:37:17] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-23 11:37:17] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-23 11:37:17] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-23 11:37:17] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-23 11:37:17] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-23 11:37:17] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-23 11:37:17] [INFO] 四人当月总盈亏数据: 谢羚羚=3011.00, 景丝丝=-6587.00, 钟鹮鹮=3112.71, 屈猫猫=100.00
[2025-05-23 11:37:21] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/23 11:43:04 ===
[2025-05-23 11:43:04] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-23 11:43:04] [INFO] 已加载 6 个用户
[2025-05-23 11:43:04] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-23 11:43:04] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-23 11:43:06] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:43:06] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-23 11:43:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:43:08] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-23 11:43:17] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:43:17] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-23 11:43:22] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-23 11:43:22] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-23 11:43:22] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-23 11:43:22] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-23 11:43:22] [INFO] 加载到 4 条每日盈亏数据
[2025-05-23 11:43:22] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 3011.00
[2025-05-23 11:43:22] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-23 11:43:22] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 100.00
[2025-05-23 11:43:22] [INFO] 四人当月总盈亏数据: 谢羚羚=3011.00, 景丝丝=-6587.00, 钟鹮鹮=4174.71, 屈猫猫=100.00
[2025-05-23 11:43:22] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=3011.00, 景丝丝=-6587.00, 钟鹮鹮=4174.71, 屈猫猫=100.00
[2025-05-23 11:43:22] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-23 11:43:22] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-23 11:43:22] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-23 11:43:22] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-23 11:43:22] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-23 11:43:22] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-23 11:43:22] [INFO] 四人当月总盈亏数据: 谢羚羚=3011.00, 景丝丝=-6587.00, 钟鹮鹮=4174.71, 屈猫猫=100.00
[2025-05-23 11:43:32] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/26 11:31:30 ===
[2025-05-26 11:31:30] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:31:30] [INFO] 已加载 6 个用户
[2025-05-26 11:31:30] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:31:30] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-26 11:31:31] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:31:31] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 11:32:02] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:32:02] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-26 11:36:51] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:36:51] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 11:37:19] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:37:19] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-26 11:37:25] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:37:25] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 11:37:27] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:37:27] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-26 11:37:46] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:37:46] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-26 11:37:53] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:37:53] [INFO] 已加载用户 景丝丝 的月度盈亏数据
[2025-05-26 11:38:27] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:38:27] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 11:38:36] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:38:36] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-26 11:38:40] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-26 11:38:40] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:38:40] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-26 11:38:40] [INFO] 加载到 4 条每日盈亏数据
[2025-05-26 11:38:40] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2532.00
[2025-05-26 11:38:40] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-26 11:38:40] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 1658.71
[2025-05-26 11:38:40] [INFO] 四人当月总盈亏数据: 谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=1658.71, 屈猫猫=100.00
[2025-05-26 11:38:40] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=1658.71, 屈猫猫=100.00
[2025-05-26 11:38:40] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-26 11:38:40] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 11:38:40] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-26 11:38:40] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-26 11:38:40] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-26 11:38:40] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 11:38:40] [INFO] 四人当月总盈亏数据: 谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=1658.71, 屈猫猫=100.00
[2025-05-26 11:38:54] [INFO] 月度盈亏设置已更新
[2025-05-26 11:38:57] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:38:57] [INFO] 已加载 6 个用户
[2025-05-26 11:38:57] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:38:57] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-26 11:38:59] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:38:59] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 11:39:01] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:39:01] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-26 11:39:27] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-26 11:39:27] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:39:27] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-26 11:39:27] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:39:27] [INFO] 加载到 4 条每日盈亏数据
[2025-05-26 11:39:27] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2532.00
[2025-05-26 11:39:27] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-26 11:39:27] [INFO] 四人当月总盈亏数据: 谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=1658.71, 屈猫猫=100.00
[2025-05-26 11:39:27] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 1658.71
[2025-05-26 11:39:27] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 11:39:27] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-26 11:39:27] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-26 11:39:27] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 11:39:27] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-26 11:39:27] [INFO] 四人当月总盈亏数据: 谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=1658.71, 屈猫猫=100.00
[2025-05-26 11:39:30] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/26 11:48:59 ===
[2025-05-26 11:48:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:48:59] [INFO] 已加载 6 个用户
[2025-05-26 11:48:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:48:59] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-26 11:49:02] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:49:02] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 11:49:03] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:49:03] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-26 11:49:13] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:49:13] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-26 11:49:16] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-26 11:49:16] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:49:16] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-26 11:49:16] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:49:16] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2532.00
[2025-05-26 11:49:16] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-26 11:49:16] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 100.00
[2025-05-26 11:49:16] [INFO] 四人当月总盈亏数据: 谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=2014.71, 屈猫猫=100.00
[2025-05-26 11:49:16] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=2014.71, 屈猫猫=100.00
[2025-05-26 11:49:16] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-26 11:49:16] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 11:49:16] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-26 11:49:16] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-26 11:49:16] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 11:49:16] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-26 11:49:16] [INFO] 四人当月总盈亏数据: 谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=2014.71, 屈猫猫=100.00
[2025-05-26 11:49:23] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/26 11:49:38 ===
[2025-05-26 11:49:38] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:49:38] [INFO] 已加载 6 个用户
[2025-05-26 11:49:38] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:49:38] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-26 11:51:06] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-26 11:51:06] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 11:51:06] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-26 11:51:06] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 11:51:06] [INFO] 加载到 4 条每日盈亏数据
[2025-05-26 11:51:06] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2532.00
[2025-05-26 11:51:06] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-26 11:51:06] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 100.00
[2025-05-26 11:51:06] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 2014.71
[2025-05-26 11:51:06] [INFO] 四人当月总盈亏数据: 谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=2014.71, 屈猫猫=100.00
[2025-05-26 11:51:06] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=2014.71, 屈猫猫=100.00
[2025-05-26 11:51:06] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-26 11:51:06] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 11:51:06] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-26 11:51:06] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-26 11:51:06] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 11:51:06] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-26 11:51:06] [INFO] 四人当月总盈亏数据: 谢羚羚=2532.00, 景丝丝=-6587.00, 钟鹮鹮=2014.71, 屈猫猫=100.00
[2025-05-26 11:51:09] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/26 15:35:02 ===
[2025-05-26 15:35:02] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 15:35:02] [INFO] 已加载 6 个用户
[2025-05-26 15:35:02] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 15:35:02] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-26 15:35:05] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:35:05] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 15:35:10] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:35:10] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-26 15:35:20] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:35:20] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-26 15:35:27] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:35:27] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 15:35:39] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:35:39] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-26 15:35:45] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:35:45] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 15:35:48] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:35:48] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-26 15:35:56] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:35:56] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-26 15:35:58] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-26 15:35:58] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 15:35:58] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-26 15:35:58] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:35:58] [INFO] 加载到 4 条每日盈亏数据
[2025-05-26 15:35:58] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-26 15:35:58] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 140.00
[2025-05-26 15:35:58] [INFO] 四人当月总盈亏数据: 谢羚羚=2969.00, 景丝丝=-6587.00, 钟鹮鹮=3349.71, 屈猫猫=140.00
[2025-05-26 15:35:58] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2969.00, 景丝丝=-6587.00, 钟鹮鹮=3349.71, 屈猫猫=140.00
[2025-05-26 15:35:58] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-26 15:35:58] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 15:35:58] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-26 15:35:58] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-26 15:35:58] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 15:35:58] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-26 15:35:58] [INFO] 四人当月总盈亏数据: 谢羚羚=2969.00, 景丝丝=-6587.00, 钟鹮鹮=3349.71, 屈猫猫=140.00
[2025-05-26 15:36:22] [INFO] 月度盈亏设置已更新
[2025-05-26 15:36:25] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 15:36:25] [INFO] 已加载 6 个用户
[2025-05-26 15:36:25] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 15:36:25] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-26 15:36:26] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-26 15:36:26] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 15:36:26] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-26 15:36:26] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2969.00
[2025-05-26 15:36:26] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 15:36:26] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-26 15:36:26] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3349.71
[2025-05-26 15:36:26] [INFO] 加载到 4 条每日盈亏数据
[2025-05-26 15:36:26] [INFO] 四人当月总盈亏数据: 谢羚羚=2969.00, 景丝丝=-6587.00, 钟鹮鹮=3349.71, 屈猫猫=140.00
[2025-05-26 15:36:26] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 140.00
[2025-05-26 15:36:26] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2969.00, 景丝丝=-6587.00, 钟鹮鹮=3349.71, 屈猫猫=140.00
[2025-05-26 15:36:26] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-26 15:36:26] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-26 15:36:28] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/26 17:40:47 ===
[2025-05-26 17:40:47] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 17:40:47] [INFO] 已加载 6 个用户
[2025-05-26 17:40:47] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 17:40:47] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-26 17:40:49] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-26 17:40:49] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 17:40:49] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-26 17:40:49] [INFO] 加载到 4 条每日盈亏数据
[2025-05-26 17:40:49] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 17:40:49] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2969.00
[2025-05-26 17:40:49] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-26 17:40:49] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: 140.00
[2025-05-26 17:40:49] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3349.71
[2025-05-26 17:40:49] [INFO] 四人当月总盈亏数据: 谢羚羚=2969.00, 景丝丝=-6587.00, 钟鹮鹮=3349.71, 屈猫猫=140.00
[2025-05-26 17:40:49] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=2969.00, 景丝丝=-6587.00, 钟鹮鹮=3349.71, 屈猫猫=140.00
[2025-05-26 17:40:49] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-26 17:40:49] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 17:40:49] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-26 17:40:49] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-26 17:40:49] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 17:40:49] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-26 17:40:49] [INFO] 四人当月总盈亏数据: 谢羚羚=2969.00, 景丝丝=-6587.00, 钟鹮鹮=3349.71, 屈猫猫=140.00
[2025-05-26 17:40:57] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-26 17:40:57] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 17:40:57] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-26 17:40:57] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 17:40:57] [INFO] 加载到 4 条每日盈亏数据
[2025-05-26 17:40:57] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 2969.00
[2025-05-26 17:40:57] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-26 17:40:57] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3349.71
[2025-05-26 17:40:57] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-26 17:40:57] [INFO] 四人当月总盈亏数据: 谢羚羚=2969.00, 景丝丝=-6587.00, 钟鹮鹮=3349.71, 屈猫猫=140.00
[2025-05-26 17:40:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 17:40:57] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-26 17:40:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-26 17:41:06] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/26 18:15:27 ===
[2025-05-26 18:15:27] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 18:15:27] [INFO] 已加载 6 个用户
[2025-05-26 18:15:27] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 18:15:27] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-26 18:15:39] [INFO] 月度盈亏设置已更新
[2025-05-26 18:15:40] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 18:15:40] [INFO] 已加载 6 个用户
[2025-05-26 18:15:40] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 18:15:40] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-26 18:15:43] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-26 18:15:43] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-26 18:15:50] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-26 18:15:50] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-26 18:15:51] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/27 11:54:23 ===
[2025-05-27 11:54:23] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 11:54:23] [INFO] 已加载 6 个用户
[2025-05-27 11:54:23] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 11:54:23] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-27 11:54:25] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 11:54:25] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-27 11:54:29] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 11:54:29] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-27 11:54:41] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 11:54:41] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-27 11:54:44] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 11:54:44] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-27 11:54:53] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 11:54:53] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-27 11:54:56] [INFO] 月度盈亏设置已更新
[2025-05-27 11:54:58] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 11:54:58] [INFO] 已加载 6 个用户
[2025-05-27 11:54:58] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 11:54:58] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-27 11:54:59] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-27 11:54:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 11:54:59] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-27 11:54:59] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 11:54:59] [INFO] 加载到 4 条每日盈亏数据
[2025-05-27 11:54:59] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 4788.00
[2025-05-27 11:54:59] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -6587.00
[2025-05-27 11:54:59] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 1699.71
[2025-05-27 11:54:59] [INFO] 四人当月总盈亏数据: 谢羚羚=4788.00, 景丝丝=-6587.00, 钟鹮鹮=1699.71, 屈猫猫=140.00
[2025-05-27 11:54:59] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=4788.00, 景丝丝=-6587.00, 钟鹮鹮=1699.71, 屈猫猫=140.00
[2025-05-27 11:54:59] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-27 11:54:59] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-27 11:54:59] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-27 11:54:59] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-27 11:54:59] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-27 11:54:59] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-27 11:54:59] [INFO] 四人当月总盈亏数据: 谢羚羚=4788.00, 景丝丝=-6587.00, 钟鹮鹮=1699.71, 屈猫猫=140.00
[2025-05-27 11:55:06] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/27 16:39:54 ===
[2025-05-27 16:39:54] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 16:39:54] [INFO] 已加载 6 个用户
[2025-05-27 16:39:54] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 16:39:54] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-27 16:39:56] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:39:56] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-27 16:40:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:40:08] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-27 16:40:23] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:40:23] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-27 16:40:25] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:40:25] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-27 16:40:33] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:40:33] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-27 16:40:37] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:40:37] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-27 16:40:38] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:40:38] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-27 16:40:45] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:40:45] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-27 16:41:01] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:41:01] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-27 16:41:04] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:41:04] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-27 16:41:12] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:41:12] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-27 16:41:14] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-27 16:41:14] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 16:41:14] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-27 16:41:14] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:41:14] [INFO] 加载到 4 条每日盈亏数据
[2025-05-27 16:41:14] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5117.00
[2025-05-27 16:41:14] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -5433.00
[2025-05-27 16:41:14] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -200.00
[2025-05-27 16:41:14] [INFO] 四人当月总盈亏数据: 谢羚羚=5117.00, 景丝丝=-5433.00, 钟鹮鹮=2155.71, 屈猫猫=-200.00
[2025-05-27 16:41:14] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5117.00, 景丝丝=-5433.00, 钟鹮鹮=2155.71, 屈猫猫=-200.00
[2025-05-27 16:41:14] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-27 16:41:14] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-27 16:41:14] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-27 16:41:14] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-27 16:41:14] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-27 16:41:14] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-27 16:41:14] [INFO] 四人当月总盈亏数据: 谢羚羚=5117.00, 景丝丝=-5433.00, 钟鹮鹮=2155.71, 屈猫猫=-200.00
[2025-05-27 16:41:31] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-27 16:41:31] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 16:41:31] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-27 16:41:31] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-27 16:41:31] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 2155.71
[2025-05-27 16:41:31] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -5433.00
[2025-05-27 16:41:31] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-27 16:41:31] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-27 16:41:31] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-27 16:41:31] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-27 16:41:31] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-27 16:41:31] [INFO] 四人当月总盈亏数据: 谢羚羚=5117.00, 景丝丝=-5433.00, 钟鹮鹮=2155.71, 屈猫猫=-200.00
[2025-05-27 16:41:39] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-27 16:41:39] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-27 16:41:41] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/28 11:50:18 ===
[2025-05-28 11:50:18] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-28 11:50:18] [INFO] 已加载 6 个用户
[2025-05-28 11:50:18] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-28 11:50:18] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-28 11:50:21] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/28 13:15:02 ===
[2025-05-28 13:15:02] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-28 13:15:02] [INFO] 已加载 6 个用户
[2025-05-28 13:15:02] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-28 13:15:02] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-28 13:15:08] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-28 13:15:08] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-28 13:15:10] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-28 13:15:10] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-28 13:16:11] [INFO] 月度盈亏设置已更新
[2025-05-28 13:16:15] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-28 13:16:15] [INFO] 已加载 6 个用户
[2025-05-28 13:16:15] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-28 13:16:15] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-28 13:16:19] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-28 13:16:19] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-28 13:16:22] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/28 13:21:14 ===
[2025-05-28 13:21:14] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-28 13:21:14] [INFO] 已加载 6 个用户
[2025-05-28 13:21:14] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-28 13:21:14] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-28 13:21:16] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-28 13:21:16] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-28 13:21:32] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/29 11:40:33 ===
[2025-05-29 11:40:33] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:40:33] [INFO] 已加载 6 个用户
[2025-05-29 11:40:33] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:40:33] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-29 11:40:35] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:40:35] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 11:40:37] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:40:37] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-29 11:40:40] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:40:40] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 11:40:52] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:40:52] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-29 11:41:03] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:41:03] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 11:41:05] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:41:05] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-29 11:41:16] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:41:16] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-29 11:41:44] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:41:44] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 11:41:52] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:41:52] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 11:41:55] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:41:55] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 11:41:58] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:41:58] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 11:42:03] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:42:03] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 11:42:19] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:42:19] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-29 11:42:23] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:42:23] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 11:42:24] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:42:24] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 11:42:37] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:42:37] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-29 11:42:42] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-29 11:42:42] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:42:42] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-29 11:42:42] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:42:42] [INFO] 加载到 4 条每日盈亏数据
[2025-05-29 11:42:42] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5612.00
[2025-05-29 11:42:42] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 2943.71
[2025-05-29 11:42:42] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -200.00
[2025-05-29 11:42:42] [INFO] 四人当月总盈亏数据: 谢羚羚=5612.00, 景丝丝=595.00, 钟鹮鹮=2943.71, 屈猫猫=-200.00
[2025-05-29 11:42:42] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5612.00, 景丝丝=595.00, 钟鹮鹮=2943.71, 屈猫猫=-200.00
[2025-05-29 11:42:42] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-29 11:42:42] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 11:42:42] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-29 11:42:42] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-29 11:42:42] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 11:42:42] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-29 11:42:42] [INFO] 四人当月总盈亏数据: 谢羚羚=5612.00, 景丝丝=595.00, 钟鹮鹮=2943.71, 屈猫猫=-200.00
[2025-05-29 11:42:57] [INFO] 月度盈亏设置已更新
[2025-05-29 11:43:16] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:43:16] [INFO] 已加载 6 个用户
[2025-05-29 11:43:16] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:43:16] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-29 11:43:18] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:43:18] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 11:43:20] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:43:20] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 11:43:35] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:43:35] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-29 11:43:39] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:43:39] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 11:43:43] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:43:43] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 11:44:09] [INFO] 月度盈亏设置已更新
[2025-05-29 11:44:10] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:44:10] [INFO] 已加载 6 个用户
[2025-05-29 11:44:10] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:44:10] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-29 11:44:12] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-29 11:44:12] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:44:12] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-29 11:44:12] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:44:12] [INFO] 加载到 4 条每日盈亏数据
[2025-05-29 11:44:12] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5612.00
[2025-05-29 11:44:12] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2821.00
[2025-05-29 11:44:12] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -200.00
[2025-05-29 11:44:12] [INFO] 四人当月总盈亏数据: 谢羚羚=5612.00, 景丝丝=-2821.00, 钟鹮鹮=2943.71, 屈猫猫=-200.00
[2025-05-29 11:44:12] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 2943.71
[2025-05-29 11:44:12] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 11:44:12] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-29 11:44:12] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-29 11:44:12] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 11:44:21] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/29 11:53:33 ===
[2025-05-29 11:53:33] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:53:33] [INFO] 已加载 6 个用户
[2025-05-29 11:53:33] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 11:53:33] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-29 11:53:36] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:53:36] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 11:53:37] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 11:53:37] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 11:53:42] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/29 12:45:30 ===
[2025-05-29 12:45:30] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 12:45:30] [INFO] 已加载 6 个用户
[2025-05-29 12:45:30] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 12:45:30] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-29 12:45:32] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 12:45:32] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 12:45:44] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 12:45:44] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-29 12:45:57] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 12:45:57] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-29 12:46:04] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 12:46:04] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 12:46:12] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-29 12:46:12] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 12:46:12] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-29 12:46:12] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 12:46:12] [INFO] 加载到 4 条每日盈亏数据
[2025-05-29 12:46:12] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2821.00
[2025-05-29 12:46:12] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -200.00
[2025-05-29 12:46:12] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6062.71
[2025-05-29 12:46:12] [INFO] 四人当月总盈亏数据: 谢羚羚=5612.00, 景丝丝=-2821.00, 钟鹮鹮=6062.71, 屈猫猫=-200.00
[2025-05-29 12:46:12] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5612.00, 景丝丝=-2821.00, 钟鹮鹮=6062.71, 屈猫猫=-200.00
[2025-05-29 12:46:12] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-29 12:46:12] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 12:46:12] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-29 12:46:12] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-29 12:46:12] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 12:46:12] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-29 12:46:12] [INFO] 四人当月总盈亏数据: 谢羚羚=5612.00, 景丝丝=-2821.00, 钟鹮鹮=6062.71, 屈猫猫=-200.00
[2025-05-29 12:51:46] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/29 13:06:58 ===
[2025-05-29 13:06:58] [INFO] 季度利润窗口加载 - 股票代码: 000021, 股票名称: 深科技
[2025-05-29 13:06:58] [INFO] 开始获取股票 000021 的季度利润数据
[2025-05-29 13:06:58] [INFO] 开始获取股票 000021 的季度利润数据
[2025-05-29 13:06:58] [INFO] API URL: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
[2025-05-29 13:06:58] [INFO] 发送HTTP请求...
[2025-05-29 13:06:58] [INFO] HTTP请求成功，响应长度: 1060
[2025-05-29 13:06:58] [API] API调用: http://api.mairuiapi.com/hscp/jdlr/000021/72430658081e51fc98
响应: [{"date":"2025-03-31","income":"336,463.82","expend":"302,999.03","profit":"28,045.86","totalp":"28,092.01","reprofit":"22,790.40","basege":"0.1147","ettege":"0.1147","otherp":"-2,553.17","totalcp":"20,237.23"},{"date":"2024-12-31","income":"1,482,716.65","expend":"1,347,906.19","profit":"123,715.42","totalp":"128,492.56","reprofit":"108,750.44","basege":"0.5962","ettege":"0.5962","otherp":"5,515.26","totalcp":"114,265.70"},{"date":"2024-09-30","income":"1,085,168.56","expend":"991,505.11","profit":"95,267.92","totalp":"96,803.44","reprofit":"81,211.98","basege":"0.4238","ettege":"0.4238","otherp":"3,715.15","totalcp":"84,927.13"},{"date":"2024-06-30","income":"705,456.41","expend":"652,810.88","profit":"56,153.66","totalp":"57,222.06","reprofit":"46,845.05","basege":"0.2309","ettege":"0.2309","otherp":"-14,796.33","totalcp":"32,048.72"},{"date":"2024-03-31","income":"312,637.90","expend":"293,190.19","profit":"21,811.85","totalp":"21,828.44","reprofit":"17,355.01","basege":"0.0780","ettege":"0.0780","otherp":"-11,244.20","totalcp":"6,110.81"}]
[2025-05-29 13:06:58] [INFO] 开始解析JSON数据...
[2025-05-29 13:06:58] [INFO] 成功解析为JSON数组，包含 5 个元素
[2025-05-29 13:06:58] [DEBUG] 处理项: {
  "date": "2025-03-31",
  "income": "336,463.82",
  "expend": "302,999.03",
  "profit": "28,045.86",
  "totalp": "28,092.01",
  "reprofit": "22,790.40",
  "basege": "0.1147",
  "ettege": "0.1147",
  "otherp": "-2,553.17",
  "totalcp": "20,237.23"
}
[2025-05-29 13:06:59] [DEBUG] API返回的日期字段: 2025-03-31, 类型: JValue
[2025-05-29 13:06:59] [DEBUG] 解析报告期: 原始值=2025-03-31, 格式化后=2025-03-31
[2025-05-29 13:06:59] [DEBUG] 解析字段 - 报告期: 2025-03-31, 营收: 336,464, 稀释每股收益: 0.1147, 净利润: 28,046, 综合收益总额: 28,092
[2025-05-29 13:06:59] [DEBUG] API返回的日期字段: 2024-12-31, 类型: JValue
[2025-05-29 13:06:59] [DEBUG] 处理项: {
  "date": "2024-12-31",
  "income": "1,482,716.65",
  "expend": "1,347,906.19",
  "profit": "123,715.42",
  "totalp": "128,492.56",
  "reprofit": "108,750.44",
  "basege": "0.5962",
  "ettege": "0.5962",
  "otherp": "5,515.26",
  "totalcp": "114,265.70"
}
[2025-05-29 13:06:59] [DEBUG] 解析报告期: 原始值=2024-12-31, 格式化后=2024-12-31
[2025-05-29 13:06:59] [DEBUG] API返回的日期字段: 2024-09-30, 类型: JValue
[2025-05-29 13:06:59] [DEBUG] 解析字段 - 报告期: 2024-06-30, 营收: 705,456, 稀释每股收益: 0.2309, 净利润: 56,154, 综合收益总额: 57,222
[2025-05-29 13:06:59] [INFO] 季度利润窗口加载完成
=== 日志开始于 2025/5/29 15:10:38 ===
[2025-05-29 15:10:38] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:10:38] [INFO] 已加载 6 个用户
[2025-05-29 15:10:38] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:10:38] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-29 15:10:40] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:10:40] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 15:10:43] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:10:43] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 15:10:55] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:10:55] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-29 15:26:48] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/29 15:30:52 ===
[2025-05-29 15:30:52] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:30:52] [INFO] 已加载 6 个用户
[2025-05-29 15:30:52] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:30:52] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-29 15:30:54] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:30:54] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 15:30:56] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:30:56] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 15:32:10] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:32:10] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-29 15:32:17] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:32:17] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-29 15:32:21] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:32:21] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 15:32:23] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:32:23] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-29 15:32:38] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-29 15:32:38] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:32:38] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-29 15:32:38] [INFO] 加载到 4 条每日盈亏数据
[2025-05-29 15:32:38] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:32:38] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5612.00
[2025-05-29 15:32:38] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2608.00
[2025-05-29 15:32:38] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -200.00
[2025-05-29 15:32:38] [INFO] 四人当月总盈亏数据: 谢羚羚=5612.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-200.00
[2025-05-29 15:32:38] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5612.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-200.00
[2025-05-29 15:32:38] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-29 15:32:38] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 15:32:38] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-29 15:32:38] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-29 15:32:38] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 15:32:38] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-29 15:32:38] [INFO] 四人当月总盈亏数据: 谢羚羚=5612.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-200.00
[2025-05-29 15:32:47] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:32:47] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 15:32:58] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:32:58] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-29 15:33:00] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-29 15:33:00] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:33:00] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-29 15:33:00] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:33:00] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5812.00
[2025-05-29 15:33:00] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2608.00
[2025-05-29 15:33:00] [INFO] 加载到 4 条每日盈亏数据
[2025-05-29 15:33:00] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6631.71
[2025-05-29 15:33:00] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -200.00
[2025-05-29 15:33:00] [INFO] 四人当月总盈亏数据: 谢羚羚=5812.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-200.00
[2025-05-29 15:33:00] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-29 15:33:00] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-29 15:33:00] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 15:33:14] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:33:14] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-29 15:33:15] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/29 15:57:06 ===
[2025-05-29 15:57:06] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:57:06] [INFO] 已加载 6 个用户
[2025-05-29 15:57:06] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:57:06] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-29 15:57:07] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-29 15:57:07] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 15:57:07] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-29 15:57:07] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 15:57:07] [INFO] 加载到 4 条每日盈亏数据
[2025-05-29 15:57:07] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5812.00
[2025-05-29 15:57:07] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2608.00
[2025-05-29 15:57:07] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 6631.71
[2025-05-29 15:57:07] [INFO] 四人当月总盈亏数据: 谢羚羚=5812.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-200.00
[2025-05-29 15:57:07] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5812.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-200.00
[2025-05-29 15:57:07] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-29 15:57:07] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 15:57:07] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-29 15:57:07] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-29 15:57:07] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 15:57:07] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-29 15:57:07] [INFO] 四人当月总盈亏数据: 谢羚羚=5812.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-200.00
[2025-05-29 15:57:20] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/29 16:35:39 ===
[2025-05-29 16:35:39] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 16:35:39] [INFO] 已加载 6 个用户
[2025-05-29 16:35:39] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 16:35:39] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-29 16:35:41] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 16:35:41] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-29 16:35:43] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 16:35:43] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-05-29 16:36:00] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 16:36:00] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-29 16:36:03] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-29 16:36:03] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-29 16:36:03] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-29 16:36:03] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-29 16:36:03] [INFO] 加载到 4 条每日盈亏数据
[2025-05-29 16:36:03] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2608.00
[2025-05-29 16:36:03] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -210.00
[2025-05-29 16:36:03] [INFO] 四人当月总盈亏数据: 谢羚羚=5812.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-210.00
[2025-05-29 16:36:03] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5812.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-210.00
[2025-05-29 16:36:03] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-29 16:36:03] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 16:36:03] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-29 16:36:03] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-29 16:36:03] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-29 16:36:03] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-29 16:36:03] [INFO] 四人当月总盈亏数据: 谢羚羚=5812.00, 景丝丝=-2608.00, 钟鹮鹮=6631.71, 屈猫猫=-210.00
[2025-05-29 16:36:09] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/30 12:27:05 ===
[2025-05-30 12:27:05] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 12:27:05] [INFO] 已加载 6 个用户
[2025-05-30 12:27:05] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 12:27:05] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-30 12:27:07] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:27:07] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-30 12:27:19] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:27:19] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-30 12:27:29] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:27:29] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-30 12:27:38] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:27:38] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-30 12:27:40] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:27:40] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-30 12:28:09] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:28:09] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-30 12:28:42] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:28:42] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-30 12:28:44] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:28:44] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-30 12:28:54] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:28:54] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-30 12:28:57] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-30 12:28:57] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 12:28:57] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-30 12:28:57] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:28:57] [INFO] 加载到 4 条每日盈亏数据
[2025-05-30 12:28:57] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5582.00
[2025-05-30 12:28:57] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1656.00
[2025-05-30 12:28:57] [INFO] 四人当月总盈亏数据: 谢羚羚=5582.00, 景丝丝=-1656.00, 钟鹮鹮=3393.71, 屈猫猫=-210.00
[2025-05-30 12:28:57] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5582.00, 景丝丝=-1656.00, 钟鹮鹮=3393.71, 屈猫猫=-210.00
[2025-05-30 12:28:57] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-30 12:28:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 12:28:57] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-30 12:28:57] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-30 12:28:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 12:28:57] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-30 12:28:57] [INFO] 四人当月总盈亏数据: 谢羚羚=5582.00, 景丝丝=-1656.00, 钟鹮鹮=3393.71, 屈猫猫=-210.00
[2025-05-30 12:29:00] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/30 12:38:30 ===
[2025-05-30 12:38:30] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 12:38:30] [INFO] 已加载 6 个用户
[2025-05-30 12:38:30] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 12:38:30] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-30 12:38:31] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-30 12:38:31] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 12:38:31] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-30 12:38:31] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 12:38:31] [INFO] 加载到 4 条每日盈亏数据
[2025-05-30 12:38:31] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5582.00
[2025-05-30 12:38:31] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1656.00
[2025-05-30 12:38:31] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3393.71
[2025-05-30 12:38:31] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -210.00
[2025-05-30 12:38:31] [INFO] 四人当月总盈亏数据: 谢羚羚=5582.00, 景丝丝=-1656.00, 钟鹮鹮=3393.71, 屈猫猫=-210.00
[2025-05-30 12:38:31] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5582.00, 景丝丝=-1656.00, 钟鹮鹮=3393.71, 屈猫猫=-210.00
[2025-05-30 12:38:31] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-30 12:38:31] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 12:38:31] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-30 12:38:31] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-30 12:38:31] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 12:38:31] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-30 12:38:31] [INFO] 四人当月总盈亏数据: 谢羚羚=5582.00, 景丝丝=-1656.00, 钟鹮鹮=3393.71, 屈猫猫=-210.00
[2025-05-30 12:38:44] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/30 16:01:42 ===
[2025-05-30 16:01:42] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:01:42] [INFO] 已加载 6 个用户
[2025-05-30 16:01:42] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:01:42] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-30 16:01:44] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 16:01:44] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-30 16:01:53] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 16:01:53] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-30 16:02:09] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:02:09] [INFO] 成功保存月度盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\monthly_profits.csv
[2025-05-30 16:02:10] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/30 16:16:01 ===
[2025-05-30 16:16:01] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:16:01] [INFO] 已加载 6 个用户
[2025-05-30 16:16:01] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:16:01] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-30 16:16:02] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 16:16:02] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-30 16:16:05] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 16:16:05] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-05-30 16:16:12] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 16:16:12] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-30 16:16:16] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-30 16:16:16] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:16:16] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-30 16:16:16] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 16:16:16] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5692.00
[2025-05-30 16:16:16] [INFO] 加载到 4 条每日盈亏数据
[2025-05-30 16:16:16] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1656.00
[2025-05-30 16:16:16] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3022.71
[2025-05-30 16:16:16] [INFO] 四人当月总盈亏数据: 谢羚羚=5692.00, 景丝丝=-1656.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 16:16:17] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5692.00, 景丝丝=-1656.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 16:16:17] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-30 16:16:17] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 16:16:17] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-30 16:16:17] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-30 16:16:17] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 16:16:17] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-30 16:16:17] [INFO] 四人当月总盈亏数据: 谢羚羚=5692.00, 景丝丝=-1656.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 16:16:34] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/30 16:27:27 ===
[2025-05-30 16:27:27] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:27:27] [INFO] 已加载 6 个用户
[2025-05-30 16:27:27] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:27:27] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-30 16:28:23] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/30 16:35:32 ===
[2025-05-30 16:35:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:35:32] [INFO] 已加载 6 个用户
[2025-05-30 16:35:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:35:32] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-30 16:35:33] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-30 16:35:33] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 16:35:33] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-30 16:35:33] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 16:35:33] [INFO] 加载到 4 条每日盈亏数据
[2025-05-30 16:35:33] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5692.00
[2025-05-30 16:35:33] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1656.00
[2025-05-30 16:35:33] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3022.71
[2025-05-30 16:35:33] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -210.00
[2025-05-30 16:35:33] [INFO] 四人当月总盈亏数据: 谢羚羚=5692.00, 景丝丝=-1656.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 16:35:33] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5692.00, 景丝丝=-1656.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 16:35:33] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-30 16:35:33] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 16:35:33] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-30 16:35:33] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-30 16:35:33] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 16:35:33] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-30 16:35:33] [INFO] 四人当月总盈亏数据: 谢羚羚=5692.00, 景丝丝=-1656.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 17:01:30] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/30 17:07:11 ===
[2025-05-30 17:07:11] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 17:07:11] [INFO] 已加载 6 个用户
[2025-05-30 17:07:11] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 17:07:11] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-30 17:07:14] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-30 17:07:14] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 17:07:14] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-30 17:07:14] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 17:07:14] [INFO] 加载到 4 条每日盈亏数据
[2025-05-30 17:07:14] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5692.00
[2025-05-30 17:07:14] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -1656.00
[2025-05-30 17:07:14] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 5 盈亏值: -210.00
[2025-05-30 17:07:14] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3022.71
[2025-05-30 17:07:14] [INFO] 四人当月总盈亏数据: 谢羚羚=5692.00, 景丝丝=-1656.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 17:07:14] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5692.00, 景丝丝=-1656.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 17:07:14] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-30 17:07:14] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 17:07:14] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-30 17:07:14] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-30 17:07:14] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 17:07:14] [INFO] 四人当月总盈亏数据: 谢羚羚=5692.00, 景丝丝=-1656.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 17:07:53] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/5/30 17:55:32 ===
[2025-05-30 17:55:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 17:55:32] [INFO] 已加载 6 个用户
[2025-05-30 17:55:32] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 17:55:32] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-05-30 17:55:35] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 17:55:35] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-05-30 17:55:38] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 17:55:38] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-05-30 17:55:55] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 17:55:55] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-05-30 17:55:57] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=5
[2025-05-30 17:55:57] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-05-30 17:55:57] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=5
[2025-05-30 17:55:57] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-05-30 17:55:57] [INFO] 加载到 4 条每日盈亏数据
[2025-05-30 17:55:57] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 5 盈亏值: 5692.00
[2025-05-30 17:55:57] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 5 盈亏值: -2299.00
[2025-05-30 17:55:57] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 5 盈亏值: 3022.71
[2025-05-30 17:55:57] [INFO] 四人当月总盈亏数据: 谢羚羚=5692.00, 景丝丝=-2299.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 17:55:57] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=5692.00, 景丝丝=-2299.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 17:55:57] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-05-30 17:55:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 17:55:57] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-05-30 17:55:57] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-05-30 17:55:57] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-05-30 17:55:57] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-05-30 17:55:57] [INFO] 四人当月总盈亏数据: 谢羚羚=5692.00, 景丝丝=-2299.00, 钟鹮鹮=3022.71, 屈猫猫=-210.00
[2025-05-30 17:56:12] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/3 15:12:23 ===
[2025-06-03 15:12:23] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-03 15:12:23] [INFO] 已加载 6 个用户
[2025-06-03 15:12:23] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-03 15:12:23] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-03 15:12:25] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-06-03 15:12:25] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-03 15:12:25] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-03 15:12:38] [INFO] 成功加载每日盈亏数据，共 4 条记录
[2025-06-03 15:12:38] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-03 15:12:42] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-03 15:12:42] [INFO] 已加载用户 谢羚羚 的月度盈亏数据
[2025-06-03 15:12:44] [INFO] 成功加载每日盈亏数据，共 5 条记录
[2025-06-03 15:12:44] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-03 15:12:47] [INFO] 成功加载每日盈亏数据，共 5 条记录
[2025-06-03 15:12:47] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-03 15:12:47] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-06-03 15:12:55] [INFO] 成功加载每日盈亏数据，共 5 条记录
[2025-06-03 15:12:55] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-03 15:12:59] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=6
[2025-06-03 15:12:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-03 15:12:59] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=6
[2025-06-03 15:12:59] [INFO] 成功加载每日盈亏数据，共 6 条记录
[2025-06-03 15:12:59] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 6 盈亏值: 40.00
[2025-06-03 15:12:59] [INFO] 加载到 6 条每日盈亏数据
[2025-06-03 15:12:59] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 6 盈亏值: -130.00
[2025-06-03 15:12:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-03 15:12:59] [INFO] 未找到用户 钟鹮鹮 年份 2025 月份 6 的每日盈亏数据
[2025-06-03 15:12:59] [INFO] 从月度盈亏数据获取: 用户 钟鹮鹮 月份 6 盈亏值: -8830
[2025-06-03 15:12:59] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-03 15:12:59] [INFO] 四人当月总盈亏数据: 谢羚羚=40.00, 景丝丝=-130.00, 钟鹮鹮=-8830, 屈猫猫=0
[2025-06-03 15:12:59] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=40.00, 景丝丝=-130.00, 钟鹮鹮=-8830, 屈猫猫=0
[2025-06-03 15:12:59] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-06-03 15:12:59] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-03 15:12:59] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-06-03 15:12:59] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-06-03 15:12:59] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-03 15:12:59] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-06-03 15:12:59] [INFO] 四人当月总盈亏数据: 谢羚羚=40.00, 景丝丝=-130.00, 钟鹮鹮=-8830, 屈猫猫=0
[2025-06-03 15:13:08] [INFO] 成功加载每日盈亏数据，共 6 条记录
[2025-06-03 15:13:08] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-03 15:13:10] [INFO] 成功加载每日盈亏数据，共 6 条记录
[2025-06-03 15:13:10] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-03 15:13:10] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-03 15:36:22] [INFO] 成功加载每日盈亏数据，共 6 条记录
[2025-06-03 15:36:22] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-03 15:36:24] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=6
[2025-06-03 15:36:24] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-03 15:36:24] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=6
[2025-06-03 15:36:24] [INFO] 成功加载每日盈亏数据，共 7 条记录
[2025-06-03 15:36:24] [INFO] 加载到 7 条每日盈亏数据
[2025-06-03 15:36:24] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 6 盈亏值: -130.00
[2025-06-03 15:36:24] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 6 盈亏值: 40.00
[2025-06-03 15:36:24] [INFO] 从月度盈亏数据获取: 用户 屈猫猫 月份 6 盈亏值: 0
[2025-06-03 15:36:24] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=40.00, 景丝丝=-130.00, 钟鹮鹮=-22.00, 屈猫猫=0
[2025-06-03 15:36:24] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-06-03 15:36:24] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-03 15:36:24] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-06-03 15:36:24] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-06-03 15:36:24] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-06-03 15:36:24] [INFO] 四人当月总盈亏数据: 谢羚羚=40.00, 景丝丝=-130.00, 钟鹮鹮=-22.00, 屈猫猫=0
[2025-06-03 15:36:42] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/4 12:10:43 ===
[2025-06-04 12:10:43] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-04 12:10:43] [INFO] 已加载 6 个用户
[2025-06-04 12:10:43] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-04 12:10:43] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-04 12:10:45] [INFO] 成功加载每日盈亏数据，共 7 条记录
[2025-06-04 12:10:45] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-04 12:10:57] [INFO] 成功加载每日盈亏数据，共 7 条记录
[2025-06-04 12:10:57] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 12:11:50] [INFO] 成功加载每日盈亏数据，共 7 条记录
[2025-06-04 12:11:50] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-04 12:11:52] [INFO] 成功加载每日盈亏数据，共 7 条记录
[2025-06-04 12:11:52] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 12:11:52] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-06-04 12:12:13] [INFO] 成功加载每日盈亏数据，共 7 条记录
[2025-06-04 12:12:13] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 12:12:16] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:12:16] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-04 12:12:18] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:12:18] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-06-04 12:12:29] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:12:29] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 12:12:36] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:12:36] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-04 12:12:38] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:12:38] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-04 12:12:45] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:12:45] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 12:12:57] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:12:57] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-04 12:12:59] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:12:59] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-06-04 12:13:06] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:13:06] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 12:13:09] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=6
[2025-06-04 12:13:09] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-04 12:13:09] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=6
[2025-06-04 12:13:09] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 12:13:09] [INFO] 加载到 8 条每日盈亏数据
[2025-06-04 12:13:09] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 6 盈亏值: 1051.00
[2025-06-04 12:13:09] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 6 盈亏值: 120.00
[2025-06-04 12:13:09] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 6 盈亏值: 2672.00
[2025-06-04 12:13:09] [INFO] 四人当月总盈亏数据: 谢羚羚=170.00, 景丝丝=1051.00, 钟鹮鹮=2672.00, 屈猫猫=120.00
[2025-06-04 12:13:09] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=170.00, 景丝丝=1051.00, 钟鹮鹮=2672.00, 屈猫猫=120.00
[2025-06-04 12:13:09] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-06-04 12:13:09] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-04 12:13:09] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-06-04 12:13:09] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-06-04 12:13:09] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-04 12:13:09] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-06-04 12:13:09] [INFO] 四人当月总盈亏数据: 谢羚羚=170.00, 景丝丝=1051.00, 钟鹮鹮=2672.00, 屈猫猫=120.00
[2025-06-04 12:14:41] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/4 17:28:18 ===
[2025-06-04 17:28:18] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-04 17:28:18] [INFO] 已加载 6 个用户
[2025-06-04 17:28:18] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-04 17:28:18] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-04 17:28:20] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:28:20] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-04 17:28:32] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:28:32] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 17:28:35] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:28:35] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-04 17:28:42] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:28:42] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-06-04 17:28:51] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:28:51] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 17:28:56] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:28:56] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-04 17:28:58] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:28:58] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-06-04 17:29:07] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:29:07] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 17:29:10] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:29:10] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-04 17:29:13] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:29:13] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-04 17:29:23] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:29:23] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-04 17:29:26] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=6
[2025-06-04 17:29:26] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-04 17:29:26] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=6
[2025-06-04 17:29:26] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-04 17:29:26] [INFO] 加载到 8 条每日盈亏数据
[2025-06-04 17:29:26] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 6 盈亏值: 200.00
[2025-06-04 17:29:26] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 6 盈亏值: -100.00
[2025-06-04 17:29:26] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 6 盈亏值: 363.00
[2025-06-04 17:29:26] [INFO] 四人当月总盈亏数据: 谢羚羚=200.00, 景丝丝=363.00, 钟鹮鹮=2586.00, 屈猫猫=-100.00
[2025-06-04 17:29:26] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=200.00, 景丝丝=363.00, 钟鹮鹮=2586.00, 屈猫猫=-100.00
[2025-06-04 17:29:26] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-06-04 17:29:26] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-04 17:29:26] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-06-04 17:29:26] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-06-04 17:29:26] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-04 17:29:26] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-06-04 17:29:26] [INFO] 四人当月总盈亏数据: 谢羚羚=200.00, 景丝丝=363.00, 钟鹮鹮=2586.00, 屈猫猫=-100.00
[2025-06-04 17:30:46] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/5 15:06:49 ===
[2025-06-05 15:06:49] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-05 15:06:49] [INFO] 已加载 6 个用户
[2025-06-05 15:06:49] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-05 15:06:49] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-05 15:06:51] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 15:06:51] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-05 15:07:12] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 15:07:12] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-05 15:07:20] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 15:07:20] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-05 15:07:24] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 15:07:24] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-05 15:07:36] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 15:07:36] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-05 15:07:40] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 15:07:40] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-05 15:07:45] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 15:07:45] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-06-05 15:07:50] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 15:07:50] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-05 15:07:54] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=6
[2025-06-05 15:07:54] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-05 15:07:54] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=6
[2025-06-05 15:07:54] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 15:07:54] [INFO] 加载到 8 条每日盈亏数据
[2025-06-05 15:07:54] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 6 盈亏值: -620.00
[2025-06-05 15:07:54] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 6 盈亏值: 1662.00
[2025-06-05 15:07:54] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 6 盈亏值: 4877.00
[2025-06-05 15:07:54] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 6 盈亏值: -100.00
[2025-06-05 15:07:54] [INFO] 四人当月总盈亏数据: 谢羚羚=-620.00, 景丝丝=1662.00, 钟鹮鹮=4877.00, 屈猫猫=-100.00
[2025-06-05 15:07:54] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=-620.00, 景丝丝=1662.00, 钟鹮鹮=4877.00, 屈猫猫=-100.00
[2025-06-05 15:07:54] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-06-05 15:07:54] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-05 15:07:54] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-06-05 15:07:54] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-06-05 15:07:54] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-05 15:07:54] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-06-05 15:07:54] [INFO] 四人当月总盈亏数据: 谢羚羚=-620.00, 景丝丝=1662.00, 钟鹮鹮=4877.00, 屈猫猫=-100.00
[2025-06-05 15:08:55] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/5 17:44:49 ===
[2025-06-05 17:44:49] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-05 17:44:49] [INFO] 已加载 6 个用户
[2025-06-05 17:44:49] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-05 17:44:49] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-05 17:44:50] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 17:44:50] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-05 17:44:53] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 17:44:53] [INFO] 已加载用户 屈猫猫 的每日盈亏数据
[2025-06-05 17:45:06] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 17:45:06] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-05 17:45:08] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=6
[2025-06-05 17:45:08] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-05 17:45:08] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=6
[2025-06-05 17:45:08] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-05 17:45:08] [INFO] 加载到 8 条每日盈亏数据
[2025-06-05 17:45:08] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 6 盈亏值: 1662.00
[2025-06-05 17:45:08] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 6 盈亏值: 4877.00
[2025-06-05 17:45:08] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 6 盈亏值: -250.00
[2025-06-05 17:45:08] [INFO] 四人当月总盈亏数据: 谢羚羚=-620.00, 景丝丝=1662.00, 钟鹮鹮=4877.00, 屈猫猫=-250.00
[2025-06-05 17:45:08] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=-620.00, 景丝丝=1662.00, 钟鹮鹮=4877.00, 屈猫猫=-250.00
[2025-06-05 17:45:08] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-06-05 17:45:08] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-05 17:45:08] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-06-05 17:45:08] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-06-05 17:45:08] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-05 17:45:08] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-06-05 17:45:08] [INFO] 四人当月总盈亏数据: 谢羚羚=-620.00, 景丝丝=1662.00, 钟鹮鹮=4877.00, 屈猫猫=-250.00
[2025-06-05 17:45:50] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/6 11:52:58 ===
[2025-06-06 11:52:58] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 11:52:58] [INFO] 已加载 6 个用户
[2025-06-06 11:52:58] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 11:52:58] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-06 11:52:59] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 11:52:59] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-06 11:53:19] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 11:53:19] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-06 11:53:21] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 11:53:21] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-06 11:53:23] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 11:53:23] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-06 11:53:31] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 11:53:31] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-06 11:53:35] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=6
[2025-06-06 11:53:35] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 11:53:35] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=6
[2025-06-06 11:53:35] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 11:53:35] [INFO] 加载到 8 条每日盈亏数据
[2025-06-06 11:53:35] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 6 盈亏值: -840.00
[2025-06-06 11:53:35] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 6 盈亏值: 1662.00
[2025-06-06 11:53:35] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 6 盈亏值: -250.00
[2025-06-06 11:53:35] [INFO] 四人当月总盈亏数据: 谢羚羚=-840.00, 景丝丝=1662.00, 钟鹮鹮=5457.00, 屈猫猫=-250.00
[2025-06-06 11:53:35] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=-840.00, 景丝丝=1662.00, 钟鹮鹮=5457.00, 屈猫猫=-250.00
[2025-06-06 11:53:35] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-06-06 11:53:35] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-06 11:53:35] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-06-06 11:53:35] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-06-06 11:53:35] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-06 11:53:35] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-06-06 11:53:35] [INFO] 四人当月总盈亏数据: 谢羚羚=-840.00, 景丝丝=1662.00, 钟鹮鹮=5457.00, 屈猫猫=-250.00
[2025-06-06 11:53:57] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/6 12:41:15 ===
[2025-06-06 12:41:15] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 12:41:15] [INFO] 已加载 6 个用户
[2025-06-06 12:41:15] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 12:41:15] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-06 12:41:17] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 12:41:17] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-06 12:41:19] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 12:41:19] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-06-06 12:41:29] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 12:41:29] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-06 12:41:31] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=6
[2025-06-06 12:41:31] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 12:41:31] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=6
[2025-06-06 12:41:31] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 12:41:31] [INFO] 加载到 8 条每日盈亏数据
[2025-06-06 12:41:31] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 6 盈亏值: -840.00
[2025-06-06 12:41:31] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 6 盈亏值: 5457.00
[2025-06-06 12:41:31] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 6 盈亏值: 3458.00
[2025-06-06 12:41:31] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 6 盈亏值: -250.00
[2025-06-06 12:41:31] [INFO] 四人当月总盈亏数据: 谢羚羚=-840.00, 景丝丝=3458.00, 钟鹮鹮=5457.00, 屈猫猫=-250.00
[2025-06-06 12:41:31] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=-840.00, 景丝丝=3458.00, 钟鹮鹮=5457.00, 屈猫猫=-250.00
[2025-06-06 12:41:31] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-06-06 12:41:31] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-06 12:41:31] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-06-06 12:41:31] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-06-06 12:41:31] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-06 12:41:31] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-06-06 12:41:31] [INFO] 四人当月总盈亏数据: 谢羚羚=-840.00, 景丝丝=3458.00, 钟鹮鹮=5457.00, 屈猫猫=-250.00
[2025-06-06 12:43:08] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/6 15:05:24 ===
[2025-06-06 15:05:24] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 15:05:24] [INFO] 已加载 6 个用户
[2025-06-06 15:05:24] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 15:05:24] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-06 15:05:25] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 15:05:25] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-06 15:05:35] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 15:05:35] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-06 15:05:38] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/6 16:58:44 ===
[2025-06-06 16:58:44] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 16:58:44] [INFO] 已加载 6 个用户
[2025-06-06 16:58:44] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 16:58:44] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-06 16:58:49] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 16:58:49] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-06 16:58:58] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 16:58:58] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-06 16:59:11] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 16:59:11] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-06 16:59:13] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 16:59:13] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-06 16:59:15] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 16:59:15] [INFO] 已加载用户 景丝丝 的每日盈亏数据
[2025-06-06 16:59:26] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 16:59:26] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-06 16:59:29] [INFO] 加载统计数据: 用户=xiekun, 年份=2025, 月份=6
[2025-06-06 16:59:29] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-06 16:59:29] [INFO] 从每日盈亏数据加载四人当月总盈亏数据: 年份=2025, 月份=6
[2025-06-06 16:59:29] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-06 16:59:29] [INFO] 加载到 8 条每日盈亏数据
[2025-06-06 16:59:29] [INFO] 从每日盈亏数据计算: 用户 谢羚羚 年份 2025 月份 6 盈亏值: -720.00
[2025-06-06 16:59:29] [INFO] 从每日盈亏数据计算: 用户 钟鹮鹮 年份 2025 月份 6 盈亏值: 5125.00
[2025-06-06 16:59:29] [INFO] 从每日盈亏数据计算: 用户 景丝丝 年份 2025 月份 6 盈亏值: 2991.00
[2025-06-06 16:59:29] [INFO] 从每日盈亏数据计算: 用户 屈猫猫 年份 2025 月份 6 盈亏值: -250.00
[2025-06-06 16:59:29] [INFO] 四人当月总盈亏数据: 谢羚羚=-720.00, 景丝丝=2991.00, 钟鹮鹮=5125.00, 屈猫猫=-250.00
[2025-06-06 16:59:29] [INFO] 绘制四人当月总盈亏图表: 用户数=4, 数据=谢羚羚=-720.00, 景丝丝=2991.00, 钟鹮鹮=5125.00, 屈猫猫=-250.00
[2025-06-06 16:59:29] [INFO] 加载用户 xiekun 的多年汇总数据
[2025-06-06 16:59:29] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-06 16:59:29] [INFO] 成功计算用户 xiekun 的汇总数据: 总盈利=103720.65, 总转入=124899.00, 总转出=144392.6, 净转入转出=-19493.60
[2025-06-06 16:59:29] [INFO] 成功显示用户 xiekun 的多年汇总数据
[2025-06-06 16:59:29] [INFO] 成功加载年度盈亏数据，共 10 条记录
[2025-06-06 16:59:29] [INFO] 加载年度盈亏数据: 用户=xiekun, 年份数量=9
[2025-06-06 16:59:29] [INFO] 四人当月总盈亏数据: 谢羚羚=-720.00, 景丝丝=2991.00, 钟鹮鹮=5125.00, 屈猫猫=-250.00
[2025-06-06 17:00:18] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/9 11:44:27 ===
[2025-06-09 11:44:27] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-09 11:44:27] [INFO] 已加载 6 个用户
[2025-06-09 11:44:27] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-09 11:44:27] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-09 11:44:29] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-09 11:44:29] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-09 11:44:31] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-09 11:44:31] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-09 11:44:52] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-09 11:44:52] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-09 11:44:52] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-09 11:44:56] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-09 11:44:56] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-09 11:45:15] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-09 11:45:15] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-09 11:45:15] [INFO] 已加载用户 钟鹮鹮 的每日盈亏数据
[2025-06-09 11:45:19] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-09 11:45:19] [INFO] 成功保存每日盈亏数据到 D:\source\gupiao\SimpleStock\StockCrawler\bin\Debug\net9.0-windows\daily_profits.csv
[2025-06-09 11:45:19] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-09 11:45:21] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-09 11:45:21] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-09 11:45:32] [INFO] 月度盈亏设置已更新
=== 日志开始于 2025/6/9 11:50:43 ===
[2025-06-09 11:50:43] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-09 11:50:43] [INFO] 已加载 6 个用户
[2025-06-09 11:50:43] [INFO] 成功加载月度盈亏数据，共 6 条记录
[2025-06-09 11:50:43] [INFO] 已加载用户 xiekun 的月度盈亏数据
[2025-06-09 11:50:44] [INFO] 成功加载每日盈亏数据，共 8 条记录
[2025-06-09 11:50:44] [INFO] 已加载用户 谢羚羚 的每日盈亏数据
[2025-06-09 11:51:08] [INFO] 月度盈亏设置已更新
