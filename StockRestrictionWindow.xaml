<Window x:Class="StockCrawler.StockRestrictionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="股票解禁限售信息" Height="450" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="#F8F9FA" FontFamily="Microsoft YaHei"
        WindowStyle="None" AllowsTransparency="True"
        BorderThickness="1" BorderBrush="#E0E0E0"
        ResizeMode="CanResizeWithGrip">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>

    <Window.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#8C8C8C"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F5F5F5"/>
                    <Setter Property="Foreground" Value="#FF4D4F"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#E8E8E8"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Background="#1890FF" Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="{Binding WindowTitle}" FontSize="16" FontWeight="Bold" Foreground="White" Margin="16,12"/>

                <Button Grid.Column="1" Style="{StaticResource CloseButtonStyle}" Click="CloseButton_Click" Margin="8,8,16,8">
                    <Path x:Name="CloseIcon" Data="M0,0 L10,10 M0,10 L10,0" Stroke="White" StrokeThickness="1.5" Width="10" Height="10"/>
                </Button>
            </Grid>
        </Border>

        <!-- 内容区域 -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 股票信息 -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                <TextBlock Text="{Binding StockName}" FontSize="18" FontWeight="Bold"/>
                <TextBlock Text="{Binding StockCode}" FontSize="14" Foreground="#8C8C8C" Margin="8,0,0,0" VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 解禁限售数据表格 -->
            <DataGrid Grid.Row="1" x:Name="RestrictionDataGrid"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      BorderThickness="1"
                      BorderBrush="#E8E8E8"
                      Background="White"
                      RowBackground="White"
                      AlternatingRowBackground="#F9F9F9"
                      CanUserResizeRows="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto">
                <DataGrid.Resources>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#F0F0F0"/>
                        <Setter Property="Padding" Value="10,8"/>
                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    </Style>
                    <Style TargetType="DataGridCell">
                        <Setter Property="Padding" Value="10,6"/>
                        <Setter Property="BorderBrush" Value="#E8E8E8"/>
                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                        <Setter Property="HorizontalAlignment" Value="Center"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="DataGridCell">
                                    <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                                        <ContentPresenter Margin="{TemplateBinding Padding}" HorizontalAlignment="{TemplateBinding HorizontalAlignment}"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </DataGrid.Resources>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="解禁日期" Binding="{Binding FormattedReleaseDate}" Width="100"/>
                    <DataGridTextColumn Header="解禁数量" Binding="{Binding FormattedReleaseAmount}" Width="120"/>
                    <DataGridTextColumn Header="解禁市值" Binding="{Binding FormattedReleaseMarketValue}" Width="120"/>
                    <DataGridTextColumn Header="上市批次" Binding="{Binding Batch}" Width="80"/>
                    <DataGridTextColumn Header="公告日期" Binding="{Binding FormattedAnnouncementDate}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- 提示信息 -->
            <TextBlock Grid.Row="2" x:Name="InfoTextBlock" Text="注：仅显示当前日期之后的解禁限售信息" Foreground="#8C8C8C" Margin="0,16,0,0"/>
        </Grid>
    </Grid>
</Window>
