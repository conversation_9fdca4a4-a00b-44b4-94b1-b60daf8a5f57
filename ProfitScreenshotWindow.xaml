<Window x:Class="StockCrawler.ProfitScreenshotWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:StockCrawler"
        mc:Ignorable="d"
        Title="当日总盈亏" Height="200" Width="320" WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize" ShowInTaskbar="False" Topmost="True" Loaded="Window_Loaded"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/icons100.png"/>
    </Window.Icon>
    <Border Background="#F9FFF9" BorderBrush="#E8E8E8" BorderThickness="1" CornerRadius="8"
            Padding="20" Margin="10">
        <Border.Effect>
            <DropShadowEffect BlurRadius="10" ShadowDepth="2" Opacity="0.2" Color="#000000"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 标题 -->
            <TextBlock Grid.Row="0" Text="投资组合总览" FontSize="16" FontWeight="SemiBold"
                       HorizontalAlignment="Center" Margin="0,0,0,15"/>

            <!-- 总盈亏信息 -->
            <Border Grid.Row="1" x:Name="ProfitInfoBorder"
                   Background="#F0FFF0" BorderBrush="#B7EB8F" BorderThickness="1"
                   HorizontalAlignment="Center" CornerRadius="6">
                <StackPanel Orientation="Horizontal" Margin="15,8">
                    <TextBlock Text="当日总盈亏: " FontSize="18" VerticalAlignment="Center"/>
                    <TextBlock x:Name="ProfitValueTextBlock" Text="0.00" FontSize="18"
                               FontWeight="Bold" VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- 提示信息 -->
            <TextBlock Grid.Row="2" Text="截图已自动复制到剪贴板，可直接粘贴使用"
                       FontSize="12" Foreground="#595959" HorizontalAlignment="Center"
                       VerticalAlignment="Bottom" Margin="0,15,0,0"/>
        </Grid>
    </Border>
</Window>
