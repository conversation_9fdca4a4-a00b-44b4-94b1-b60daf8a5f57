using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using StockCrawler.Models;
using StockCrawler.Utils;

namespace StockCrawler.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private ObservableCollection<Stock> _stocks;
        private bool _isLoading;
        private string _statusMessage;

        public ObservableCollection<Stock> Stocks
        {
            get => _stocks;
            set
            {
                _stocks = value;
                OnPropertyChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public ICommand CaptureScreenshotCommand { get; }

        public MainViewModel()
        {
            Stocks = new ObservableCollection<Stock>();
            CaptureScreenshotCommand = new RelayCommand<FrameworkElement>(CaptureScreenshot);
        }

        private void CaptureScreenshot(FrameworkElement element)
        {
            if (element == null)
                return;

            // 捕获截图并复制到剪贴板
            bool success = ScreenshotHelper.CaptureElementToClipboard(element);

            // 显示反馈
            if (success)
            {
                // 获取股票信息
                var stock = element.Tag as Stock;
                string stockInfo = stock != null ? $"{stock.Name} ({stock.Code})" : "当前股票";
                
                // 显示提示
                MessageBox.Show($"{stockInfo}的盈亏信息已复制到剪贴板，可以粘贴使用。", "截图成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("截图失败，请稍后重试。", "截图失败", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // 简单的命令实现
    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Predicate<T> _canExecute;

        public RelayCommand(Action<T> execute, Predicate<T> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public bool CanExecute(object parameter)
        {
            return _canExecute == null || _canExecute((T)parameter);
        }

        public void Execute(object parameter)
        {
            _execute((T)parameter);
        }

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }
    }
}
